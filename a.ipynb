{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from typing import Dict, List, Tuple, Any\n", "import os\n", "import sys\n", "import torch\n", "from loguru import logger\n", "\n", "import seaborn as sns\n", "\n", "sns.set_style(\"whitegrid\")\n", "sns.set_palette(\"husl\")\n", "\n", "\n", "# Add the package to path for imports\n", "\n", "from llm_modeling_metrics import ModelFactory\n", "from llm_modeling_metrics.core.operators import (\n", "    HardwareSpecs,\n", "    AttentionOperator,\n", "    MLAAttentionOperator,\n", ")"]}, {"cell_type": "code", "execution_count": 2, "id": "1", "metadata": {}, "outputs": [], "source": ["# models = {}\n", "\n", "# # DeepSeek V3 with <PERSON> At<PERSON>tion\n", "# print(\"Loading DeepSeek V3 model...\")\n", "# models[\"DSv3_MLA\"] = {\n", "#     \"model\": ModelFactory.create_model(\"deepseek-ai/DeepSeek-V3\"),\n", "#     \"name\": \"DSv3, 8K-32K\",\n", "#     \"color\": \"blue\",\n", "#     \"marker\": \"o\",\n", "#     \"scale_factor\": 1.0,  # Use realistic values\n", "# }\n", "\n", "# Qwen3 MoE (Qwen/Qwen3-235B-A22B)\n", "# print(\"Loading Qwen3 MoE model...\")\n", "# models[\"Qwen3_MoE\"] = {\n", "#     \"model\": ModelFactory.create_model(\"Qwen/Qwen3-235B-A22B\"),\n", "#     \"name\": \"Qwen3 MoE, 8K-32K\",\n", "#     \"color\": \"blue\",\n", "#     \"marker\": \"o\",\n", "#     \"scale_factor\": 1.0,\n", "# }\n", "\n", "# print(\"Loading Step-3 representative model...\")\n", "# models[\"Step3_MFA\"] = {\n", "#     \"model\": ModelFactory.create_model(\"stepfun-ai/step3\"),\n", "#     \"name\": \"Step-3, 8K-32K\",\n", "#     \"color\": \"red\",\n", "#     \"marker\": \"*\",\n", "#     \"scale_factor\": 1.0,\n", "# }"]}, {"cell_type": "code", "execution_count": 3, "id": "2", "metadata": {}, "outputs": [], "source": ["model = ModelFactory.create_model(\"deepseek-ai/DeepSeek-V3\")"]}, {"cell_type": "code", "execution_count": 4, "id": "3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mDEBUG\u001b[0m \u001b[36mmoe_model.py\u001b[0m:\u001b[37m489\u001b[0m \u001b[34m\u001b[1mShared experts: 1, Routed experts: 256,             Experts per token: num_experts_per_tok=8 num_moe_layers=58 num_dense_layers=3\u001b[0m\n", "\u001b[34m\u001b[1mDEBUG\u001b[0m \u001b[36mmoe_model.py\u001b[0m:\u001b[37m493\u001b[0m \u001b[34m\u001b[1mattention_params/1e9=11.669 embedding_params/1e9=1.853 dense_mlp_params/1e9=1.189 moe_active_params/1e9=23.095\u001b[0m\n"]}, {"data": {"text/plain": ["37.807128576"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["model.compute_active_params_per_token() / 1e9"]}, {"cell_type": "code", "execution_count": 5, "id": "4", "metadata": {}, "outputs": [], "source": ["from transformers import AutoConfig, AutoModelForCausalLM"]}, {"cell_type": "code", "execution_count": 6, "id": "5", "metadata": {}, "outputs": [], "source": ["config = AutoConfig.from_pretrained(\"Qwen/Qwen3-235B-A22B\", trust_remote_code=True)\n", "config.num_hidden_layers = 1"]}, {"cell_type": "code", "execution_count": 7, "id": "6", "metadata": {}, "outputs": [], "source": ["from torch import nn"]}, {"cell_type": "code", "execution_count": 8, "id": "7", "metadata": {}, "outputs": [], "source": ["# config = AutoConfig.from_pretrained(\"openai-community/gpt2\", trust_remote_code=True)\n", "# config.num_hidden_layers = 4\n", "# model = AutoModelForCausalLM.from_config(config)\n", "# print(model)"]}, {"cell_type": "code", "execution_count": 9, "id": "8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mDEBUG\u001b[0m \u001b[36mmoe_model.py\u001b[0m:\u001b[37m489\u001b[0m \u001b[34m\u001b[1mShared experts: 1, Routed experts: 256,             Experts per token: num_experts_per_tok=8 num_moe_layers=58 num_dense_layers=3\u001b[0m\n", "\u001b[34m\u001b[1mDEBUG\u001b[0m \u001b[36mmoe_model.py\u001b[0m:\u001b[37m493\u001b[0m \u001b[34m\u001b[1mattention_params/1e9=11.669 embedding_params/1e9=1.853 dense_mlp_params/1e9=1.189 moe_active_params/1e9=23.095\u001b[0m\n"]}], "source": ["model = ModelFactory.create_model(\"deepseek-ai/DeepSeek-V3\")\n", "active_params = model.compute_active_params_per_token()"]}, {"cell_type": "code", "execution_count": 10, "id": "9", "metadata": {}, "outputs": [], "source": ["# model = ModelFactory.create_model(\"moonshotai/Kimi-K2-Instruct\")\n", "# active_params = model.compute_active_params_per_token()\n", "# active_params / 1e9"]}, {"cell_type": "code", "execution_count": 11, "id": "10", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'vocab_size': 129280, 'max_position_embeddings': 163840, 'hidden_size': 7168, 'intermediate_size': 18432, 'moe_intermediate_size': 2048, 'num_hidden_layers': 61, 'num_nextn_predict_layers': 1, 'num_attention_heads': 128, 'n_shared_experts': 1, 'n_routed_experts': 256, 'ep_size': 1, 'routed_scaling_factor': 2.5, 'kv_lora_rank': 512, 'q_lora_rank': 1536, 'qk_rope_head_dim': 64, 'v_head_dim': 128, 'qk_nope_head_dim': 128, 'topk_method': 'noaux_tc', 'n_group': 8, 'topk_group': 4, 'num_experts_per_tok': 8, 'moe_layer_freq': 1, 'first_k_dense_replace': 3, 'norm_topk_prob': True, 'scoring_func': 'sigmoid', 'num_key_value_heads': 128, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-06, 'use_cache': True, 'rope_theta': 10000, 'rope_scaling': {'beta_fast': 32, 'beta_slow': 1, 'factor': 40, 'mscale': 1.0, 'mscale_all_dim': 1.0, 'original_max_position_embeddings': 4096, 'type': 'yarn'}, 'attention_bias': False, 'attention_dropout': 0.0, 'return_dict': True, 'output_hidden_states': False, 'torchscript': False, 'torch_dtype': 'bfloat16', 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'architectures': ['DeepseekV3ForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'task_specific_params': None, 'problem_type': None, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 0, 'pad_token_id': None, 'eos_token_id': 1, 'sep_token_id': None, 'decoder_start_token_id': None, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, '_name_or_path': 'deepseek-ai/DeepSeek-V3', 'transformers_version': '4.55.0', 'auto_map': {'AutoConfig': 'configuration_deepseek.DeepseekV3Config', 'AutoModel': 'modeling_deepseek.DeepseekV3Model', 'AutoModelForCausalLM': 'modeling_deepseek.DeepseekV3ForCausalLM'}, 'model_type': 'deepseek_v3', 'quantization_config': {'activation_scheme': 'dynamic', 'fmt': 'e4m3', 'quant_method': 'fp8', 'weight_block_size': [128, 128]}, 'tf_legacy_loss': False, 'use_bfloat16': False, 'output_attentions': False}\n", "Model: deepseek-ai/DeepSeek-V3\n", " hidden_size: 7168\n", " num_layers: 61\n", " num_heads: 128\n", " intermediate_size: 18432\n", " vocab_size: 129280\n", " experts_per_token: 8\n", " shared_experts: 1\n", " routed_experts: 256\n"]}], "source": ["print(model.config)\n", "print(model)"]}, {"cell_type": "code", "execution_count": 12, "id": "1dbb7f6a", "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["model.config['num_experts_per_tok']"]}, {"cell_type": "code", "execution_count": 13, "id": "33948d6f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1mINFO\u001b[0m \u001b[36m1782711339.py\u001b[0m:\u001b[37m5\u001b[0m \u001b[1mexpert_bs=32.0\u001b[0m\n"]}, {"data": {"text/plain": ["64.0"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["total_bs = 1024\n", "\n", "expert_bs = total_bs * model.config['num_experts_per_tok'] / model.config['n_routed_experts']\n", "\n", "logger.info(f'{expert_bs=}')\n", "\n", "\n", "expert_bs * model.config['hidden_size'] * model.config['moe_intermediate_size'] * 2 / (model.config['hidden_size'] * model.config['moe_intermediate_size'])\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "76d88231", "metadata": {}, "outputs": [{"data": {"text/plain": ["295.1819809069213"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from llm_modeling_metrics.core.operators import HardwareSpecs\n", "h100 = HardwareSpecs.from_gpu_spec('nvidia_h100_sxm5')\n", "\n", "\n", "per_card_bs = h100.get_compute_density() * (model.config['hidden_size'] * model.config['moe_intermediate_size']) / (model.config['hidden_size'] * model.config['moe_intermediate_size'] * 2 )\n", "\n", "per_card_bs"]}, {"cell_type": "code", "execution_count": 15, "id": "d59b38f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["HardwareSpecs(name='NVIDIA H100 SXM5', peak_flops=[fp8_tensor:1978.9T, fp16_tensor:989.4T, bf16_tensor:989.4T, tf32_tensor:494.7T, fp64_tensor:66.9T, int8_tensor:1978.9T, fp16:133.8T, bf16:133.8T, fp32:66.9T, fp64:33.5T, int32:33.5T], memory_bandwidth=3352.0GB/s, memory_size=80GB, tensor_cores=528)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["h100"]}, {"cell_type": "code", "execution_count": null, "id": "06badcfb", "metadata": {}, "outputs": [{"data": {"text/plain": ["9445.823389021481"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["global_bs = per_card_bs  * model.config['n_routed_experts'] / model.config['num_experts_per_tok']\n", "\n", "global_bs"]}, {"cell_type": "code", "execution_count": null, "id": "4063637d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3"}}, "nbformat": 4, "nbformat_minor": 5}
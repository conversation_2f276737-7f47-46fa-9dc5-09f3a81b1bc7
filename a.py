import gradio as gr
import matplotlib.pyplot as plt
import numpy as np

def create_plot(frequency):
    x = np.linspace(0, 2 * np.pi, 400)
    y = np.sin(frequency * x)
    fig, ax = plt.subplots()
    ax.plot(x, y)
    ax.set_title(f"Sine Wave (Frequency: {frequency})")
    return fig

iface = gr.Interface(
    fn=create_plot,
    inputs=gr.Slider(minimum=1, maximum=10, step=1, default=1, label="Frequency"),
    outputs=gr.Plot(),
    title="Interactive Sine Wave Plot"
)

iface.launch()
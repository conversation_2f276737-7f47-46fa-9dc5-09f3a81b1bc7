# LLM Modeling Metrics Documentation

Welcome to the LLM Modeling Metrics documentation. This package provides comprehensive analysis of computational requirements and performance characteristics for Large Language Models.

## 📚 Documentation Index

### Core Documentation

- **[API Reference](api_reference.md)** - Complete API documentation with examples
- **[Real Config API](real_config_api.md)** - Automatic model configuration fetching from HuggingFace
- **[Deployment Guide](deployment.md)** - Production deployment and scaling

### Mixed Precision Support

- **[Mixed Precision API](MIXED_PRECISION_API.md)** - API endpoints for mixed precision analysis
- **[Mixed Precision Guide](mixed_precision_guide.md)** - Comprehensive guide to mixed precision concepts
- **[Mixed Precision Migration](mixed_precision_migration.md)** - Step-by-step migration guide
- **[Mixed Precision Best Practices](mixed_precision_best_practices.md)** - Production optimization strategies

### Hardware Integration

- **[Hardware Integration](hardware_integration.md)** - Hardware specification and performance analysis
- **[Hardware Configuration Management](hardware_configuration_management.md)** - Managing hardware profiles and configurations

### Specialized Topics

- **[Attention Operators](attention_operators.md)** - Attention mechanism analysis and optimization

## 🚀 Quick Start

```python
from llm_modeling_metrics import ModelFactory

# Create model with automatic config fetching
model = ModelFactory.create_model("meta-llama/Llama-3.1-8B")

# Get comprehensive metrics
metrics = model.get_metrics(sequence_length=2048)
print(f"Parameters: {metrics.total_params:,}")
print(f"Memory: {metrics.memory_total / 1e9:.2f} GB")
```

## 🌐 Web API

Start the web server for interactive analysis:

```bash
python run_api.py
```

Access the API at `http://localhost:8000` with documentation at `/docs`.

## 📊 Key Features

### Model Analysis
- **Automatic Model Detection**: Supports dense and MoE architectures
- **Real Config Fetching**: Automatic configuration from HuggingFace Hub
- **Mixed Precision Support**: Comprehensive precision optimization
- **Hardware Integration**: Performance analysis on specific hardware

### Web Interface
- **Interactive Dashboard**: Real-time analysis and visualization
- **RESTful API**: Complete HTTP API for integration
- **Export Capabilities**: JSON, CSV, Excel export formats
- **Real-time Updates**: WebSocket support for progress tracking

### Advanced Features
- **Roofline Analysis**: Performance bottleneck identification
- **Operator-Level Timing**: Detailed performance breakdown
- **Hardware Recommendations**: Optimal hardware selection
- **Parallel Strategy Analysis**: Multi-GPU deployment optimization

## 🔧 Installation

```bash
pip install llm-modeling-metrics
```

## 📖 Documentation Structure

```
docs/
├── README.md                           # This file - documentation index
├── api_reference.md                    # Complete API reference
├── real_config_api.md                  # Automatic config fetching
├── deployment.md                       # Production deployment
├── MIXED_PRECISION_API.md             # Mixed precision API endpoints
├── mixed_precision_guide.md           # Mixed precision concepts
├── mixed_precision_migration.md       # Migration guide
├── mixed_precision_best_practices.md  # Production best practices
├── hardware_integration.md            # Hardware analysis
├── hardware_configuration_management.md # Hardware management
└── attention_operators.md             # Attention mechanisms
```

## 🤝 Contributing

For contributing guidelines and development setup, see the main repository README.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

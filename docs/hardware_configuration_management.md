# Hardware Configuration Management

This document describes the advanced hardware configuration management system implemented for the LLM Modeling Metrics platform.

## Overview

The hardware configuration management system provides:

- **Automatic hardware detection** from updated `gpu_specs.yaml` files
- **Custom hardware profile support** through configuration files
- **Hardware specification validation** and error handling
- **Real-time configuration reloading** with file watching
- **System monitoring and health checks** for hardware integration
- **Advanced hardware specification editor** for power users

## Components

### HardwareConfigManager

The `HardwareConfigManager` is the central component that handles:

- Loading and merging hardware configurations from multiple sources
- Automatic detection of configuration file changes
- Custom hardware profile management
- Configuration validation

```python
from llm_modeling_metrics.hardware import HardwareConfigManager

# Initialize with auto-reload enabled
config_manager = HardwareConfigManager(
    primary_config_file="gpu/data/gpu_specs.yaml",
    custom_config_dir="~/.llm_modeling_metrics/hardware_profiles",
    enable_auto_reload=True
)

# Get available hardware
hardware = config_manager.get_available_hardware()

# Save a custom profile
custom_profile = {
    "gpus": {
        "my_custom_gpu": {
            "name": "My Custom GPU",
            "memory_size_gb": 24,
            "memory_bandwidth_gbps": 1500,
            "tensor_performance": {"fp16_tensor": 300},
            "vector_performance": {"fp32": 150}
        }
    }
}

result = config_manager.save_custom_profile("my_profile", custom_profile)
```

### HardwareSpecEditor

The `HardwareSpecEditor` provides advanced editing capabilities:

```python
from llm_modeling_metrics.hardware import HardwareSpecEditor, HardwareType

editor = HardwareSpecEditor()

# Create a new hardware template
gpu_template = editor.create_hardware_template(HardwareType.GPU)

# Clone an existing specification
cloned_spec = editor.clone_hardware_spec("nvidia_h100_sxm5", "my_h100_variant")

# Validate a specification
validation_result = editor.validate_hardware_spec("my_gpu", gpu_template)

# Export to YAML or JSON
yaml_export = editor.export_hardware_spec("nvidia_h100_sxm5", "yaml")
```

### HardwareSystemMonitor

The monitoring system provides health checks and error tracking:

```python
from llm_modeling_metrics.hardware import get_system_monitor

monitor = get_system_monitor()

# Get system health status
health = monitor.get_system_health()
print(f"Overall status: {health['overall_status']}")

# Get error history
errors = monitor.get_error_history(component="hardware_service", limit=10)

# Register custom health check
def custom_check():
    from llm_modeling_metrics.hardware.monitoring import HealthCheck, HealthStatus
    return HealthCheck(
        name="custom_check",
        status=HealthStatus.HEALTHY,
        message="All systems operational"
    )

monitor.register_health_check("my_custom_check", custom_check)
```

## API Endpoints

The system exposes several REST API endpoints for configuration management:

### Configuration Summary
```
GET /api/hardware/config/summary
```
Returns configuration management summary including sources, auto-reload status, and hardware counts.

### System Health
```
GET /api/hardware/config/health
```
Returns system health status with health checks, error summaries, and performance metrics.

### Validate Configurations
```
POST /api/hardware/config/validate
```
Validates all loaded hardware configurations and returns validation results.

### List Custom Profiles
```
GET /api/hardware/config/profiles
```
Lists all custom hardware profiles with metadata.

### Save Custom Profile
```
POST /api/hardware/config/profiles
```
Saves a new custom hardware profile.

### Delete Custom Profile
```
DELETE /api/hardware/config/profiles/{profile_name}
```
Deletes a custom hardware profile.

## Custom Hardware Profiles

Custom hardware profiles allow you to:

1. **Add new hardware devices** not in the primary configuration
2. **Override existing specifications** with custom values
3. **Create hardware variants** for testing and development
4. **Share hardware configurations** across teams

### Profile Format

Custom profiles use the same YAML/JSON format as the primary configuration:

```yaml
gpus:
  my_custom_gpu:
    name: "My Custom GPU"
    architecture: "Custom Architecture"
    memory_size_gb: 32
    memory_bandwidth_gbps: 2000
    tensor_performance:
      fp16_tensor: 400
      bf16_tensor: 400
    vector_performance:
      fp32: 200
      fp16: 400

npus:
  my_custom_npu:
    name: "My Custom NPU"
    memory_size_gb: 64
    memory_bandwidth_gbps: 1600
    tensor_performance:
      fp16_tensor: 320
      int8_tensor: 640
```

### Profile Locations

Custom profiles are stored in:
- **Default location**: `~/.llm_modeling_metrics/hardware_profiles/`
- **Custom location**: Configurable via `HardwareConfigManager`

## Error Handling and Fallbacks

The system includes comprehensive error handling:

### Validation Errors
- Hardware specification validation with detailed error messages
- Automatic fallback to default specifications when validation fails
- Recommendations for fixing configuration issues

### Configuration Loading Errors
- Graceful handling of missing or corrupted configuration files
- Fallback to minimal hardware specifications
- Detailed error logging and monitoring

### Timing Calculation Errors
- Fallback timing values when calculations fail
- Error context preservation for debugging
- Performance warning system for slow operations

## Monitoring and Health Checks

The system includes built-in health checks for:

- **Hardware Configuration**: Validates configuration accessibility
- **Memory Usage**: Monitors system memory consumption
- **Error Rate**: Tracks error frequency and severity
- **Performance**: Monitors operation timing and bottlenecks

### Custom Health Checks

You can register custom health checks:

```python
def database_health_check():
    # Check database connectivity
    try:
        # Your database check logic here
        return HealthCheck(
            name="database",
            status=HealthStatus.HEALTHY,
            message="Database connection OK"
        )
    except Exception as e:
        return HealthCheck(
            name="database",
            status=HealthStatus.ERROR,
            message=f"Database error: {e}"
        )

monitor.register_health_check("database", database_health_check)
```

## Best Practices

1. **Use version control** for custom hardware profiles
2. **Validate configurations** before deploying to production
3. **Monitor system health** regularly
4. **Set up fallback configurations** for critical hardware
5. **Test configuration changes** in development environments
6. **Document custom hardware specifications** thoroughly

## Troubleshooting

### Configuration Not Loading
- Check file permissions and paths
- Verify YAML/JSON syntax
- Review error logs for specific issues

### Auto-reload Not Working
- Ensure file watching is enabled
- Check directory permissions
- Verify file system supports change notifications

### Validation Failures
- Review validation error messages
- Check required fields are present
- Verify data types and ranges

### Performance Issues
- Monitor system health metrics
- Check for slow operations in logs
- Consider disabling auto-reload in high-load scenarios

## Migration Guide

### From Basic Hardware Service

If you're upgrading from the basic `HardwareService`:

```python
# Old way
from llm_modeling_metrics.hardware import HardwareService
service = HardwareService("gpu_specs.yaml")

# New way with configuration management
from llm_modeling_metrics.hardware import HardwareService
service = HardwareService(
    specs_file="gpu_specs.yaml",
    enable_monitoring=True,
    enable_auto_reload=True
)

# Access new features
config_summary = service.get_configuration_summary()
health_status = service.get_system_health()
custom_profiles = service.list_custom_profiles()
```

### Configuration File Updates

The system automatically detects updates to the primary `gpu_specs.yaml` file and reloads configurations. No manual intervention is required.

## Security Considerations

- Custom profile directories should have appropriate file permissions
- Validate all user-provided hardware specifications
- Monitor for unauthorized configuration changes
- Use authentication for configuration management API endpoints
- Regularly audit custom hardware profiles for accuracy

# LLM Modeling Metrics API Reference

A comprehensive Python library for analyzing computational requirements and performance characteristics of Large Language Models.

## Quick Start

```python
from llm_modeling_metrics import ModelFactory

# Create model with automatic config fetching
model = ModelFactory.create_model("meta-llama/Llama-3.1-8B")

# Get comprehensive metrics
metrics = model.get_metrics(sequence_length=2048)
print(f"Parameters: {metrics.total_params:,}")
print(f"Memory: {metrics.memory_total / 1e9:.2f} GB")
```

## Core Classes

### ModelFactory

Factory class for creating model instances with automatic configuration fetching.

```python
from llm_modeling_metrics import ModelFactory

# The factory is pre-configured with model types
model = ModelFactory.create_model("model-name")
```

#### Methods

##### `create_model(model_name: str, config: Optional[Any] = None) -> BaseModel`

Create appropriate model instance based on architecture with automatic config fetching.

**Parameters:**
- `model_name` (str): HuggingFace model name or path
- `config` (optional): Pre-loaded model configuration (fetched automatically if None)

**Returns:**
- `BaseModel`: Model instance (DenseModel or MoEModel)

**Example:**
```python
# Automatic config fetching and model type detection
model = ModelFactory.create_model("meta-llama/Llama-3.1-8B")  # -> DenseModel
moe_model = ModelFactory.create_model("deepseek-ai/DeepSeek-V3")  # -> MoEModel

# With custom config
from transformers import AutoConfig
config = AutoConfig.from_pretrained("meta-llama/Llama-3.1-8B")
model = ModelFactory.create_model("meta-llama/Llama-3.1-8B", config=config)
```

##### `get_supported_architectures() -> List[str]`

Get list of supported model architectures.

**Returns:**
- `List[str]`: List of architecture names ['dense', 'moe']

### BaseModel

Abstract base class for all model implementations.

```python
from llm_modeling_metrics import BaseModel, ModelMetrics, ParallelConfig

# Don't instantiate directly - use ModelFactory.create_model()
model = ModelFactory.create_model("model-name")
```

#### Methods

##### `get_metrics(sequence_length: int = 2048, batch_size: int = 1, parallel_config: Optional[ParallelConfig] = None, kv_lens: Optional[int] = None) -> ModelMetrics`

Get comprehensive model metrics including parameters, FLOPs, and memory requirements.

**Parameters:**
- `sequence_length` (int): Input sequence length for FLOP calculation
- `batch_size` (int): Batch size for analysis
- `parallel_config` (ParallelConfig, optional): Parallel execution configuration
- `kv_lens` (int, optional): KV cache length for decode analysis

**Returns:**
- `ModelMetrics`: Comprehensive metrics object

**Example:**
```python
model = ModelFactory.create_model("meta-llama/Llama-3.1-8B")
metrics = model.get_metrics(sequence_length=4096, batch_size=2)
print(f"Parameters: {metrics.total_params:,}")
print(f"FLOPs: {metrics.flops_forward:,}")
```

##### `compute_flops(sequence_length: int = 2048, batch_size: int = 1, kv_lens: Optional[int] = None) -> Dict[str, int]`

Compute detailed FLOP breakdown by component.

**Parameters:**
- `sequence_length` (int): Input sequence length
- `batch_size` (int): Batch size for analysis
- `kv_lens` (int, optional): KV cache length for decode analysis

**Returns:**
- `Dict[str, int]`: FLOP breakdown by component

**Example:**
```python
flops = model.compute_flops(sequence_length=2048, batch_size=1)
print(f"Attention FLOPs: {flops['attention']:,}")
print(f"MLP FLOPs: {flops['mlp']:,}")
print(f"Total FLOPs: {flops['total']:,}")
```

##### `compute_memory_requirements(sequence_length: int = 2048, batch_size: int = 1, **kwargs) -> Dict[str, int]`

Compute detailed memory requirements with comprehensive mixed precision support.

**Parameters:**
- `sequence_length` (int): Input sequence length
- `batch_size` (int): Batch size
- `dtype` (str, optional): Legacy parameter for uniform precision (default: 'fp16')
- `weight_dtype` (str, optional): Precision for model weights (default: 'bf16')
- `activation_dtype` (str, optional): Precision for activations (default: 'bf16')
- `kv_cache_dtype` (str, optional): Precision for KV cache (default: 'bf16')
- `attention_parameter_dtype` (str, optional): Precision for attention parameters (default: 'bf16')
- `expert_parameter_dtype` (str, optional): Precision for expert parameters in MoE models (default: 'fp8')
- `grad_dtype` (str, optional): Precision for gradients during training (default: 'fp16')
- `optimizer_dtype` (str, optional): Precision for optimizer states (default: 'fp32')
- `training` (bool): Whether to include training memory (default: False)
- `include_kv_cache` (bool): Whether to include KV cache memory (default: False)

**Returns:**
- `Dict[str, int]`: Memory breakdown by component in bytes

**Mixed Precision Examples:**
```python
# Basic mixed precision for dense models
memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',
    activation_dtype='bf16',
    kv_cache_dtype='fp8',  # Save memory on KV cache
    include_kv_cache=True
)

# MoE model with expert parameter optimization
moe_memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    weight_dtype='bf16',                    # Base model weights
    activation_dtype='bf16',                # Activations
    kv_cache_dtype='fp8',                   # KV cache compression
    attention_parameter_dtype='bf16',       # Keep attention quality
    expert_parameter_dtype='fp8',           # Compress expert parameters
    include_kv_cache=True
)

# Training with mixed precision
training_memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',
    activation_dtype='bf16',
    grad_dtype='fp16',      # FP16 gradients
    optimizer_dtype='fp32', # FP32 optimizer states
    training=True
)
```

**Supported Precision Types:**
- `'fp32'`: 32-bit floating point (4 bytes)
- `'bf16'`: 16-bit brain floating point (2 bytes)
- `'fp16'`: 16-bit floating point (2 bytes)
- `'fp8'`: 8-bit floating point (1 byte)
- `'int8'`: 8-bit integer (1 byte)
- `'fp4'`: 4-bit floating point (0.5 bytes, packed)

##### `get_matrix_shapes(parallel_config: ParallelConfig) -> Dict[str, Any]`

Get matrix shapes under parallel configuration.

**Parameters:**
- `parallel_config` (ParallelConfig): Parallel execution configuration

**Returns:**
- `Dict[str, Any]`: Matrix shapes by component

##### `get_total_params() -> int`

Get total number of parameters in the model.

**Returns:**
- `int`: Total parameter count

**Example:**
```python
total_params = model.get_total_params()
print(f"Model has {total_params / 1e9:.1f}B parameters")
```

##### `get_matrix_shapes(parallel_config: Optional[ParallelConfig] = None, sequence_length: int = 1, batch_size: int = 1) -> Dict[str, Any]`

Get matrix shapes under parallel configuration.

**Parameters:**
- `parallel_config` (ParallelConfig, optional): Parallel execution configuration
- `sequence_length` (int): Input sequence length
- `batch_size` (int): Batch size

**Returns:**
- `Dict[str, Any]`: Matrix shapes by component

**Example:**
```python
shapes = model.get_matrix_shapes(sequence_length=2048)
print(f"Attention shapes: {shapes['attention']}")
print(f"MLP shapes: {shapes['mlp']}")
```

##### `validate_parallel_config(parallel_config: ParallelConfig) -> bool`

Validate that a parallel configuration is feasible for this model.

**Parameters:**
- `parallel_config` (ParallelConfig): Parallel configuration to validate

**Returns:**
- `bool`: True if configuration is valid, False otherwise

### ConfigManager

Manages model configuration fetching and caching from HuggingFace Hub.

```python
from llm_modeling_metrics import ConfigManager

# Initialize with optional HuggingFace token for private models
config_manager = ConfigManager(token="your_hf_token")
```

#### Methods

##### `fetch_config(model_name: str, force_refresh: bool = False) -> Dict[str, Any]`

Fetch and cache model configuration from HuggingFace Hub.

**Parameters:**
- `model_name` (str): HuggingFace model name
- `force_refresh` (bool): Force refresh from remote (default: False)

**Returns:**
- `Dict[str, Any]`: Model configuration

**Example:**
```python
config_manager = ConfigManager(token="your_hf_token")
config = config_manager.fetch_config("meta-llama/Llama-3.1-8B")
```

##### `get_cached_config(model_name: str) -> Optional[Dict[str, Any]]`

Get cached configuration without network call.

**Parameters:**
- `model_name` (str): HuggingFace model name

**Returns:**
- `Optional[Dict[str, Any]]`: Cached configuration or None

##### `clear_cache(model_name: Optional[str] = None) -> None`

Clear cached configurations.

**Parameters:**
- `model_name` (str, optional): Specific model to clear cache for, or None to clear all

##### `list_cached_models() -> List[str]`

List all models that have cached configurations.

**Returns:**
- `List[str]`: List of model names with cached configurations

## Hardware Integration

### HardwareService

Service for managing hardware specifications and performance analysis.

```python
from llm_modeling_metrics import HardwareService

# Initialize with GPU specifications
service = HardwareService("data/gpu_specs.yaml")

# Get available hardware
hardware_list = service.get_available_hardware()
```

#### Methods

##### `get_available_hardware() -> List[HardwareSpec]`

Get list of available hardware specifications.

##### `get_hardware_spec(hardware_id: str) -> HardwareSpec`

Get detailed specifications for specific hardware.

##### `validate_hardware_compatibility(hardware_id: str, model_config: Dict) -> ValidationResult`

Validate hardware compatibility with model requirements.

### HardwareSpec

Hardware specification data class.

```python
@dataclass
class HardwareSpec:
    id: str
    name: str
    memory_size_gb: float
    memory_bandwidth_gbps: float
    peak_flops: Dict[str, float]  # TFLOPS by precision
    tensor_cores: bool
```

### Roofline Analysis

Performance analysis using the roofline model.

```python
# Get roofline analysis for model on specific hardware
roofline_data = model.get_roofline_analysis(
    batch_size=1,
    sequence_length=2048,
    hardware_name='nvidia_h100_sxm5'
)

print(f"Arithmetic intensity: {roofline_data['arithmetic_intensity']:.2f}")
print(f"Performance bound: {roofline_data['performance_bound']}")
```

### Operator-Level Analysis

Detailed operator performance analysis.

```python
# Get operator breakdown with hardware-specific timing
operator_breakdown = model.get_operator_breakdown(
    batch_size=1,
    sequence_length=2048,
    hardware_name='nvidia_h100_sxm5'
)

for op_name, metrics in operator_breakdown.items():
    print(f"{op_name}: {metrics['execution_time_ms']:.2f}ms")
```

## Data Classes

### ModelMetrics

Container for comprehensive model metrics.

```python
@dataclass
class ModelMetrics:
    model_name: str
    architecture: str
    total_params: int
    attention_params: int
    mlp_params: int
    embedding_params: int
    flops_forward: int
    flops_per_token: int
    memory_params: int
    memory_activations: int
    memory_total: int
    attention_shapes: Dict[str, Tuple[int, ...]]
    mlp_shapes: Dict[str, Tuple[int, ...]]
    sequence_length: int
    batch_size: int
    timestamp: datetime
    # MoE-specific fields
    experts_per_token: Optional[int] = None
    active_params_per_token: Optional[int] = None
```

**Key Attributes:**
- `total_params`: Total model parameters
- `flops_forward`: Forward pass FLOPs
- `flops_per_token`: FLOPs per token
- `memory_total`: Total memory requirements (bytes)
- `attention_shapes`: Attention layer matrix shapes
- `mlp_shapes`: MLP layer matrix shapes
- `experts_per_token`: Active experts per token (MoE only)

### ParallelConfig

Configuration for parallel execution strategies with MoE support.

```python
@dataclass
class ParallelConfig:
    tensor_parallel_size: int = 1
    pipeline_parallel_size: int = 1
    data_parallel_size: int = 1
    expert_parallel_size: int = 1
    expert_data_parallel_size: int = 1
```

**Attributes:**
- `tensor_parallel_size`: Tensor parallelism degree
- `pipeline_parallel_size`: Pipeline parallelism degree
- `data_parallel_size`: Data parallelism degree
- `expert_parallel_size`: Expert parallelism degree (MoE)
- `expert_data_parallel_size`: Expert data parallelism degree (MoE)

**Example:**
```python
from llm_modeling_metrics import ParallelConfig

# Standard tensor parallelism
config = ParallelConfig(tensor_parallel_size=4)

# MoE with expert parallelism
moe_config = ParallelConfig(
    tensor_parallel_size=2,
    expert_parallel_size=4
)

# Validate configuration
is_valid = model.validate_parallel_config(config)
```

## Model Implementations

The system automatically classifies models into two types based on their architecture:

### DenseModel

Implementation for dense transformer models where all parameters are active for every token.

```python
# Use ModelFactory for automatic detection
model = ModelFactory.create_model("meta-llama/Llama-3.1-8B")  # -> DenseModel

# Direct instantiation (not recommended)
from llm_modeling_metrics.models import DenseModel
model = DenseModel("meta-llama/Llama-3.1-8B")
```

**Automatically detected for:**
- **Llama family**: Llama 2, Llama 3, Llama 3.1, Code Llama, etc.
- **Qwen family**: Qwen, Qwen2, Qwen2.5, etc. (non-MoE variants)
- **Mistral family**: Mistral 7B, Mistral Nemo (dense variants)
- **Other architectures**: Any standard transformer without MoE components

**Key Features:**
- Standard multi-head attention (MHA) or grouped-query attention (GQA)
- All parameters active for every forward pass
- Optimized memory calculations for dense architectures

### MoEModel

Implementation for Mixture of Experts models with sparse activation patterns.

```python
# Use ModelFactory for automatic detection
model = ModelFactory.create_model("deepseek-ai/DeepSeek-V3")  # -> MoEModel

# Direct instantiation (not recommended)
from llm_modeling_metrics.models import MoEModel
model = MoEModel("deepseek-ai/DeepSeek-V3")
```

**Automatically detected for:**
- **DeepSeek family**: DeepSeek V2, DeepSeek V3 (with MLA attention)
- **Mixtral family**: Mixtral 8x7B, Mixtral 8x22B
- **Qwen MoE variants**: Qwen2.5-MoE series
- **Any model with**: Expert routing, sparse activation patterns

**Key Features:**
- Mixture of Experts with sparse activation
- Multi-head Latent Attention (MLA) support for DeepSeek models
- Expert-specific mixed precision optimization
- Accurate active parameter counting

**MoE-Specific Methods:**

##### `get_expert_metrics() -> Dict[str, Any]`

Get MoE-specific metrics including expert utilization and routing information.

**Returns:**
- `Dict[str, Any]`: Expert-specific metrics

**Example:**
```python
moe_model = ModelFactory.create_model("deepseek-ai/DeepSeek-V3")
expert_metrics = moe_model.get_expert_metrics()
print(f"Active experts per token: {expert_metrics['experts_per_token']}")
print(f"Total experts: {expert_metrics['num_experts']}")
print(f"Active params per token: {expert_metrics['active_params_per_token']}")
```

##### `compute_memory_requirements()` - MoE-Specific Mixed Precision

MoE models support additional mixed precision parameters for expert components:

**Additional Parameters:**
- `expert_parameter_dtype` (str, optional): Precision for expert parameters (default: 'fp8')

**MoE Mixed Precision Examples:**
```python
# Balanced MoE optimization
memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    weight_dtype='bf16',                    # Base model weights
    activation_dtype='bf16',                # Activations
    kv_cache_dtype='fp8',                   # KV cache compression
    attention_parameter_dtype='bf16',       # Keep attention quality
    expert_parameter_dtype='fp8',           # Expert params compressed
    include_kv_cache=True
)

# Aggressive expert quantization for memory-constrained deployment
aggressive_memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    expert_parameter_dtype='fp4',           # 4-bit expert parameters
    attention_parameter_dtype='bf16',       # Keep attention quality
    include_kv_cache=True
)

# Memory breakdown shows expert vs shared parameter usage
print(f"Total memory: {memory['memory_total'] / 1e9:.2f} GB")
print(f"Expert memory: {memory.get('expert_memory', 0) / 1e9:.2f} GB")
```

## Comparison Module

### Comparator

Engine for comparing multiple models with comprehensive analysis.

```python
from llm_modeling_metrics.comparison import Comparator

comparator = Comparator()
```

#### Methods

##### `compare_models(model_names: List[str], sequence_length: int = 2048, batch_size: int = 1, parallel_configs: Optional[List[ParallelConfig]] = None, **kwargs) -> ComparisonResult`

Compare multiple models across metrics with mixed precision support.

**Parameters:**
- `model_names` (List[str]): List of model names to compare
- `sequence_length` (int): Sequence length for analysis
- `batch_size` (int): Batch size for analysis
- `parallel_configs` (List[ParallelConfig], optional): Parallel configurations to test
- `**kwargs`: Mixed precision parameters (weight_dtype, activation_dtype, etc.)

**Returns:**
- `ComparisonResult`: Comprehensive comparison results

**Example:**
```python
# Basic comparison
results = comparator.compare_models([
    "meta-llama/Llama-3.1-8B",
    "deepseek-ai/DeepSeek-V3"
])

# Mixed precision comparison
mixed_precision_results = comparator.compare_models([
    "meta-llama/Llama-3.1-8B",
    "deepseek-ai/DeepSeek-V3"
],
sequence_length=4096,
weight_dtype='bf16',
kv_cache_dtype='fp8',
expert_parameter_dtype='fp8'
)

# Export results
results.export_excel("model_comparison.xlsx")
df = results.to_dataframe()
```

### ComparisonResult

Container for comparison results with export capabilities.

```python
@dataclass
class ComparisonResult:
    models: List[str]
    metrics: Dict[str, List[Any]]
    parallel_configs: List[ParallelConfig]
    timestamp: datetime
    metadata: Dict[str, Any]
```

#### Methods

##### `to_dataframe() -> pd.DataFrame`

Convert results to pandas DataFrame for analysis.

##### `export_excel(filename: str) -> None`

Export results to Excel file with multiple sheets.

##### `export_json(filename: str) -> None`

Export results to JSON file.

##### `export_csv(filename: str) -> None`

Export results to CSV file.

##### `get_summary() -> Dict[str, Any]`

Get summary statistics of the comparison.

**Example:**
```python
# Get comparison summary
summary = results.get_summary()
print(f"Models compared: {summary['num_models']}")
print(f"Best efficiency: {summary['best_efficiency_model']}")
print(f"Lowest memory: {summary['lowest_memory_model']}")
```

## Web API

The package includes a comprehensive FastAPI-based web service for analyzing LLM computational requirements.

### Starting the Web Server

```python
# Using the provided script
python run_api.py

# Or programmatically
from llm_modeling_metrics.web.app import app
import uvicorn

uvicorn.run(app, host="0.0.0.0", port=8000)
```

The API will be available at `http://localhost:8000` with interactive documentation at `/docs`.

### Core Analysis Endpoints

#### POST `/api/analyze`

Analyze models and return comprehensive metrics with mixed precision support.

**Request Body:**
```json
{
    "model_names": ["meta-llama/Llama-3.1-8B"],
    "sequence_length": 2048,
    "batch_size": 1,
    "kv_lens": null,
    "parallel_config": {
        "tensor_parallel_size": 1,
        "pipeline_parallel_size": 1,
        "data_parallel_size": 1
    },
    "weight_dtype": "bf16",
    "activation_dtype": "bf16",
    "kv_cache_dtype": "fp8",
    "attention_parameter_dtype": "bf16",
    "expert_parameter_dtype": "fp8"
}
```

**Response:**
```json
{
    "results": {
        "meta-llama/Llama-3.1-8B": {
            "model_name": "meta-llama/Llama-3.1-8B",
            "architecture": "dense",
            "total_params": 8030261248,
            "flops_forward": 1644167168000,
            "memory_total": 32121044992,
            "attention_mechanism": "GQA"
        }
    },
    "execution_time": 1.23,
    "timestamp": "2025-01-25T10:30:00Z"
}
```

#### POST `/api/memory/analyze`

Detailed memory analysis with mixed precision breakdown.

**Request Body:**
```json
{
    "model_names": ["deepseek-ai/DeepSeek-V3"],
    "sequence_length": 4096,
    "batch_size": 1,
    "dtype": "fp16",
    "weight_dtype": "bf16",
    "activation_dtype": "bf16",
    "kv_cache_dtype": "fp8",
    "expert_parameter_dtype": "fp8",
    "include_kv_cache": true,
    "training": false
}
```

**Response:**
```json
{
    "model_results": {
        "deepseek-ai/DeepSeek-V3": {
            "parameters": 671088640000,
            "activations": 134217728,
            "kv_cache": 67108864,
            "gradients": 0,
            "optimizer_states": 0,
            "total": 671290054592,
            "attention_mechanism": "MLA",
            "by_component": {
                "attention": 268435456000,
                "mlp": 402653184000,
                "embeddings": 0
            },
            "by_precision": {
                "bf16": 536870912000,
                "fp8": 134217728000,
                "fp16": 0,
                "fp32": 0
            },
            "efficiency_metrics": {
                "memory_savings_percent": 12.5,
                "compression_ratio": 0.875
            }
        }
    },
    "execution_time": 0.45
}
```

#### POST `/api/memory/kv-growth`

Analyze KV cache memory growth across different sequence lengths.

**Request Body:**
```json
{
    "model_names": ["meta-llama/Llama-3.1-8B"],
    "sequence_lengths": [1024, 2048, 4096, 8192],
    "batch_size": 1,
    "kv_cache_dtype": "fp8"
}
```

#### GET `/api/memory/dtypes`

Get supported data types and their characteristics.

**Response:**
```json
{
    "supported_dtypes": ["fp32", "fp16", "bf16", "int8", "fp8", "fp4"],
    "default_dtype": "fp16",
    "dtype_info": {
        "fp32": {
            "bytes_per_element": 4,
            "description": "32-bit floating point",
            "use_case": "Highest precision, largest memory usage"
        },
        "fp8": {
            "bytes_per_element": 1,
            "description": "8-bit floating point",
            "use_case": "Memory-efficient with good quality"
        }
    }
}
```

### Model Management Endpoints

#### GET `/api/models/supported`

Get list of supported model architectures.

**Response:**
```json
{
    "architectures": ["dense", "moe"],
    "architecture_info": {
        "dense": {
            "examples": ["meta-llama/Llama-3.1-8B", "Qwen/Qwen2.5-7B"]
        },
        "moe": {
            "examples": ["deepseek-ai/DeepSeek-V3", "mistralai/Mixtral-8x7B-v0.1"]
        }
    }
}
```

#### GET `/api/models/validate/{model_name}`

Validate if a model is supported and can be analyzed.

**Response:**
```json
{
    "valid": true,
    "model_name": "meta-llama/Llama-3.1-8B",
    "architecture": "dense",
    "config_available": true,
    "estimated_params": 8030261248
}
```

### Hardware Integration Endpoints

#### GET `/api/hardware/list`

Get list of available hardware specifications.

#### GET `/api/hardware/{hardware_id}/specs`

Get detailed specifications for specific hardware.

#### POST `/api/hardware/recommendations`

Get hardware recommendations for specific workloads.

### Roofline Analysis Endpoints

#### POST `/api/roofline/generate`

Generate roofline model data for hardware platforms.

#### POST `/api/roofline/plot-operators`

Plot operators on roofline model for performance analysis.

#### POST `/api/roofline/compare`

Compare multiple hardware platforms using roofline analysis.

### Real-time Features

#### WebSocket `/ws/{client_id}`

Real-time analysis updates and progress tracking.

#### GET `/api/analysis/status/{request_id}`

Get status of long-running analysis requests.

#### GET `/api/analysis/list`

List recent analysis requests with filtering options.

### Utility Endpoints

#### GET `/health`

Health check endpoint.

#### GET `/api/stats`

API usage statistics and performance metrics.

#### POST `/api/export`

Export analysis results in various formats (JSON, CSV, Excel).

## Mixed Precision Support

The package provides comprehensive mixed precision support for optimizing memory usage and performance across different model components.

### Quick Start with Mixed Precision

```python
# Basic mixed precision for dense models
memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',      # High quality weights
    activation_dtype='bf16',  # Stable activations
    kv_cache_dtype='fp8',     # Memory-efficient KV cache
    include_kv_cache=True
)

# MoE-specific mixed precision
moe_memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    attention_parameter_dtype='bf16',  # Keep attention quality
    expert_parameter_dtype='fp8',      # Compress expert parameters
    include_kv_cache=True
)
```

### Supported Precision Types

| Type | Bytes | Description | Best Use Case |
|------|-------|-------------|---------------|
| `fp32` | 4 | 32-bit float | Highest precision, training |
| `bf16` | 2 | Brain float 16 | Balanced quality/memory |
| `fp16` | 2 | Half precision | Good quality, wide support |
| `fp8` | 1 | 8-bit float | Memory efficient, good quality |
| `int8` | 1 | 8-bit integer | Quantized inference |
| `fp4` | 0.5 | 4-bit float | Extreme compression |

### Mixed Precision Guidelines

For detailed guidance, see:
- **[Mixed Precision Guide](mixed_precision_guide.md)** - Comprehensive concepts and usage
- **[Migration Guide](mixed_precision_migration.md)** - Step-by-step migration
- **[Best Practices](mixed_precision_best_practices.md)** - Production optimization

## Error Handling

### Exception Hierarchy

```python
class LLMModelingError(Exception):
    """Base exception for LLM modeling package"""

class ConfigurationError(LLMModelingError):
    """Model configuration is invalid or unavailable"""

class ModelNotSupportedError(LLMModelingError):
    """Model architecture is not supported"""

class ComputationError(LLMModelingError):
    """Metric computation failed"""

class ValidationError(LLMModelingError):
    """Input validation failed"""

class HardwareError(LLMModelingError):
    """Hardware specification or compatibility error"""
```

### Error Handling Examples

```python
from llm_modeling_metrics import ModelFactory, ConfigurationError, ModelNotSupportedError

try:
    model = ModelFactory.create_model("invalid-model-name")
except ConfigurationError as e:
    print(f"Configuration error: {e}")
    # Fallback to cached config or manual config
except ModelNotSupportedError as e:
    print(f"Model not supported: {e}")
    # Handle unsupported architecture
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Mixed Precision Support

The package provides comprehensive mixed precision support for optimizing memory usage and performance. See the dedicated guides for detailed information:

- **[Mixed Precision Guide](mixed_precision_guide.md)** - Comprehensive guide to mixed precision concepts and usage
- **[Migration Guide](mixed_precision_migration.md)** - Step-by-step migration from single precision to mixed precision
- **[Best Practices](mixed_precision_best_practices.md)** - Production-ready best practices and optimization strategies

### Quick Start with Mixed Precision

```python
# Basic mixed precision configuration
memory = model.compute_memory_requirements(
    sequence_length=2048,
    weight_dtype='bf16',      # High quality weights
    activation_dtype='bf16',  # Stable activations
    kv_cache_dtype='fp8',     # Memory-efficient KV cache
    include_kv_cache=True
)

# MoE-specific mixed precision
moe_memory = moe_model.compute_memory_requirements(
    sequence_length=4096,
    attention_parameter_dtype='bf16',  # Keep attention quality
    expert_parameter_dtype='fp8',      # Compress expert parameters
    include_kv_cache=True
)
```

## Utilities

### Caching

The package includes automatic caching for model configurations and computation results to improve performance.

```python
from llm_modeling_metrics.utils.caching import get_cache_manager

# Get global cache manager
cache_manager = get_cache_manager()

# Clear all cached data
cache_manager.clear_all()

# Get cache statistics
stats = cache_manager.get_cache_stats()
print(f"Config cache size: {stats['config_cache']['size']}")
print(f"Computation cache size: {stats['computation_cache']['size']}")

# Cleanup expired entries
removed = cache_manager.cleanup_expired()
```

### Performance Monitoring

Built-in performance monitoring and profiling utilities.

```python
from llm_modeling_metrics.utils.performance import PerformanceMonitor

# Monitor analysis performance
with PerformanceMonitor() as monitor:
    model = ModelFactory.create_model("meta-llama/Llama-3.1-8B")
    metrics = model.get_metrics(sequence_length=4096)

print(f"Analysis took: {monitor.elapsed_time:.2f}s")
print(f"Memory usage: {monitor.peak_memory_mb:.1f}MB")
```

### Table Generation

Utilities for generating formatted comparison tables.

```python
from llm_modeling_metrics.utils.table_generator import TableGenerator

# Generate comparison table
table = TableGenerator.create_comparison_table(
    models=["meta-llama/Llama-3.1-8B", "deepseek-ai/DeepSeek-V3"],
    metrics=['total_params', 'memory_total', 'flops_forward']
)

print(table.to_string())
table.to_excel("comparison.xlsx")
```

## Complete Examples

### Basic Model Analysis

```python
from llm_modeling_metrics import ModelFactory

# Create and analyze a model
model = ModelFactory.create_model("meta-llama/Llama-3.1-8B")

# Get basic metrics
metrics = model.get_metrics(sequence_length=2048)
print(f"Model: {metrics.model_name}")
print(f"Parameters: {metrics.total_params / 1e9:.1f}B")
print(f"Memory: {metrics.memory_total / 1e9:.2f} GB")

# Get detailed memory breakdown
memory = model.compute_memory_requirements(
    sequence_length=2048,
    include_kv_cache=True
)
print(f"Parameters: {memory['memory_params'] / 1e9:.2f} GB")
print(f"KV Cache: {memory['memory_kv_cache'] / 1e9:.2f} GB")
```

### Mixed Precision Optimization

```python
# Compare different precision configurations
configs = [
    {'name': 'FP16 Baseline', 'dtype': 'fp16'},
    {'name': 'Mixed BF16/FP8', 'weight_dtype': 'bf16', 'kv_cache_dtype': 'fp8'},
    {'name': 'Aggressive FP8', 'weight_dtype': 'fp8', 'activation_dtype': 'fp8'}
]

for config in configs:
    memory = model.compute_memory_requirements(
        sequence_length=4096,
        include_kv_cache=True,
        **{k: v for k, v in config.items() if k != 'name'}
    )
    print(f"{config['name']}: {memory['memory_total'] / 1e9:.2f} GB")
```

### Model Comparison

```python
from llm_modeling_metrics.comparison import Comparator

# Compare multiple models
comparator = Comparator()
results = comparator.compare_models([
    "meta-llama/Llama-3.1-8B",
    "meta-llama/Llama-3.1-70B",
    "deepseek-ai/DeepSeek-V3"
], sequence_length=4096)

# Export results
results.export_excel("model_comparison.xlsx")

# Get summary
summary = results.get_summary()
print(f"Most efficient model: {summary['best_efficiency_model']}")
```

### Hardware Integration

```python
from llm_modeling_metrics import HardwareService

# Initialize hardware service
hardware_service = HardwareService()

# Get hardware recommendations
recommendations = hardware_service.get_hardware_recommendations(
    model_type="dense",
    batch_size=16,
    sequence_length=2048,
    precision_requirements=["fp16", "bf16"]
)

for rec in recommendations[:3]:  # Top 3 recommendations
    print(f"{rec.hardware_name}: Score {rec.score:.1f}/100")
```

### Web API Usage

```python
import requests

# Analyze model via API
response = requests.post("http://localhost:8000/api/analyze", json={
    "model_names": ["meta-llama/Llama-3.1-8B"],
    "sequence_length": 2048,
    "weight_dtype": "bf16",
    "kv_cache_dtype": "fp8"
})

results = response.json()
model_metrics = results["results"]["meta-llama/Llama-3.1-8B"]
print(f"Total params: {model_metrics['total_params']:,}")
```

## Installation and Setup

### Installation

```bash
pip install llm-modeling-metrics
```

### Environment Setup

```bash
# Optional: Set HuggingFace token for private models
export HF_TOKEN="your_token_here"

# Optional: Configure cache directory
export LLM_MODELING_CACHE_DIR="/path/to/cache"
```

### Verification

```python
from llm_modeling_metrics import ModelFactory

# Test installation
model = ModelFactory.create_model("microsoft/DialoGPT-medium")
metrics = model.get_metrics()
print(f"Installation successful! Model has {metrics.total_params:,} parameters")
```

# Real Config API Documentation

The LLM Modeling Metrics package now supports automatic fetching and caching of real model configurations from HuggingFace, eliminating the need to manually maintain configuration dictionaries.

## Overview

Instead of manually creating model configurations, the package can now:
- Automatically fetch model configs from HuggingFace Hub
- Cache configs locally with TTL (Time To Live)
- Detect model architecture automatically (Mo<PERSON> vs Dense)
- Handle network failures gracefully with cached fallbacks
- Support private models with HuggingFace tokens

## Quick Start

### Basic Usage

```python
from llm_modeling_metrics.core.model_factory import ModelFactory
from llm_modeling_metrics.models.moe_model import MoEModel
from llm_modeling_metrics.models.dense_model import DenseModel

# Initialize factory
model_factory = ModelFactory()
model_factory.register_model('moe', MoEModel)
model_factory.register_model('dense', DenseModel)

# Create model with automatic config fetching
model = model_factory.create_model("deepseek-ai/DeepSeek-V3")

# The config is automatically fetched, cached, and the correct model type is created
print(f"Created {type(model).__name__} with {model.get_total_params() / 1e9:.1f}B parameters")
```

### Manual Config Management

```python
from llm_modeling_metrics.core.config_manager import ConfigManager

# Initialize config manager
config_manager = ConfigManager()

# Fetch and cache config
config = config_manager.fetch_config("meta-llama/Llama-3.1-8B")

# Create model manually
from llm_modeling_metrics.models.dense_model import DenseModel
model = DenseModel("meta-llama/Llama-3.1-8B", config)
```

## Configuration Options

### ConfigManager Options

```python
config_manager = ConfigManager(
    cache_dir="~/.modeling_llm/model_configs",  # Cache directory
    token="your_hf_token",                      # HuggingFace token for private models
    cache_expiry_days=30,                       # Cache TTL in days
    request_timeout=5,                          # Request timeout in seconds
    max_retries=2                               # Maximum retry attempts
)
```

### Environment Variables

- `HF_TOKEN`: HuggingFace API token for private models
- `HTTP_PROXY`, `HTTPS_PROXY`: Proxy settings for network requests

## Architecture Detection

The system automatically detects model architecture based on config parameters:

### MoE Models
Detected when config contains any of:
- `num_experts_per_tok` > 0
- `n_routed_experts` > 0
- `num_experts` > 0
- `num_local_experts` > 0
- `experts_per_token` > 0

### Dense Models
All other models are classified as dense transformers.

## Caching System

### Cache Behavior
- Configs are cached locally for 30 days by default
- Cache is checked first before network requests
- Expired cache is used as fallback if network fails
- Force refresh available with `force_refresh=True`

### Cache Management

```python
# Check cached config without network call
cached_config = config_manager.get_cached_config("model-name")

# Force refresh from network
fresh_config = config_manager.fetch_config("model-name", force_refresh=True)

# Clear specific model cache
config_manager.clear_cache("model-name")

# Clear all cached configs
config_manager.clear_cache()

# List cached models
cached_models = config_manager.list_cached_models()
```

## Error Handling

The API includes robust error handling:

```python
try:
    model = model_factory.create_model("some-model")
except ConfigurationError as e:
    print(f"Failed to fetch config: {e}")
    # Use fallback config or handle gracefully
```

### Common Error Scenarios
- **Network connectivity issues**: Falls back to cached config
- **Private model without token**: Raises ConfigurationError
- **Non-existent model**: Raises ConfigurationError after retries
- **Malformed config**: Raises validation errors during model creation

## Migration from Manual Configs

### Before (Manual Config)
```python
def create_manual_config():
    return {
        'hidden_size': 4096,
        'num_hidden_layers': 32,
        # ... many more parameters
    }

config = create_manual_config()
model = MoEModel("model-name", config)
```

### After (Real Config)
```python
model_factory = ModelFactory()
model_factory.register_model('moe', MoEModel)
model_factory.register_model('dense', DenseModel)

model = model_factory.create_model("model-name")  # Config fetched automatically
```

## Best Practices

### 1. Use ModelFactory for New Code
```python
# Recommended approach
model = model_factory.create_model("model-name")
```

### 2. Handle Network Dependencies
```python
try:
    model = model_factory.create_model("model-name")
except ConfigurationError:
    # Fallback to manual config for offline scenarios
    model = MoEModel("model-name", fallback_config)
```

### 3. Set Up Caching for CI/CD
```python
# Pre-populate cache in CI/CD
config_manager = ConfigManager()
for model_name in required_models:
    config_manager.fetch_config(model_name)
```

### 4. Use Environment Variables
```bash
export HF_TOKEN="your_token_here"
export HTTP_PROXY="http://proxy:8080"
```

## Supported Models

The API works with any model on HuggingFace Hub that has a `config.json` file:

### Popular MoE Models
- `deepseek-ai/DeepSeek-V3`
- `deepseek-ai/DeepSeek-V2`
- `mistralai/Mixtral-8x7B-v0.1`
- `mistralai/Mixtral-8x22B-v0.1`

### Popular Dense Models
- `meta-llama/Llama-3.1-8B`
- `meta-llama/Llama-3.1-70B`
- `Qwen/Qwen2.5-7B`
- `microsoft/DialoGPT-medium`

## Examples

See the following example scripts:
- `examples/real_config_example.py` - Comprehensive usage examples
- `examples/config_migration_guide.py` - Migration from manual configs
- `test_real_config_api.py` - Test suite demonstrating functionality

## Troubleshooting

### Config Fetch Fails
1. Check internet connectivity
2. Verify model name is correct
3. Check if model requires authentication
4. Set HF_TOKEN for private models
5. Check proxy settings if behind firewall

### Cache Issues
1. Check cache directory permissions
2. Clear cache if corrupted: `config_manager.clear_cache()`
3. Verify disk space for cache directory

### Model Creation Fails
1. Verify config was fetched successfully
2. Check if model architecture is supported
3. Register custom model types if needed
4. Check for missing required config parameters

## API Reference

### ConfigManager

#### Methods
- `fetch_config(model_name, force_refresh=False)` - Fetch and cache config
- `get_cached_config(model_name)` - Get cached config without network call
- `clear_cache(model_name=None)` - Clear cache for specific or all models
- `list_cached_models()` - List all cached model names

### ModelFactory

#### Methods
- `set_config_manager(config_manager)` - Set config manager instance
- `register_model(architecture, model_class)` - Register model type
- `create_model(model_name, config=None)` - Create model with auto config fetch
- `get_supported_architectures()` - List registered architectures

## Performance Considerations

- First config fetch requires network call (~1-5 seconds)
- Cached config access is nearly instantaneous (<0.01 seconds)
- Cache reduces network traffic and improves reliability
- Consider pre-populating cache for production deployments

## Security Notes

- HuggingFace tokens are handled securely through environment variables
- Cached configs are stored locally in user directory
- Network requests respect proxy settings
- No sensitive data is logged or exposed

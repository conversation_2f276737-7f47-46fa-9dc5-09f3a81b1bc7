# Mixed Precision Best Practices

## Overview

This document provides comprehensive best practices for implementing mixed precision in LLM deployments. These guidelines are based on empirical research, production experience, and hardware considerations.

## General Principles

### 1. Quality First Approach

Always prioritize model quality over memory savings. A model that saves 50% memory but performs poorly is not useful.

```python
# Good: Conservative approach with quality validation
config = {
    'weight_dtype': 'bf16',      # Proven stable
    'activation_dtype': 'bf16',  # Maintains quality
    'kv_cache_dtype': 'fp8',     # Safe memory savings
}

# Risky: Aggressive approach without validation
risky_config = {
    'weight_dtype': 'fp4',       # May degrade quality significantly
    'activation_dtype': 'int8',  # Potential instability
    'kv_cache_dtype': 'fp4',     # Aggressive compression
}
```

### 2. Incremental Optimization

Make changes incrementally and validate at each step.

```python
# Phase 1: Start with lowest-risk changes
phase1 = {'kv_cache_dtype': 'fp8'}

# Phase 2: Add moderate optimizations
phase2 = {**phase1, 'attention_parameter_dtype': 'fp8'}

# Phase 3: Consider aggressive optimizations only if phases 1-2 succeed
phase3 = {**phase2, 'weight_dtype': 'int8'}
```

### 3. Hardware-Aware Configuration

Choose precisions based on your target hardware capabilities.

```python
def get_hardware_optimized_config(gpu_model):
    """Get hardware-optimized mixed precision configuration."""

    if gpu_model in ['H100', 'A100']:
        # Modern GPUs with excellent FP8 support
        return {
            'weight_dtype': 'bf16',
            'activation_dtype': 'bf16',
            'kv_cache_dtype': 'fp8',
            'attention_parameter_dtype': 'fp8'
        }
    elif gpu_model in ['V100', 'T4']:
        # Older GPUs with limited FP8 support
        return {
            'weight_dtype': 'bf16',
            'activation_dtype': 'bf16',
            'kv_cache_dtype': 'fp16',
            'attention_parameter_dtype': 'bf16'
        }
    else:
        # Conservative fallback
        return {
            'weight_dtype': 'bf16',
            'activation_dtype': 'bf16',
            'kv_cache_dtype': 'bf16',
            'attention_parameter_dtype': 'bf16'
        }
```

## Component-Specific Best Practices

### Weight Precision

**Best Practices:**
- Start with BF16 for all deployments
- Consider INT8 only after extensive quality validation
- Avoid FP4 weights unless memory is extremely constrained

```python
# Recommended progression
weight_precision_progression = [
    'bf16',    # Start here - stable and widely supported
    'int8',    # Consider if memory is critical and quality validates
    'fp4'      # Last resort - requires extensive testing
]

# Quality validation for weight quantization
def validate_weight_quantization(model, test_data, target_precision):
    """Validate weight quantization impact."""

    # Baseline with BF16
    baseline_memory = model.compute_memory_requirements(weight_dtype='bf16')

    # Target precision
    target_memory = model.compute_memory_requirements(weight_dtype=target_precision)

    memory_savings = (baseline_memory['total'] - target_memory['total']) / baseline_memory['total']

    print(f"Weight quantization to {target_precision}:")
    print(f"  Memory savings: {memory_savings:.1%}")
    print(f"  Quality validation required: {'HIGH' if target_precision in ['int8', 'fp4'] else 'MEDIUM'}")

    return target_memory
```

### Activation Precision

**Best Practices:**
- Keep activations in BF16 for stability
- FP16 acceptable for memory-critical scenarios
- Avoid lower precisions for activations

```python
# Activation precision guidelines
activation_guidelines = {
    'bf16': {
        'stability': 'excellent',
        'memory_usage': 'standard',
        'recommendation': 'default choice'
    },
    'fp16': {
        'stability': 'good',
        'memory_usage': 'reduced',
        'recommendation': 'memory-constrained scenarios'
    },
    'fp8': {
        'stability': 'experimental',
        'memory_usage': 'low',
        'recommendation': 'research only'
    }
}
```

### KV Cache Precision

**Best Practices:**
- FP8 is the sweet spot for most deployments
- Provides significant memory savings with minimal quality impact
- Consider FP4 only for very long sequences

```python
def optimize_kv_cache_precision(sequence_length, memory_budget_gb):
    """Choose optimal KV cache precision based on constraints."""

    if memory_budget_gb > 40:
        return 'bf16'  # No memory pressure
    elif memory_budget_gb > 20:
        return 'fp8'   # Balanced approach
    elif sequence_length > 8192:
        return 'fp8'   # Long sequences need compression
    else:
        return 'fp4'   # Aggressive compression needed
```

### Expert Parameters (MoE Models)

**Best Practices:**
- Expert parameters are prime candidates for compression
- FP8 provides excellent memory savings for experts
- Consider FP4 for models with many experts (64+)

```python
def optimize_expert_precision(num_experts, experts_per_token):
    """Choose expert precision based on model characteristics."""

    expert_utilization = experts_per_token / num_experts

    if num_experts <= 8:
        return 'bf16'  # Small number of experts
    elif num_experts <= 32:
        return 'fp8'   # Medium number of experts
    elif expert_utilization < 0.2:  # Low utilization
        return 'fp4'   # Aggressive compression for unused experts
    else:
        return 'fp8'   # Balanced approach
```

## Training Best Practices

### Gradient Precision

**Best Practices:**
- FP16 gradients are standard for mixed precision training
- BF16 gradients for better numerical stability
- Keep optimizer states in FP32

```python
# Training configuration best practices
training_best_practices = {
    'stable_training': {
        'weight_dtype': 'bf16',
        'activation_dtype': 'bf16',
        'grad_dtype': 'bf16',        # More stable than FP16
        'optimizer_dtype': 'fp32'    # Always keep high precision
    },
    'memory_optimized_training': {
        'weight_dtype': 'bf16',
        'activation_dtype': 'bf16',
        'grad_dtype': 'fp16',        # Memory savings
        'optimizer_dtype': 'fp32'    # Stability
    },
    'experimental_training': {
        'weight_dtype': 'bf16',
        'activation_dtype': 'bf16',
        'grad_dtype': 'fp8',         # Experimental - monitor carefully
        'optimizer_dtype': 'fp16'    # Risky - may affect convergence
    }
}
```

### Training Stability Monitoring

```python
def monitor_training_stability(loss_history, gradient_norms):
    """Monitor training stability with mixed precision."""

    stability_metrics = {
        'loss_spikes': count_loss_spikes(loss_history),
        'gradient_explosions': count_gradient_explosions(gradient_norms),
        'nan_occurrences': count_nan_values(loss_history),
        'convergence_rate': calculate_convergence_rate(loss_history)
    }

    # Alert conditions
    alerts = []
    if stability_metrics['loss_spikes'] > 5:
        alerts.append("Frequent loss spikes - consider higher precision gradients")
    if stability_metrics['gradient_explosions'] > 2:
        alerts.append("Gradient explosions - increase gradient clipping or precision")
    if stability_metrics['nan_occurrences'] > 0:
        alerts.append("NaN values detected - revert to higher precision")

    return stability_metrics, alerts
```

## Quality Validation Best Practices

### Comprehensive Validation Framework

```python
class QualityValidator:
    """Comprehensive quality validation for mixed precision."""

    def __init__(self, model, test_datasets):
        self.model = model
        self.test_datasets = test_datasets
        self.baseline_metrics = None

    def establish_baseline(self, baseline_config):
        """Establish baseline metrics with high precision."""
        self.baseline_metrics = self.evaluate_config(baseline_config)
        return self.baseline_metrics

    def evaluate_config(self, config):
        """Evaluate a mixed precision configuration."""
        metrics = {}

        # Memory metrics
        memory = self.model.compute_memory_requirements(**config)
        metrics['memory_gb'] = memory['total'] / (1024**3)

        # Quality metrics (implement your specific metrics)
        metrics['perplexity'] = self.compute_perplexity(config)
        metrics['task_accuracy'] = self.compute_task_accuracy(config)
        metrics['numerical_stability'] = self.check_numerical_stability(config)

        return metrics

    def validate_config(self, config, quality_threshold=0.95):
        """Validate configuration against baseline."""
        if self.baseline_metrics is None:
            raise ValueError("Must establish baseline first")

        current_metrics = self.evaluate_config(config)

        # Quality preservation check
        quality_ratio = current_metrics['task_accuracy'] / self.baseline_metrics['task_accuracy']
        memory_savings = 1 - (current_metrics['memory_gb'] / self.baseline_metrics['memory_gb'])

        validation_result = {
            'quality_preserved': quality_ratio >= quality_threshold,
            'memory_savings': memory_savings,
            'numerical_stability': current_metrics['numerical_stability'],
            'recommendation': self.get_recommendation(quality_ratio, memory_savings)
        }

        return validation_result

    def get_recommendation(self, quality_ratio, memory_savings):
        """Get recommendation based on validation results."""
        if quality_ratio >= 0.98 and memory_savings >= 0.20:
            return "EXCELLENT - Deploy to production"
        elif quality_ratio >= 0.95 and memory_savings >= 0.15:
            return "GOOD - Consider production deployment"
        elif quality_ratio >= 0.90 and memory_savings >= 0.25:
            return "ACCEPTABLE - Monitor quality closely"
        else:
            return "POOR - Revert to higher precision"
```

### Task-Specific Validation

```python
def create_task_specific_validator(task_type):
    """Create validator for specific tasks."""

    validators = {
        'language_modeling': {
            'primary_metric': 'perplexity',
            'threshold': 0.02,  # 2% perplexity increase acceptable
            'validation_sets': ['wikitext', 'ptb']
        },
        'question_answering': {
            'primary_metric': 'exact_match',
            'threshold': 0.05,  # 5% accuracy drop acceptable
            'validation_sets': ['squad', 'natural_questions']
        },
        'code_generation': {
            'primary_metric': 'pass_at_k',
            'threshold': 0.10,  # 10% pass rate drop acceptable
            'validation_sets': ['humaneval', 'mbpp']
        },
        'reasoning': {
            'primary_metric': 'accuracy',
            'threshold': 0.03,  # 3% accuracy drop acceptable
            'validation_sets': ['gsm8k', 'math']
        }
    }

    return validators.get(task_type, validators['language_modeling'])
```

## Performance Optimization

### Memory-Performance Trade-offs

```python
def analyze_memory_performance_tradeoffs(model, configs):
    """Analyze trade-offs between memory usage and performance."""

    results = []

    for name, config in configs.items():
        memory = model.compute_memory_requirements(**config)

        # Estimate performance impact (simplified)
        performance_score = estimate_performance_impact(config)

        results.append({
            'config_name': name,
            'memory_gb': memory['total'] / (1024**3),
            'performance_score': performance_score,
            'efficiency_ratio': performance_score / (memory['total'] / (1024**3))
        })

    # Sort by efficiency ratio
    results.sort(key=lambda x: x['efficiency_ratio'], reverse=True)

    return results

def estimate_performance_impact(config):
    """Estimate relative performance impact of precision choices."""

    # Simplified performance model
    precision_performance = {
        'fp32': 0.5,   # Slowest
        'bf16': 1.0,   # Baseline
        'fp16': 1.1,   # Slightly faster
        'fp8': 1.3,    # Faster with modern hardware
        'int8': 1.5,   # Fastest
        'fp4': 1.8     # Fastest but quality concerns
    }

    # Weight different components
    weight_impact = precision_performance.get(config.get('weight_dtype', 'bf16'), 1.0)
    activation_impact = precision_performance.get(config.get('activation_dtype', 'bf16'), 1.0)

    # Weighted average
    overall_performance = weight_impact * 0.6 + activation_impact * 0.4

    return overall_performance
```

### Batch Size Optimization

```python
def optimize_batch_size_with_mixed_precision(model, config, target_memory_gb):
    """Find optimal batch size for given memory budget."""

    batch_sizes = [1, 2, 4, 8, 16, 32, 64]
    optimal_batch_size = 1

    for batch_size in batch_sizes:
        memory = model.compute_memory_requirements(
            batch_size=batch_size,
            **config
        )

        memory_gb = memory['total'] / (1024**3)

        if memory_gb <= target_memory_gb:
            optimal_batch_size = batch_size
        else:
            break

    return optimal_batch_size
```

## Production Deployment Best Practices

### Gradual Rollout Strategy

```python
class GradualRolloutManager:
    """Manage gradual rollout of mixed precision configurations."""

    def __init__(self, baseline_config, target_config):
        self.baseline_config = baseline_config
        self.target_config = target_config
        self.rollout_stages = self.create_rollout_stages()

    def create_rollout_stages(self):
        """Create progressive rollout stages."""
        return [
            {
                'name': 'canary',
                'traffic_percentage': 5,
                'duration_days': 3,
                'config': self.get_conservative_config(),
                'success_criteria': {
                    'error_rate_increase': 0.01,
                    'latency_increase': 0.05,
                    'quality_degradation': 0.02
                }
            },
            {
                'name': 'limited',
                'traffic_percentage': 25,
                'duration_days': 7,
                'config': self.get_moderate_config(),
                'success_criteria': {
                    'error_rate_increase': 0.005,
                    'latency_increase': 0.03,
                    'quality_degradation': 0.01
                }
            },
            {
                'name': 'full',
                'traffic_percentage': 100,
                'duration_days': 14,
                'config': self.target_config,
                'success_criteria': {
                    'error_rate_increase': 0.002,
                    'latency_increase': 0.02,
                    'quality_degradation': 0.005
                }
            }
        ]

    def get_conservative_config(self):
        """Get conservative configuration for initial rollout."""
        return {
            **self.baseline_config,
            'kv_cache_dtype': 'fp8'  # Only change KV cache initially
        }

    def get_moderate_config(self):
        """Get moderate configuration for limited rollout."""
        conservative = self.get_conservative_config()
        return {
            **conservative,
            'attention_parameter_dtype': 'fp8'  # Add attention compression
        }
```

### Monitoring and Alerting

```python
def setup_production_monitoring():
    """Set up comprehensive monitoring for mixed precision deployment."""

    monitoring_config = {
        'memory_alerts': {
            'gpu_memory_usage_high': {
                'threshold': 0.90,
                'severity': 'warning'
            },
            'memory_leak_detected': {
                'threshold': 'increasing_trend',
                'severity': 'critical'
            }
        },
        'performance_alerts': {
            'latency_regression': {
                'threshold': 0.10,  # 10% increase
                'severity': 'warning'
            },
            'throughput_degradation': {
                'threshold': 0.15,  # 15% decrease
                'severity': 'critical'
            }
        },
        'quality_alerts': {
            'accuracy_drop': {
                'threshold': 0.02,  # 2% drop
                'severity': 'critical'
            },
            'perplexity_increase': {
                'threshold': 0.05,  # 5% increase
                'severity': 'warning'
            }
        },
        'stability_alerts': {
            'nan_values_detected': {
                'threshold': 1,  # Any NaN
                'severity': 'critical'
            },
            'numerical_instability': {
                'threshold': 'pattern_detected',
                'severity': 'critical'
            }
        }
    }

    return monitoring_config
```

## Common Pitfalls and How to Avoid Them

### Pitfall 1: Aggressive Optimization Without Validation

**Problem**: Applying aggressive quantization without proper quality validation.

**Solution**:
```python
# Wrong approach
aggressive_config = {
    'weight_dtype': 'fp4',
    'activation_dtype': 'int8',
    'kv_cache_dtype': 'fp4'
}
# Deploy without validation - DANGEROUS

# Right approach
def safe_optimization_pipeline(model, target_config):
    """Safe optimization with validation at each step."""

    current_config = {'dtype': 'bf16'}  # Start conservative

    for component, target_precision in target_config.items():
        # Test single component change
        test_config = current_config.copy()
        test_config[component] = target_precision

        # Validate
        if validate_configuration(model, test_config):
            current_config = test_config
            print(f"✅ Successfully optimized {component} to {target_precision}")
        else:
            print(f"❌ Failed to optimize {component} to {target_precision}")
            break

    return current_config
```

### Pitfall 2: Ignoring Hardware Limitations

**Problem**: Using precisions not well-supported by target hardware.

**Solution**:
```python
def hardware_aware_config(gpu_model, target_config):
    """Adjust configuration based on hardware capabilities."""

    hardware_support = {
        'H100': ['fp32', 'bf16', 'fp16', 'fp8', 'int8', 'fp4'],
        'A100': ['fp32', 'bf16', 'fp16', 'fp8', 'int8'],
        'V100': ['fp32', 'bf16', 'fp16', 'int8'],
        'T4': ['fp32', 'bf16', 'fp16', 'int8']
    }

    supported = hardware_support.get(gpu_model, ['fp32', 'bf16', 'fp16'])

    # Adjust config to supported precisions
    adjusted_config = {}
    for component, precision in target_config.items():
        if precision in supported:
            adjusted_config[component] = precision
        else:
            # Fallback to closest supported precision
            adjusted_config[component] = find_closest_supported(precision, supported)
            print(f"⚠️  {component} precision adjusted from {precision} to {adjusted_config[component]}")

    return adjusted_config
```

### Pitfall 3: Insufficient Quality Monitoring

**Problem**: Not monitoring quality metrics in production.

**Solution**:
```python
class ContinuousQualityMonitor:
    """Continuous quality monitoring for production deployment."""

    def __init__(self, baseline_metrics):
        self.baseline_metrics = baseline_metrics
        self.quality_history = []

    def log_quality_metrics(self, current_metrics):
        """Log current quality metrics."""
        self.quality_history.append({
            'timestamp': datetime.now(),
            'metrics': current_metrics
        })

        # Check for degradation
        self.check_quality_degradation(current_metrics)

    def check_quality_degradation(self, current_metrics):
        """Check for quality degradation and alert if necessary."""

        for metric_name, current_value in current_metrics.items():
            baseline_value = self.baseline_metrics.get(metric_name)

            if baseline_value is not None:
                degradation = (baseline_value - current_value) / baseline_value

                if degradation > 0.02:  # 2% degradation threshold
                    self.trigger_alert(metric_name, degradation)

    def trigger_alert(self, metric_name, degradation):
        """Trigger quality degradation alert."""
        print(f"🚨 QUALITY ALERT: {metric_name} degraded by {degradation:.1%}")
        # Implement your alerting mechanism here
```

## Advanced Optimization Techniques

### Dynamic Precision Adjustment

```python
class DynamicPrecisionManager:
    """Dynamically adjust precision based on runtime conditions."""

    def __init__(self, model):
        self.model = model
        self.precision_configs = self.create_precision_tiers()
        self.current_tier = 'balanced'

    def create_precision_tiers(self):
        """Create different precision tiers for different conditions."""
        return {
            'quality_critical': {
                'weight_dtype': 'bf16',
                'activation_dtype': 'bf16',
                'kv_cache_dtype': 'bf16',
                'attention_parameter_dtype': 'bf16'
            },
            'balanced': {
                'weight_dtype': 'bf16',
                'activation_dtype': 'bf16',
                'kv_cache_dtype': 'fp8',
                'attention_parameter_dtype': 'fp8'
            },
            'memory_optimized': {
                'weight_dtype': 'int8',
                'activation_dtype': 'bf16',
                'kv_cache_dtype': 'fp8',
                'attention_parameter_dtype': 'fp8'
            }
        }

    def adjust_precision_based_on_load(self, current_memory_usage, quality_score):
        """Adjust precision based on current system conditions."""

        if current_memory_usage > 0.90:  # High memory pressure
            if quality_score > 0.95:  # Quality is good, can optimize more
                self.current_tier = 'memory_optimized'
            else:
                self.current_tier = 'balanced'
        elif quality_score < 0.90:  # Quality issues
            self.current_tier = 'quality_critical'
        else:
            self.current_tier = 'balanced'

        return self.precision_configs[self.current_tier]
```

### Model-Specific Optimization

```python
def get_model_specific_recommendations(model_architecture, model_size):
    """Get model-specific mixed precision recommendations."""

    recommendations = {
        'llama': {
            'small': {  # < 7B parameters
                'weight_dtype': 'bf16',
                'kv_cache_dtype': 'fp8',
                'notes': 'Conservative approach for smaller models'
            },
            'medium': {  # 7B - 70B parameters
                'weight_dtype': 'bf16',
                'kv_cache_dtype': 'fp8',
                'attention_parameter_dtype': 'fp8',
                'notes': 'Balanced optimization'
            },
            'large': {  # > 70B parameters
                'weight_dtype': 'int8',
                'kv_cache_dtype': 'fp8',
                'attention_parameter_dtype': 'fp8',
                'notes': 'Aggressive optimization needed'
            }
        },
        'moe': {
            'any_size': {
                'weight_dtype': 'bf16',
                'expert_parameter_dtype': 'fp8',
                'kv_cache_dtype': 'fp8',
                'attention_parameter_dtype': 'bf16',
                'notes': 'Focus on expert parameter compression'
            }
        }
    }

    size_category = 'small' if model_size < 7e9 else 'medium' if model_size < 70e9 else 'large'

    return recommendations.get(model_architecture, {}).get(size_category, {
        'weight_dtype': 'bf16',
        'activation_dtype': 'bf16',
        'notes': 'Conservative fallback'
    })
```

## Conclusion

Mixed precision optimization is a powerful technique for improving LLM deployment efficiency, but it requires careful planning, systematic validation, and ongoing monitoring. Follow these best practices to achieve optimal results:

1. **Start Conservative**: Begin with low-risk optimizations
2. **Validate Thoroughly**: Never skip quality validation
3. **Monitor Continuously**: Set up comprehensive monitoring
4. **Be Hardware-Aware**: Choose precisions your hardware supports well
5. **Plan for Rollback**: Always have a rollback strategy
6. **Document Everything**: Keep detailed records of configurations and results

Remember: The goal is not maximum compression, but optimal balance between memory efficiency, performance, and quality for your specific use case.

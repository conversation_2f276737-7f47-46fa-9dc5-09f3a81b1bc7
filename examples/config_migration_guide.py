"""
Migration guide showing how to update from manual configs to real cached configs.

This script demonstrates the before/after patterns for using the updated API.
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm_modeling_metrics.core.model_factory import ModelFactory
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel


def show_old_pattern():
    """Show the old pattern with manual config creation."""

    print("=== OLD PATTERN (Manual Config) ===")
    print()

    # Old way: Manual config creation
    def create_manual_deepseek_config():
        return {
            "model_type": "deepseek_v3",
            "architectures": ["DeepseekV3ForCausalLM"],
            "hidden_size": 7168,
            "intermediate_size": 18432,
            "num_hidden_layers": 61,
            "num_attention_heads": 128,
            "num_key_value_heads": 128,
            "vocab_size": 129280,
            "max_position_embeddings": 163840,
            "tie_word_embeddings": False,
            "rms_norm_eps": 1e-6,
            "num_experts_per_tok": 8,
            "n_routed_experts": 256,
            "n_shared_experts": 1,
            "moe_intermediate_size": 2048,
            "moe_layer_freq": 1,
            "first_k_dense_replace": 3,
            "kv_lora_rank": 512,
            "q_lora_rank": 1536,
            "qk_rope_head_dim": 64,
            "v_head_dim": 128,
            "qk_nope_head_dim": 128,
            "use_cache": True,
            "pad_token_id": 0,
            "bos_token_id": 0,
            "eos_token_id": 1,
        }

    print("# Old way - manual config creation")
    print("config = create_manual_deepseek_config()")
    print("model = MoEModel('deepseek-ai/DeepSeek-V3', config)")
    print()

    # Actually create with old pattern
    config = create_manual_deepseek_config()
    model = MoEModel("deepseek-ai/DeepSeek-V3", config)

    print("Problems with old pattern:")
    print("❌ Manual config maintenance required")
    print("❌ Risk of outdated or incorrect parameters")
    print("❌ No automatic updates when model changes")
    print("❌ Duplication across different scripts")
    print("❌ Hard to ensure accuracy")
    print()

    return model


def show_new_pattern():
    """Show the new pattern with real config fetching."""

    print("=== NEW PATTERN (Real Config Fetching) ===")
    print()

    print("# New way - automatic config fetching")
    print("model_factory = ModelFactory()")
    print("model_factory.register_model('moe', MoEModel)")
    print("model_factory.register_model('dense', DenseModel)")
    print("model = model_factory.create_model('deepseek-ai/DeepSeek-V3')")
    print()

    # Actually create with new pattern
    try:
        model_factory = ModelFactory()
        model_factory.register_model("moe", MoEModel)
        model_factory.register_model("dense", DenseModel)
        model = model_factory.create_model("deepseek-ai/DeepSeek-V3")

        print("Benefits of new pattern:")
        print("✅ Automatic config fetching from HuggingFace")
        print("✅ Always up-to-date with latest model parameters")
        print("✅ Local caching for performance")
        print("✅ Automatic architecture detection")
        print("✅ Fallback handling for network issues")
        print("✅ No manual config maintenance")
        print()

        return model

    except Exception as e:
        print(f"Note: Real config fetch failed ({e})")
        print("This is expected without internet/HF access")
        print()
        return None


def show_alternative_patterns():
    """Show alternative patterns for different use cases."""

    print("=== ALTERNATIVE PATTERNS ===")
    print()

    # Pattern 1: Manual config manager
    print("Pattern 1: Manual ConfigManager usage")
    print("config_manager = ConfigManager()")
    print("config = config_manager.fetch_config('model-name')")
    print("model = MoEModel('model-name', config)")
    print()

    # Pattern 2: With custom cache settings
    print("Pattern 2: Custom cache settings")
    print("config_manager = ConfigManager(")
    print("    cache_dir='/custom/cache/path',")
    print("    cache_expiry_days=7,")
    print("    token='your_hf_token'")
    print(")")
    print("model_factory.set_config_manager(config_manager)")
    print()

    # Pattern 3: Force refresh
    print("Pattern 3: Force refresh config")
    print("config = config_manager.fetch_config('model-name', force_refresh=True)")
    print()

    # Pattern 4: Check cache first
    print("Pattern 4: Check cache without network call")
    print("cached_config = config_manager.get_cached_config('model-name')")
    print("if cached_config:")
    print("    model = MoEModel('model-name', cached_config)")
    print("else:")
    print("    # Fetch from network or use fallback")
    print()


def show_migration_checklist():
    """Show a checklist for migrating existing code."""

    print("=== MIGRATION CHECKLIST ===")
    print()

    print("Step 1: Replace manual config functions")
    print("  ❌ Remove: create_model_config() functions")
    print("  ✅ Add: ConfigManager and ModelFactory setup")
    print()

    print("Step 2: Update model creation")
    print("  ❌ Old: model = MoEModel(name, manual_config)")
    print("  ✅ New: model = model_factory.create_model(name)")
    print()

    print("Step 3: Handle network dependencies")
    print("  ✅ Add: try/except blocks for config fetching")
    print("  ✅ Add: fallback configs for offline testing")
    print("  ✅ Consider: caching strategy for CI/CD")
    print()

    print("Step 4: Update tests")
    print("  ✅ Use real configs in integration tests")
    print("  ✅ Keep manual configs only for unit tests")
    print("  ✅ Add tests for config fetching/caching")
    print()

    print("Step 5: Environment setup")
    print("  ✅ Set HF_TOKEN for private models")
    print("  ✅ Configure proxy settings if needed")
    print("  ✅ Set up cache directory permissions")
    print()


def demonstrate_comparison():
    """Compare results between old and new patterns."""

    print("=== COMPARISON ===")
    print()

    old_model = show_old_pattern()
    new_model = show_new_pattern()

    if new_model:
        print("Comparing results:")

        old_params = old_model.get_total_params()
        new_params = new_model.get_total_params()

        print(f"Old pattern total params: {old_params / 1e9:.2f}B")
        print(f"New pattern total params: {new_params / 1e9:.2f}B")

        if abs(old_params - new_params) / old_params < 0.01:  # Within 1%
            print("✅ Results are consistent")
        else:
            print("⚠️  Results differ - real config may have updates")

        print()

        # Compare some key config values
        old_config = old_model.config
        new_config = new_model.config

        key_params = ["hidden_size", "num_hidden_layers", "num_experts_per_tok"]
        print("Key parameter comparison:")
        for param in key_params:
            old_val = old_config.get(param, "N/A")
            new_val = new_config.get(param, "N/A")
            status = "✅" if old_val == new_val else "⚠️"
            print(f"  {param}: {old_val} vs {new_val} {status}")

        print()


if __name__ == "__main__":
    print("LLM Modeling Metrics - Config Migration Guide")
    print("=" * 60)
    print()

    demonstrate_comparison()
    show_alternative_patterns()
    show_migration_checklist()

    print("=== SUMMARY ===")
    print()
    print("The new API provides:")
    print("• Automatic config fetching and caching")
    print("• Always up-to-date model parameters")
    print("• Reduced maintenance burden")
    print("• Better accuracy and reliability")
    print()
    print("Migration is straightforward:")
    print("1. Replace manual config creation with ModelFactory")
    print("2. Add error handling for network dependencies")
    print("3. Keep fallback configs for offline scenarios")
    print()
    print("For more examples, see examples/real_config_example.py")

"""
Example demonstrating roofline analysis with the RooflineService.

This example shows how to:
1. Load hardware specifications
2. Generate roofline data for multiple hardware platforms
3. Plot operators on roofline models
4. Compare hardware performance characteristics
5. Generate optimization recommendations
"""

import sys
from pathlib import Path

import numpy as np

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from llm_modeling_metrics.core.operators import MatMulOperator
from llm_modeling_metrics.hardware.roofline_service import RooflineService
from llm_modeling_metrics.hardware.service import HardwareService


def demonstrate_roofline_analysis():
    """Demonstrate comprehensive roofline analysis."""
    print("=== Roofline Analysis Example ===\n")

    # Initialize services
    try:
        hardware_service = HardwareService()
        roofline_service = RooflineService()
    except FileNotFoundError:
        print("Error: GPU specifications file not found.")
        print("Please ensure gpu/data/gpu_specs.yaml exists.")
        return

    # Get available hardware
    print("1. Loading available hardware...")
    available_hardware = hardware_service.get_available_hardware()

    print(f"   Found {len(available_hardware.get('gpu', []))} GPUs")
    print(f"   Found {len(available_hardware.get('npu', []))} NPUs")

    # Select hardware for analysis
    hardware_specs = []
    if available_hardware.get("gpu"):
        # Select first few GPUs for demonstration
        hardware_specs.extend(available_hardware["gpu"][:3])
    if available_hardware.get("npu"):
        # Add an NPU if available
        hardware_specs.extend(available_hardware["npu"][:1])

    if not hardware_specs:
        print("No hardware specifications found.")
        return

    print(f"   Selected {len(hardware_specs)} hardware platforms for analysis:")
    for hw in hardware_specs:
        print(f"     - {hw.name} ({hw.type.value})")

    # Create sample operators representing common LLM operations
    print("\n2. Creating sample operators...")
    operators = [
        MatMulOperator(M=32, N=4096, K=4096, precision="bf16"),  # Attention projection
        MatMulOperator(M=32, N=11008, K=4096, precision="bf16"),  # MLP up projection
        MatMulOperator(M=32, N=4096, K=11008, precision="bf16"),  # MLP down projection
    ]

    # Give operators descriptive names
    operators[0].name = "attention_proj"
    operators[1].name = "mlp_up"
    operators[2].name = "mlp_down"

    print(f"   Created {len(operators)} operators:")
    for op in operators:
        flops = op.compute_flops(batch_size=32, sequence_length=2048)
        memory_bytes = op.compute_memory_movement_bytes(
            batch_size=32, sequence_length=2048
        )
        oi = flops / memory_bytes if memory_bytes > 0 else 0
        print(f"     - {op.name}: {flops/1e9:.1f} GFLOPS, OI={oi:.1f} FLOP/Byte")

    # Generate roofline data
    print("\n3. Generating roofline data...")
    precisions = ["fp16", "bf16", "fp8"]
    roofline_data = roofline_service.generate_roofline_data(hardware_specs, precisions)

    print(
        f"   Generated roofline curves for {len(roofline_data.hardware_specs)} hardware platforms"
    )
    print(
        f"   Operational intensity range: {roofline_data.operational_intensity_range[0]:.2f} to {roofline_data.operational_intensity_range[-1]:.0f} FLOP/Byte"
    )

    # Display knee points
    print("\n4. Knee point analysis:")
    for hw_id, knee_points in roofline_data.knee_points.items():
        hw_name = roofline_data.hardware_specs[hw_id].name
        print(f"   {hw_name}:")
        for precision, knee_point in knee_points.items():
            print(
                f"     - {precision}: OI={knee_point.operational_intensity:.1f} FLOP/Byte, "
                f"Peak={knee_point.performance_tflops:.0f} TFLOPS"
            )

    # Compare hardware rooflines with operators
    print("\n5. Comparing hardware performance...")
    comparison_data = roofline_service.compare_hardware_rooflines(
        hardware_specs, operators, batch_size=32, sequence_length=2048
    )

    # Display performance rankings
    print("\n   Performance rankings by operator:")
    for operator_name, rankings in comparison_data.performance_rankings.items():
        print(f"   {operator_name}:")
        for i, hw_id in enumerate(rankings):
            hw_name = roofline_data.hardware_specs[hw_id].name
            plot_data = comparison_data.roofline_data[hw_id]

            # Find operator point for this hardware
            op_point = None
            for point in plot_data.operator_points:
                if point.operator_name == operator_name:
                    op_point = point
                    break

            if op_point:
                print(
                    f"     {i+1}. {hw_name}: {op_point.achieved_performance_tflops:.1f} TFLOPS "
                    f"({op_point.utilization_percent:.1f}% util, "
                    f"{'compute' if op_point.is_compute_bound else 'memory'} bound)"
                )

    # Display recommendations
    print("\n6. Hardware recommendations:")
    for i, recommendation in enumerate(comparison_data.recommendations, 1):
        print(f"   {i}. {recommendation}")

    # Analyze individual hardware with operators
    print("\n7. Detailed analysis for best performing hardware...")
    best_hw_id = comparison_data.hardware_platforms[0]  # Assuming first is best overall
    best_hw_spec = roofline_data.hardware_specs[best_hw_id]

    plot_data = roofline_service.plot_operators_on_roofline(
        operators, best_hw_spec, batch_size=32, sequence_length=2048
    )

    print(f"   Analysis for {plot_data.hardware_name}:")
    print(f"   Available precisions: {list(plot_data.roofline_curves.keys())}")

    print("\n   Operator analysis:")
    for op_point in plot_data.operator_points:
        bottleneck = "compute-bound" if op_point.is_compute_bound else "memory-bound"
        print(f"     - {op_point.operator_name}:")
        print(
            f"       * Operational Intensity: {op_point.operational_intensity:.1f} FLOP/Byte"
        )
        print(
            f"       * Achieved Performance: {op_point.achieved_performance_tflops:.1f} TFLOPS"
        )
        print(f"       * Utilization: {op_point.utilization_percent:.1f}%")
        print(f"       * Bottleneck: {bottleneck}")

    # Generate simple visualization data
    print("\n8. Generating visualization data...")
    print("   (In a real application, this would create interactive plots)")

    # Show roofline curve characteristics
    for hw_id, curves in roofline_data.performance_curves.items():
        hw_name = roofline_data.hardware_specs[hw_id].name
        print(f"\n   {hw_name} roofline characteristics:")

        for precision, curve in curves.items():
            max_perf = np.max(curve)
            min_perf = np.min(curve)
            print(f"     - {precision}: {min_perf:.2f} to {max_perf:.0f} TFLOPS")

    print("\n=== Analysis Complete ===")
    print("\nThis example demonstrated:")
    print("- Loading hardware specifications")
    print("- Generating roofline models for multiple hardware platforms")
    print("- Calculating knee points for compute/memory transitions")
    print("- Plotting operators on roofline models")
    print("- Comparing performance across hardware platforms")
    print("- Generating optimization recommendations")
    print("\nIn a real application, this data would be used to create")
    print("interactive visualizations and detailed performance reports.")


def demonstrate_simple_roofline():
    """Demonstrate simple roofline generation for a single hardware."""
    print("\n=== Simple Roofline Example ===\n")

    try:
        hardware_service = HardwareService()
        roofline_service = RooflineService()
    except FileNotFoundError:
        print("Error: GPU specifications file not found.")
        return

    # Get first available GPU
    available_hardware = hardware_service.get_available_hardware()
    if not available_hardware.get("gpu"):
        print("No GPU specifications found.")
        return

    gpu_spec = available_hardware["gpu"][0]
    print(f"Analyzing: {gpu_spec.name}")

    # Calculate knee points for different precisions
    precisions = ["fp32", "fp16", "bf16", "fp8"]
    print("\nKnee points:")

    for precision in precisions:
        if gpu_spec.supports_precision(precision):
            knee_point = roofline_service.calculate_knee_points(gpu_spec, precision)
            if knee_point:
                print(
                    f"  {precision}: {knee_point.operational_intensity:.1f} FLOP/Byte @ {knee_point.performance_tflops:.0f} TFLOPS"
                )

    # Generate roofline data
    roofline_data = roofline_service.generate_roofline_data([gpu_spec], precisions)

    print(
        f"\nGenerated roofline curves for {len(roofline_data.performance_curves[gpu_spec.id])} precisions"
    )
    print("Roofline analysis complete!")


if __name__ == "__main__":
    # Run the comprehensive example
    demonstrate_roofline_analysis()

    # Run the simple example
    demonstrate_simple_roofline()

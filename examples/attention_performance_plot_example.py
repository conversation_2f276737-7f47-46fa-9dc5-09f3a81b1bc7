#!/usr/bin/env python3
"""
Example usage of the attention performance plot API endpoint.

This example demonstrates how to use the /api/attention/performance-plot endpoint
to generate comprehensive plot data for attention mechanism analysis.
"""

import asyncio
import json
from datetime import datetime

from llm_modeling_metrics.web.app import get_attention_performance_plot
from llm_modeling_metrics.web.models import AttentionPerformancePlotRequest


async def example_attention_performance_plot():
    """Example of using the attention performance plot endpoint."""
    
    print("Attention Performance Plot API Example")
    print("=" * 50)
    
    # Create a request for multiple models and hardware configurations
    request = AttentionPerformancePlotRequest(
        model_names=["DSv3", "Qwen3", "Step3"],  # DeepSeek V3, Qwen3 MoE, Step-3
        sequence_lengths=[8192, 16384, 24576, 32768],  # 8K to 32K context
        hardware_names=["H100", "A800", "H20", "910B"],  # Multiple GPU types
        batch_size=1,
        flash_attention=True,
        precision="bf16",
        include_metadata=True
    )
    
    print(f"Request configuration:")
    print(f"  Models: {request.model_names}")
    print(f"  Sequence lengths: {request.sequence_lengths}")
    print(f"  Hardware: {request.hardware_names}")
    print(f"  Precision: {request.precision}")
    print(f"  FlashAttention: {request.flash_attention}")
    
    try:
        # Call the endpoint
        response = await get_attention_performance_plot(
            request=request,
            client_ip="127.0.0.1",
            authenticated=True
        )
        
        print(f"\n✅ Analysis completed successfully!")
        print(f"Execution time: {response.execution_time:.3f} seconds")
        print(f"Timestamp: {response.timestamp}")
        
        # Display results summary
        print(f"\nResults Summary:")
        print(f"  Hardware configurations: {len(response.hardware_limits)}")
        print(f"  Model trajectories: {len(response.model_trajectories)}")
        print(f"  Metadata included: {response.metadata is not None}")
        
        # Show hardware roofline data
        print(f"\nHardware Roofline Data:")
        for hw_name, hw_data in response.hardware_limits.items():
            mem_points = len(hw_data["memory_access_gb"])
            compute_points = len(hw_data["compute_gflops"])
            print(f"  {hw_name}: {mem_points} memory points, {compute_points} compute points")
        
        # Show model trajectory data
        print(f"\nModel Trajectory Data:")
        for model_name, model_data in response.model_trajectories.items():
            attention_type = model_data.get("attention_mechanism", "Unknown")
            data_points = len(model_data.get("sequence_lengths", []))
            mem_range = model_data.get("memory_access_gb", [])
            compute_range = model_data.get("compute_gflops", [])
            
            if mem_range and compute_range:
                mem_min, mem_max = min(mem_range), max(mem_range)
                compute_min, compute_max = min(compute_range), max(compute_range)
                print(f"  {model_name} ({attention_type}):")
                print(f"    Data points: {data_points}")
                print(f"    Memory access: {mem_min:.2f} - {mem_max:.2f} GB")
                print(f"    Compute: {compute_min:.1f} - {compute_max:.1f} GFLOPS")
        
        # Show plot configuration
        if "plot_config" in response.plot_data:
            plot_config = response.plot_data["plot_config"]
            print(f"\nPlot Configuration:")
            print(f"  Title: {plot_config.get('title', 'N/A')}")
            print(f"  Precision: {plot_config.get('precision', 'N/A')}")
            print(f"  FlashAttention: {plot_config.get('flash_attention', 'N/A')}")
            print(f"  Batch size: {plot_config.get('batch_size', 'N/A')}")
        
        # Show axis configuration
        if "axis_config" in response.plot_data:
            axis_config = response.plot_data["axis_config"]
            print(f"\nAxis Configuration:")
            print(f"  X-axis: {axis_config.get('x_label', 'N/A')} {axis_config.get('x_range', 'N/A')}")
            print(f"  Y-axis: {axis_config.get('y_label', 'N/A')} {axis_config.get('y_range', 'N/A')}")
        
        # Show metadata if available
        if response.metadata:
            metadata = response.metadata
            stats = metadata.get("statistics", {})
            print(f"\nAnalysis Statistics:")
            print(f"  Models analyzed: {stats.get('models_analyzed', 'N/A')}")
            print(f"  Hardware configs: {stats.get('hardware_configs', 'N/A')}")
            print(f"  Total data points: {stats.get('total_data_points', 'N/A')}")
        
        print(f"\n✅ Example completed successfully!")
        
        # Example of how to use the data for plotting (pseudo-code)
        print(f"\nExample usage for frontend plotting:")
        print(f"```javascript")
        print(f"// Hardware roofline curves")
        print(f"const hardwareCurves = response.hardware_limits;")
        print(f"// Model performance trajectories")
        print(f"const modelTrajectories = response.model_trajectories;")
        print(f"// Plot configuration")
        print(f"const plotConfig = response.plot_data.plot_config;")
        print(f"const axisConfig = response.plot_data.axis_config;")
        print(f"```")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        if hasattr(e, 'status_code') and hasattr(e, 'detail'):
            print(f"HTTP Status: {e.status_code}")
            print(f"Detail: {e.detail}")
        return False


if __name__ == "__main__":
    success = asyncio.run(example_attention_performance_plot())
    print(f"\nExample {'completed successfully' if success else 'failed'}!")
"""
Mixed Precision Optimization Example

This example demonstrates how to systematically optimize mixed precision configurations
for different deployment scenarios. It shows how to analyze trade-offs between
memory usage, performance, and model quality.
"""

import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), ".."))


from llm_modeling_metrics.models.dense_model import <PERSON><PERSON>Model


def create_llama_config():
    """Create a Llama-style model configuration."""
    return {
        "hidden_size": 4096,
        "num_hidden_layers": 32,
        "num_attention_heads": 32,
        "num_key_value_heads": 32,
        "intermediate_size": 11008,
        "vocab_size": 32000,
        "max_position_embeddings": 2048,
        "model_type": "llama",
        "tie_word_embeddings": False,
        "rms_norm_eps": 1e-6,
    }


def format_bytes(bytes_value):
    """Format bytes in human readable format."""
    for unit in ["B", "KB", "MB", "GB", "TB"]:
        if bytes_value < 1024.0:
            return f"{bytes_value:.2f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.2f} PB"


def estimate_quality_impact(config):
    """
    Estimate relative quality impact based on precision choices.
    This is a simplified heuristic - real quality should be measured empirically.
    """
    precision_quality = {
        "fp32": 1.0,
        "bf16": 0.98,
        "fp16": 0.96,
        "fp8": 0.90,
        "int8": 0.85,
        "fp4": 0.75,
    }

    # Weight quality impact more heavily
    weight_impact = precision_quality.get(config.get("weight_dtype", "bf16"), 0.98)
    activation_impact = precision_quality.get(
        config.get("activation_dtype", "bf16"), 0.98
    )
    kv_cache_impact = precision_quality.get(config.get("kv_cache_dtype", "bf16"), 0.98)

    # Weighted average (weights matter most, then activations, then KV cache)
    overall_quality = (
        weight_impact * 0.5 + activation_impact * 0.3 + kv_cache_impact * 0.2
    )
    return overall_quality


def main():
    print("=== Mixed Precision Optimization Example ===\n")

    # Create model instance
    config = create_llama_config()
    model = DenseModel("llama-7b", config)

    # Analysis parameters
    sequence_length = 2048
    batch_size = 1

    print(f"Model: Llama-7B")
    print(f"Sequence length: {sequence_length}")
    print(f"Batch size: {batch_size}")
    print()

    # Define precision options for systematic exploration
    precision_options = ["bf16", "fp8", "int8"]
    kv_cache_options = ["bf16", "fp8"]

    # 1. Systematic Configuration Exploration
    print("=== 1. Systematic Configuration Exploration ===")
    print(
        f"{'Weight':<6} {'Activation':<10} {'KV Cache':<8} {'Memory (GB)':<12} {'Savings':<8} {'Quality':<8}"
    )
    print("-" * 60)

    baseline_memory = None
    best_configs = []

    for weight_dtype in precision_options:
        for activation_dtype in precision_options:
            for kv_cache_dtype in kv_cache_options:
                try:
                    memory = model.compute_memory_requirements(
                        sequence_length=sequence_length,
                        batch_size=batch_size,
                        weight_dtype=weight_dtype,
                        activation_dtype=activation_dtype,
                        kv_cache_dtype=kv_cache_dtype,
                        include_kv_cache=True,
                    )

                    if baseline_memory is None:
                        baseline_memory = model.compute_memory_requirements(
                            sequence_length=sequence_length,
                            batch_size=batch_size,
                            dtype="bf16",
                            include_kv_cache=True,
                        )

                    total_gb = memory["total"] / (1024**3)
                    savings = (
                        (baseline_memory["total"] - memory["total"])
                        / baseline_memory["total"]
                    ) * 100

                    config_dict = {
                        "weight_dtype": weight_dtype,
                        "activation_dtype": activation_dtype,
                        "kv_cache_dtype": kv_cache_dtype,
                    }
                    quality = estimate_quality_impact(config_dict)

                    print(
                        f"{weight_dtype:<6} {activation_dtype:<10} {kv_cache_dtype:<8} {total_gb:>8.2f} {savings:>8.1f}% {quality:>8.2f}"
                    )

                    # Track promising configurations
                    if (
                        savings > 10 and quality > 0.85
                    ):  # Good savings with acceptable quality
                        best_configs.append(
                            {
                                "config": config_dict,
                                "memory_gb": total_gb,
                                "savings_percent": savings,
                                "quality_score": quality,
                                "efficiency_score": savings
                                * quality,  # Combined metric
                            }
                        )

                except Exception as e:
                    print(
                        f"{weight_dtype:<6} {activation_dtype:<10} {kv_cache_dtype:<8} {'ERROR':<12} {'N/A':<8} {'N/A':<8}"
                    )

    print()

    # 2. Top Configurations Analysis
    print("=== 2. Top Configurations by Efficiency ===")
    best_configs.sort(key=lambda x: x["efficiency_score"], reverse=True)

    print(
        f"{'Rank':<4} {'Configuration':<25} {'Memory':<10} {'Savings':<8} {'Quality':<8} {'Efficiency':<10}"
    )
    print("-" * 75)

    for i, config_data in enumerate(best_configs[:5], 1):
        config = config_data["config"]
        config_str = f"{config['weight_dtype']}/{config['activation_dtype']}/{config['kv_cache_dtype']}"
        print(
            f"{i:<4} {config_str:<25} {config_data['memory_gb']:>6.2f} GB {config_data['savings_percent']:>6.1f}% "
            f"{config_data['quality_score']:>6.2f} {config_data['efficiency_score']:>8.1f}"
        )

    print()

    # 3. Scenario-Based Recommendations
    print("=== 3. Scenario-Based Recommendations ===")

    scenarios = [
        {
            "name": "Production Inference (Quality Priority)",
            "weight_priority": 0.4,
            "memory_priority": 0.3,
            "quality_priority": 0.3,
            "min_quality": 0.95,
        },
        {
            "name": "Production Inference (Balanced)",
            "weight_priority": 0.3,
            "memory_priority": 0.4,
            "quality_priority": 0.3,
            "min_quality": 0.90,
        },
        {
            "name": "Edge Deployment (Memory Priority)",
            "weight_priority": 0.2,
            "memory_priority": 0.6,
            "quality_priority": 0.2,
            "min_quality": 0.85,
        },
        {
            "name": "Research/Development",
            "weight_priority": 0.2,
            "memory_priority": 0.2,
            "quality_priority": 0.6,
            "min_quality": 0.98,
        },
    ]

    for scenario in scenarios:
        print(f"\n{scenario['name']}:")

        # Filter configs by minimum quality
        valid_configs = [
            c for c in best_configs if c["quality_score"] >= scenario["min_quality"]
        ]

        if not valid_configs:
            print("  No configurations meet quality requirements")
            continue

        # Score configs based on scenario priorities
        for config_data in valid_configs:
            memory_score = config_data["savings_percent"] / 100  # Normalize to 0-1
            quality_score = config_data["quality_score"]

            scenario_score = (
                scenario["memory_priority"] * memory_score
                + scenario["quality_priority"] * quality_score
            )
            config_data["scenario_score"] = scenario_score

        # Get best config for this scenario
        best_config = max(valid_configs, key=lambda x: x["scenario_score"])
        config = best_config["config"]

        print(
            f"  Recommended: {config['weight_dtype']}/{config['activation_dtype']}/{config['kv_cache_dtype']}"
        )
        print(
            f"  Memory: {best_config['memory_gb']:.2f} GB ({best_config['savings_percent']:.1f}% savings)"
        )
        print(f"  Quality Score: {best_config['quality_score']:.2f}")

    print()

    # 4. Memory Breakdown Analysis
    print("=== 4. Memory Breakdown Analysis ===")

    # Compare memory breakdown for different configurations
    configs_to_analyze = [
        {"name": "Baseline", "dtype": "bf16"},
        {
            "name": "Recommended",
            "weight_dtype": "bf16",
            "activation_dtype": "bf16",
            "kv_cache_dtype": "fp8",
        },
        {
            "name": "Aggressive",
            "weight_dtype": "int8",
            "activation_dtype": "bf16",
            "kv_cache_dtype": "fp8",
        },
    ]

    print(f"{'Component':<15} {'Baseline':<12} {'Recommended':<12} {'Aggressive':<12}")
    print("-" * 55)

    memories = []
    for config in configs_to_analyze:
        if "dtype" in config:
            memory = model.compute_memory_requirements(
                sequence_length=sequence_length,
                batch_size=batch_size,
                dtype=config["dtype"],
                include_kv_cache=True,
            )
        else:
            memory = model.compute_memory_requirements(
                sequence_length=sequence_length,
                batch_size=batch_size,
                weight_dtype=config["weight_dtype"],
                activation_dtype=config["activation_dtype"],
                kv_cache_dtype=config["kv_cache_dtype"],
                include_kv_cache=True,
            )
        memories.append(memory)

    components = ["parameters", "activations", "kv_cache", "total"]
    for component in components:
        values = [
            mem.get(component, 0) / (1024**3) for mem in memories
        ]  # Convert to GB
        print(
            f"{component.title():<15} {values[0]:>8.2f} GB {values[1]:>8.2f} GB {values[2]:>8.2f} GB"
        )

    print()

    # 5. Performance Considerations
    print("=== 5. Performance Considerations ===")
    print("• Lower precision generally improves throughput but may reduce quality")
    print("• FP8 provides good balance between memory savings and quality")
    print(
        "• INT8 weights offer significant memory savings but require careful calibration"
    )
    print(
        "• KV cache precision has minimal quality impact but significant memory impact"
    )
    print(
        "• Consider hardware support: not all GPUs support all precisions efficiently"
    )
    print()
    print("Hardware-specific recommendations:")
    print("• H100/A100: FP8 and BF16 are well supported")
    print("• V100/T4: Focus on FP16 and INT8 optimizations")
    print("• Edge devices: Aggressive quantization (INT8/FP4) may be necessary")
    print()

    # 6. Migration Strategy
    print("=== 6. Migration Strategy ===")
    print("1. Start with conservative mixed precision (BF16 weights, FP8 KV cache)")
    print("2. Measure quality impact on your specific tasks/datasets")
    print("3. Gradually increase quantization if quality remains acceptable")
    print("4. Monitor inference speed and memory usage in production")
    print("5. Consider A/B testing different precision configurations")
    print()
    print("Quality validation checklist:")
    print("• Run evaluation on representative test sets")
    print("• Compare perplexity/loss metrics")
    print("• Test on downstream tasks specific to your use case")
    print("• Monitor for numerical instability or convergence issues")
    print()


if __name__ == "__main__":
    main()

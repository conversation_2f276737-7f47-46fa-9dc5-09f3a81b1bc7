"""
Example demonstrating how to use real cached/fetched configs with MoEModel and DenseModel.

This example shows the updated API that fetches real model configurations from HuggingFace
instead of using manually created configs.
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm_modeling_metrics.core.config_manager import Config<PERSON>anager
from llm_modeling_metrics.core.model_factory import ModelFactory
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel


def demonstrate_real_config_api():
    """Demonstrate the updated API using real cached/fetched configs."""

    print("=== Real Config API Demonstration ===")
    print()

    # Initialize model factory (default models are registered automatically)
    model_factory = ModelFactory()

    # Test models to analyze
    test_models = [
        {
            "name": "deepseek-ai/DeepSeek-V3",
            "type": "MoE",
            "description": "DeepSeek V3 - Large MoE model with 671B total, 37B active parameters",
        },
        {
            "name": "Qwen/Qwen2.5-7B",
            "type": "Dense",
            "description": "Qwen 2.5 7B - Standard dense transformer model",
        },
        {
            "name": "meta-llama/Llama-3.1-8B",
            "type": "Dense",
            "description": "Llama 3.1 8B - Popular dense model",
        },
    ]

    for model_info in test_models:
        model_name = model_info["name"]
        expected_type = model_info["type"]
        description = model_info["description"]

        print(f"=== {model_name} ===")
        print(f"Description: {description}")
        print(f"Expected Type: {expected_type}")
        print()

        try:
            # Method 1: Use ModelFactory (recommended - automatic architecture detection)
            print(
                "Method 1: Using ModelFactory (automatic config fetch + architecture detection)"
            )
            model = model_factory.create_model(model_name)

            # Alternative: Use convenience function (even simpler)
            # from llm_modeling_metrics.core.model_factory import create_model
            # model = create_model(model_name)

            print(f"✅ Successfully created {type(model).__name__}")
            print(
                f"   Architecture detected: {model_factory._detect_architecture(model_name, model.config)}"
            )
            print(f"   Model type: {model.config.get('model_type', 'unknown')}")
            print(f"   Hidden size: {model.config.get('hidden_size', 'unknown')}")
            print(f"   Layers: {model.config.get('num_hidden_layers', 'unknown')}")

            # Show key metrics
            total_params = model.get_total_params()
            print(f"   Total parameters: {total_params / 1e9:.2f}B")

            if isinstance(model, MoEModel):
                active_params = model.compute_active_params_per_token()
                efficiency = (active_params / total_params) * 100
                print(f"   Active parameters: {active_params / 1e9:.2f}B")
                print(f"   Parameter efficiency: {efficiency:.1f}%")

            print()

            # Method 2: Manual config fetch + model creation
            print("Method 2: Manual config fetch + explicit model creation")
            config_manager = ConfigManager()
            config = config_manager.fetch_config(model_name)

            # Determine model type and create explicitly
            if any(
                config.get(key, 0) > 0
                for key in ["num_experts_per_tok", "n_routed_experts", "num_experts"]
            ):
                manual_model = MoEModel(model_name, config)
                print(f"✅ Created MoEModel manually")
            else:
                manual_model = DenseModel(model_name, config)
                print(f"✅ Created DenseModel manually")

            print(f"   Same config: {model.config == manual_model.config}")
            print()

            # Method 3: Check cached config without fetching
            print("Method 3: Check cached config (no network call)")
            cached_config = config_manager.get_cached_config(model_name)
            if cached_config:
                print(f"✅ Config is cached locally")
                print(f"   Cached config matches: {config == cached_config}")
            else:
                print("ℹ️  Config not in cache (would require network fetch)")

            print()

        except Exception as e:
            print(f"❌ Error processing {model_name}: {e}")
            print()

        print("-" * 80)
        print()

    # Demonstrate cache management
    print("=== Cache Management ===")
    print()

    # List cached models
    cached_models = config_manager.list_cached_models()
    print(f"Cached models ({len(cached_models)}):")
    for cached_model in cached_models[:5]:  # Show first 5
        print(f"  - {cached_model}")
    if len(cached_models) > 5:
        print(f"  ... and {len(cached_models) - 5} more")
    print()

    # Show cache directory
    print(f"Cache directory: {config_manager.cache_dir}")
    print(f"Cache expiry: {config_manager.cache_expiry_days} days")
    print()

    # Demonstrate force refresh
    if cached_models:
        example_model = cached_models[0]
        print(f"Force refresh example for {example_model}:")
        try:
            fresh_config = config_manager.fetch_config(
                example_model, force_refresh=True
            )
            print(f"✅ Force refreshed config for {example_model}")
        except Exception as e:
            print(f"❌ Failed to force refresh: {e}")

    print()
    print("=== Key Benefits of Real Config API ===")
    print("✅ Automatic config fetching from HuggingFace")
    print("✅ Local caching with TTL (30 days default)")
    print("✅ Automatic architecture detection (MoE vs Dense)")
    print("✅ Fallback to cached config if network fails")
    print("✅ Support for private models with HF tokens")
    print("✅ Retry logic with exponential backoff")
    print("✅ No need to manually maintain config dictionaries")
    print()


def demonstrate_config_comparison():
    """Compare real config vs manual config for accuracy."""

    print("=== Config Accuracy Comparison ===")
    print()

    model_name = "deepseek-ai/DeepSeek-V3"

    try:
        # Get real config
        config_manager = ConfigManager()
        real_config = config_manager.fetch_config(model_name)

        print("Real config key parameters:")
        key_params = [
            "hidden_size",
            "num_hidden_layers",
            "num_attention_heads",
            "intermediate_size",
            "vocab_size",
            "num_experts_per_tok",
            "n_routed_experts",
            "n_shared_experts",
            "moe_intermediate_size",
        ]

        for param in key_params:
            value = real_config.get(param, "NOT_FOUND")
            print(f"  {param}: {value}")

        print()

        # Create model with real config
        model = MoEModel(model_name, real_config)
        real_total_params = model.get_total_params()
        real_active_params = model.compute_active_params_per_token()

        print(f"Real config results:")
        print(f"  Total parameters: {real_total_params / 1e9:.2f}B")
        print(f"  Active parameters: {real_active_params / 1e9:.2f}B")
        print(
            f"  Parameter efficiency: {(real_active_params / real_total_params) * 100:.1f}%"
        )

        print()
        print(
            "✅ Using real config ensures accuracy and stays up-to-date with model changes"
        )

    except Exception as e:
        print(f"❌ Could not fetch real config: {e}")
        print(
            "This is expected if you don't have internet access or HF token for private models"
        )


if __name__ == "__main__":
    demonstrate_real_config_api()
    print()
    demonstrate_config_comparison()

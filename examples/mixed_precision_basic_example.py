"""
Basic Mixed Precision Example

This example demonstrates how to use mixed precision support in the LLM modeling metrics system.
It shows how to specify different precisions for different model components and analyze
the memory savings and performance implications.
"""

import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from llm_modeling_metrics.models.dense_model import DenseModel


def create_llama_config():
    """Create a Llama-style model configuration."""
    return {
        "hidden_size": 4096,
        "num_hidden_layers": 32,
        "num_attention_heads": 32,
        "num_key_value_heads": 32,
        "intermediate_size": 11008,
        "vocab_size": 32000,
        "max_position_embeddings": 2048,
        "model_type": "llama",
        "tie_word_embeddings": False,
        "rms_norm_eps": 1e-6,
    }


def format_bytes(bytes_value):
    """Format bytes in human readable format."""
    for unit in ["B", "KB", "MB", "GB", "TB"]:
        if bytes_value < 1024.0:
            return f"{bytes_value:.2f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.2f} PB"


def main():
    print("=== Mixed Precision Basic Example ===\n")

    # Create model instance
    config = create_llama_config()
    model = DenseModel("llama-7b", config)

    # Analysis parameters
    sequence_length = 2048
    batch_size = 1

    print(f"Model: Llama-7B")
    print(f"Sequence length: {sequence_length}")
    print(f"Batch size: {batch_size}")
    print()

    # 1. Baseline: All BF16 (traditional approach)
    print("=== 1. Baseline: All BF16 ===")
    baseline_memory = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        dtype="bf16",  # Legacy parameter - applies to all components
        include_kv_cache=True,
    )

    print(f"Total memory: {format_bytes(baseline_memory['total'])}")
    print(f"Parameters: {format_bytes(baseline_memory['parameters'])}")
    print(f"Activations: {format_bytes(baseline_memory['activations'])}")
    print(f"KV Cache: {format_bytes(baseline_memory['kv_cache'])}")
    print()

    # 2. Mixed Precision: Optimized configuration
    print("=== 2. Mixed Precision: Optimized ===")
    mixed_memory = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype="bf16",  # Keep weights in BF16 for quality
        activation_dtype="bf16",  # Keep activations in BF16 for quality
        kv_cache_dtype="fp8",  # Use FP8 for KV cache to save memory
        attention_parameter_dtype="bf16",  # Keep attention params in BF16
        include_kv_cache=True,
    )

    print(f"Total memory: {format_bytes(mixed_memory['total'])}")
    print(f"Parameters: {format_bytes(mixed_memory['parameters'])}")
    print(f"Activations: {format_bytes(mixed_memory['activations'])}")
    print(f"KV Cache: {format_bytes(mixed_memory['kv_cache'])}")

    # Calculate savings
    memory_savings = baseline_memory["total"] - mixed_memory["total"]
    savings_percent = (memory_savings / baseline_memory["total"]) * 100
    print(f"Memory savings: {format_bytes(memory_savings)} ({savings_percent:.1f}%)")
    print()

    # 3. Aggressive Mixed Precision: Maximum memory savings
    print("=== 3. Aggressive Mixed Precision: Maximum Savings ===")
    aggressive_memory = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype="int8",  # Quantize weights to INT8
        activation_dtype="bf16",  # Keep activations in BF16 for stability
        kv_cache_dtype="fp8",  # Use FP8 for KV cache
        attention_parameter_dtype="fp8",  # Use FP8 for attention params
        include_kv_cache=True,
    )

    print(f"Total memory: {format_bytes(aggressive_memory['total'])}")
    print(f"Parameters: {format_bytes(aggressive_memory['parameters'])}")
    print(f"Activations: {format_bytes(aggressive_memory['activations'])}")
    print(f"KV Cache: {format_bytes(aggressive_memory['kv_cache'])}")

    # Calculate savings
    aggressive_savings = baseline_memory["total"] - aggressive_memory["total"]
    aggressive_percent = (aggressive_savings / baseline_memory["total"]) * 100
    print(
        f"Memory savings: {format_bytes(aggressive_savings)} ({aggressive_percent:.1f}%)"
    )
    print()

    # 4. Training configuration with mixed precision
    print("=== 4. Training Configuration ===")
    training_memory = model.compute_memory_requirements(
        sequence_length=sequence_length,
        batch_size=batch_size,
        weight_dtype="bf16",  # Weights in BF16
        activation_dtype="bf16",  # Activations in BF16
        grad_dtype="fp16",  # Gradients in FP16 to save memory
        optimizer_dtype="fp32",  # Optimizer states in FP32 for stability
        training=True,
        include_kv_cache=False,  # No KV cache during training
    )

    print(f"Total memory: {format_bytes(training_memory['total'])}")
    print(f"Parameters: {format_bytes(training_memory['parameters'])}")
    print(f"Activations: {format_bytes(training_memory['activations'])}")
    print(f"Gradients: {format_bytes(training_memory['gradients'])}")
    print(f"Optimizer: {format_bytes(training_memory['optimizer'])}")
    print()

    # 5. Comparison table
    print("=== 5. Configuration Comparison ===")
    print(
        f"{'Configuration':<20} {'Total Memory':<15} {'Savings':<15} {'Quality Impact':<15}"
    )
    print("-" * 65)

    baseline_gb = baseline_memory["total"] / (1024**3)
    mixed_gb = mixed_memory["total"] / (1024**3)
    aggressive_gb = aggressive_memory["total"] / (1024**3)
    training_gb = training_memory["total"] / (1024**3)

    print(f"{'Baseline (BF16)':<20} {baseline_gb:>10.2f} GB {'':<15} {'Highest':<15}")
    print(
        f"{'Optimized Mixed':<20} {mixed_gb:>10.2f} GB {savings_percent:>10.1f}% {'Minimal':<15}"
    )
    print(
        f"{'Aggressive Mixed':<20} {aggressive_gb:>10.2f} GB {aggressive_percent:>10.1f}% {'Moderate':<15}"
    )
    print(f"{'Training Mixed':<20} {training_gb:>10.2f} GB {'N/A':<15} {'Good':<15}")
    print()

    # 6. Recommendations
    print("=== 6. Recommendations ===")
    print("• Inference (Quality Priority): Use optimized mixed precision")
    print("  - weight_dtype='bf16', activation_dtype='bf16', kv_cache_dtype='fp8'")
    print()
    print("• Inference (Memory Priority): Use aggressive mixed precision")
    print("  - weight_dtype='int8', activation_dtype='bf16', kv_cache_dtype='fp8'")
    print()
    print("• Training: Use mixed precision with FP32 optimizer")
    print("  - weight_dtype='bf16', grad_dtype='fp16', optimizer_dtype='fp32'")
    print()
    print("• Production Deployment: Start with optimized, monitor quality metrics")
    print()


if __name__ == "__main__":
    main()

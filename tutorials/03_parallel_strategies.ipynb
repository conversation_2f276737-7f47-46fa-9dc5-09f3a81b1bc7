{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Parallel Strategies Analysis Tutorial\n", "\n", "This tutorial explores different parallelization strategies for LLMs, including tensor parallelism, pipeline parallelism, and their combinations.\n", "\n", "## Understanding Parallelism in LLMs\n", "\n", "- **Tensor Parallelism (TP)**: Splits individual operations across multiple devices\n", "- **Pipeline Parallelism (PP)**: Splits layers across multiple devices\n", "- **Data Parallelism (DP)**: Replicates the model across devices with different data batches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import os\n", "from llm_modeling_metrics import (\n", "    ModelFactory,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    ParallelStrategyCalculator,\n", ")\n", "from llm_modeling_metrics.comparison import Comparator\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Set up plotting\n", "plt.style.use(\"default\")\n", "plt.rcParams[\"figure.figsize\"] = (12, 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"HF_TOKEN\"] = \"*************************************\"\n", "\n", "os.environ[\"http_proxy\"] = \"http://127.0.0.1:7890\"\n", "os.environ[\"https_proxy\"] = os.environ[\"http_proxy\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load Test Models\n", "\n", "We'll analyze parallelism strategies on different model sizes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["self.config={'vocab_size': 32000, 'max_position_embeddings': 4096, 'hidden_size': 4096, 'intermediate_size': 11008, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-05, 'pretraining_tp': 1, 'use_cache': True, 'rope_theta': 10000.0, 'rope_scaling': None, 'attention_bias': False, 'attention_dropout': 0.0, 'mlp_bias': False, 'head_dim': 128, 'return_dict': True, 'output_hidden_states': False, 'torchscript': False, 'torch_dtype': 'float16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['LlamaForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 1, 'pad_token_id': None, 'eos_token_id': 2, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': 'meta-llama/Llama-2-7b-hf', 'transformers_version': '4.53.0', 'model_type': 'llama', 'output_attentions': False}\n", "self._parsed_config={'hidden_size': 4096, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'intermediate_size': 11008, 'vocab_size': 32000, 'max_position_embeddings': 4096, 'model_type': 'llama', 'tie_word_embeddings': False, 'rms_norm_eps': 1e-05}\n", "Loaded model: meta-llama/Llama-2-7b-hf\n", "Model type: DenseModel\n", "\n", "Base Model Stats:\n", "Total Parameters: 6,738,149,376\n", "Parameter Memory: 12.55 GB\n"]}], "source": ["# Test models of different sizes\n", "test_models = [\n", "    \"meta-llama/Llama-2-7b-hf\",\n", "    # Add more models as needed\n", "]\n", "\n", "# Load the first model for detailed analysis\n", "model_name = test_models[0]\n", "model = ModelFactory.create_model(model_name)\n", "\n", "print(f\"Loaded model: {model_name}\")\n", "print(f\"Model type: {type(model).__name__}\")\n", "\n", "# Get base metrics\n", "base_metrics = model.get_metrics(sequence_length=2048)\n", "print(f\"\\nBase Model Stats:\")\n", "print(f\"Total Parameters: {base_metrics.total_params:,}\")\n", "print(f\"Parameter Memory: {base_metrics.memory_params / (1024**3):.2f} GB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON>sor Parallelism Analysis\n", "\n", "Let's analyze how tensor parallelism affects matrix shapes and memory usage."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Tensor Parallelism Analysis ===\n", "TP size 1: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "TP size 2: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "TP size 4: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "TP size 8: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "TP size 16: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "No valid tensor parallel configurations found\n"]}], "source": ["# Test different tensor parallel sizes\n", "tp_sizes = [1, 2, 4, 8, 16]\n", "tp_results = []\n", "\n", "print(\"=== Tensor Parallelism Analysis ===\")\n", "\n", "for tp_size in tp_sizes:\n", "    try:\n", "        parallel_config = ParallelConfig(tensor_parallel_size=tp_size)\n", "\n", "        # Validate configuration first\n", "        is_valid = ParallelStrategyCalculator.validate_parallel_config(\n", "            parallel_config, model.config.__dict__\n", "        )\n", "\n", "        if not is_valid:\n", "            print(\n", "                f\"TP size {tp_size}: Invalid configuration (dimensions not divisible)\"\n", "            )\n", "            continue\n", "\n", "        # Get metrics with this configuration\n", "        metrics = model.get_metrics(\n", "            sequence_length=2048, parallel_config=parallel_config\n", "        )\n", "        shapes = model.get_matrix_shapes(parallel_config)\n", "\n", "        # Extract key shapes for analysis\n", "        attention_shapes = shapes.get(\"attention\", {})\n", "        mlp_shapes = shapes.get(\"mlp\", {})\n", "\n", "        tp_results.append(\n", "            {\n", "                \"tp_size\": tp_size,\n", "                \"memory_activations_gb\": metrics.memory_activations / (1024**3),\n", "                \"memory_params_gb\": metrics.memory_params / (1024**3),\n", "                \"flops_forward\": metrics.flops_forward,\n", "                \"q_proj_shape\": str(attention_shapes.get(\"q_proj\", \"N/A\")),\n", "                \"gate_proj_shape\": str(mlp_shapes.get(\"gate_proj\", \"N/A\")),\n", "                \"valid\": True,\n", "            }\n", "        )\n", "\n", "        print(f\"TP size {tp_size}: ✓ Valid\")\n", "\n", "    except Exception as e:\n", "        print(f\"TP size {tp_size}: ✗ Failed - {e}\")\n", "        tp_results.append({\"tp_size\": tp_size, \"valid\": False, \"error\": str(e)})\n", "\n", "# Create DataFrame with valid results\n", "valid_results = [r for r in tp_results if r.get(\"valid\", False)]\n", "if valid_results:\n", "    tp_df = pd.DataFrame(valid_results)\n", "    print(\"\\n=== Valid Tensor Parallel Configurations ===\")\n", "    display_cols = [\n", "        \"tp_size\",\n", "        \"memory_activations_gb\",\n", "        \"memory_params_gb\",\n", "        \"q_proj_shape\",\n", "    ]\n", "    print(tp_df[display_cols].to_string(index=False))\n", "else:\n", "    print(\"No valid tensor parallel configurations found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Memory Scaling Analysis\n", "\n", "Let's visualize how memory requirements scale with parallelism."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No valid results to plot\n"]}], "source": ["if valid_results:\n", "    # Plot memory scaling\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "    # Activation memory scaling\n", "    ax1.plot(\n", "        tp_df[\"tp_size\"],\n", "        tp_df[\"memory_activations_gb\"],\n", "        \"bo-\",\n", "        linewidth=2,\n", "        markersize=8,\n", "    )\n", "    ax1.set_xlabel(\"Tensor Parallel Size\")\n", "    ax1.set_ylabel(\"Activation Memory per Device (GB)\")\n", "    ax1.set_title(\"Activation Memory Scaling with Tensor Parallelism\")\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_yscale(\"log\")\n", "\n", "    # Parameter memory scaling\n", "    ax2.plot(\n", "        tp_df[\"tp_size\"], tp_df[\"memory_params_gb\"], \"ro-\", linewidth=2, markersize=8\n", "    )\n", "    ax2.set_xlabel(\"Tensor Parallel Size\")\n", "    ax2.set_ylabel(\"Parameter Memory per Device (GB)\")\n", "    ax2.set_title(\"Parameter Memory Scaling with Tensor Parallelism\")\n", "    ax2.grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # Calculate memory reduction\n", "    base_activation_memory = tp_df.iloc[0][\"memory_activations_gb\"]\n", "    base_param_memory = tp_df.iloc[0][\"memory_params_gb\"]\n", "\n", "    print(\"\\n=== Memory Reduction Analysis ===\")\n", "    for _, row in tp_df.iterrows():\n", "        tp_size = row[\"tp_size\"]\n", "        activation_reduction = base_activation_memory / row[\"memory_activations_gb\"]\n", "        param_reduction = base_param_memory / row[\"memory_params_gb\"]\n", "\n", "        print(\n", "            f\"TP={tp_size}: Activation {activation_reduction:.1f}x reduction, \"\n", "            f\"Parameters {param_reduction:.1f}x reduction\"\n", "        )\n", "else:\n", "    print(\"No valid results to plot\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Matrix Shape Analysis\n", "\n", "Let's examine how tensor parallelism affects matrix shapes in detail."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No valid results for shape analysis\n"]}], "source": ["# Detailed shape analysis for different TP sizes\n", "if valid_results:\n", "    print(\"=== Matrix Shape Analysis ===\")\n", "\n", "    # Get shapes for a few TP sizes\n", "    analysis_tp_sizes = (\n", "        [1, 2, 4]\n", "        if len(valid_results) >= 3\n", "        else [r[\"tp_size\"] for r in valid_results[:3]]\n", "    )\n", "\n", "    shape_analysis = {}\n", "\n", "    for tp_size in analysis_tp_sizes:\n", "        parallel_config = ParallelConfig(tensor_parallel_size=tp_size)\n", "        shapes = model.get_matrix_shapes(parallel_config)\n", "\n", "        print(f\"\\n--- TP Size: {tp_size} ---\")\n", "\n", "        # Attention shapes\n", "        print(\"Attention Layer Shapes:\")\n", "        attention_shapes = shapes.get(\"attention\", {})\n", "        for name, shape in attention_shapes.items():\n", "            print(f\"  {name}: {shape}\")\n", "\n", "        # MLP shapes\n", "        print(\"MLP Layer Shapes:\")\n", "        mlp_shapes = shapes.get(\"mlp\", {})\n", "        for name, shape in mlp_shapes.items():\n", "            print(f\"  {name}: {shape}\")\n", "\n", "        shape_analysis[tp_size] = {\"attention\": attention_shapes, \"mlp\": mlp_shapes}\n", "\n", "    # Analyze how specific dimensions change\n", "    print(\"\\n=== Dimension Scaling Analysis ===\")\n", "\n", "    # Track how key dimensions scale\n", "    for component in [\"attention\", \"mlp\"]:\n", "        print(f\"\\n{component.title()} Component:\")\n", "\n", "        # Get all unique shape names across TP sizes\n", "        all_shape_names = set()\n", "        for tp_size in analysis_tp_sizes:\n", "            all_shape_names.update(shape_analysis[tp_size][component].keys())\n", "\n", "        for shape_name in sorted(all_shape_names):\n", "            print(f\"  {shape_name}:\")\n", "            for tp_size in analysis_tp_sizes:\n", "                shape = shape_analysis[tp_size][component].get(shape_name, \"N/A\")\n", "                print(f\"    TP={tp_size}: {shape}\")\n", "else:\n", "    print(\"No valid results for shape analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Communication Volume Analysis\n", "\n", "Tensor parallelism introduces communication overhead. Let's analyze this."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No valid results for communication analysis\n"]}], "source": ["# Analyze communication requirements\n", "if valid_results:\n", "    print(\"=== Communication Volume Analysis ===\")\n", "\n", "    comm_results = []\n", "\n", "    for tp_size in analysis_tp_sizes:\n", "        parallel_config = ParallelConfig(tensor_parallel_size=tp_size)\n", "\n", "        try:\n", "            # Calculate communication volume\n", "            comm_volume = ParallelStrategyCalculator.compute_communication_volume(\n", "                model, parallel_config\n", "            )\n", "\n", "            total_comm = sum(comm_volume.values())\n", "\n", "            comm_results.append(\n", "                {\n", "                    \"tp_size\": tp_size,\n", "                    \"total_communication_mb\": total_comm / (1024**2),\n", "                    \"allreduce_mb\": comm_volume.get(\"allreduce\", 0) / (1024**2),\n", "                    \"allgather_mb\": comm_volume.get(\"allgather\", 0) / (1024**2),\n", "                }\n", "            )\n", "\n", "            print(f\"TP={tp_size}: Total communication {total_comm/(1024**2):.2f} MB\")\n", "            for comm_type, volume in comm_volume.items():\n", "                print(f\"  {comm_type}: {volume/(1024**2):.2f} MB\")\n", "\n", "        except Exception as e:\n", "            print(f\"TP={tp_size}: Communication analysis failed - {e}\")\n", "\n", "    if comm_results:\n", "        comm_df = pd.DataFrame(comm_results)\n", "\n", "        # Plot communication overhead\n", "        plt.figure(figsize=(10, 6))\n", "        plt.plot(\n", "            comm_df[\"tp_size\"],\n", "            comm_df[\"total_communication_mb\"],\n", "            \"go-\",\n", "            linewidth=2,\n", "            markersize=8,\n", "            label=\"Total Communication\",\n", "        )\n", "\n", "        if \"allreduce_mb\" in comm_df.columns:\n", "            plt.plot(\n", "                comm_df[\"tp_size\"],\n", "                comm_df[\"allreduce_mb\"],\n", "                \"r^-\",\n", "                linewidth=2,\n", "                markersize=6,\n", "                label=\"AllReduce\",\n", "            )\n", "\n", "        if \"allgather_mb\" in comm_df.columns:\n", "            plt.plot(\n", "                comm_df[\"tp_size\"],\n", "                comm_df[\"allgather_mb\"],\n", "                \"bs-\",\n", "                linewidth=2,\n", "                markersize=6,\n", "                label=\"AllGather\",\n", "            )\n", "\n", "        plt.xlabel(\"Tensor Parallel Size\")\n", "        plt.ylabel(\"Communication Volume (MB)\")\n", "        plt.title(\"Communication Overhead vs Tensor Parallelism\")\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        plt.show()\n", "else:\n", "    print(\"No valid results for communication analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON>ine Parallelism Analysis\n", "\n", "Now let's explore pipeline parallelism strategies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Pipeline Parallelism Analysis ===\n", "Model has unknown layers\n", "Cannot determine number of layers for pipeline analysis\n"]}], "source": ["# Analyze pipeline parallelism\n", "print(\"=== Pipeline Parallelism Analysis ===\")\n", "\n", "# Get model configuration to understand layer count\n", "num_layers = getattr(\n", "    model.config, \"num_hidden_layers\", getattr(model.config, \"num_layers\", \"unknown\")\n", ")\n", "print(f\"Model has {num_layers} layers\")\n", "\n", "if isinstance(num_layers, int):\n", "    # Test different pipeline parallel sizes\n", "    pp_sizes = [1, 2, 4]\n", "    # Only test PP sizes that divide evenly into the number of layers\n", "    valid_pp_sizes = [pp for pp in pp_sizes if num_layers % pp == 0]\n", "\n", "    print(f\"Valid PP sizes (divide evenly into {num_layers} layers): {valid_pp_sizes}\")\n", "\n", "    pp_results = []\n", "\n", "    for pp_size in valid_pp_sizes:\n", "        parallel_config = ParallelConfig(pipeline_parallel_size=pp_size)\n", "\n", "        try:\n", "            metrics = model.get_metrics(\n", "                sequence_length=2048, parallel_config=parallel_config\n", "            )\n", "\n", "            layers_per_stage = num_layers // pp_size\n", "\n", "            pp_results.append(\n", "                {\n", "                    \"pp_size\": pp_size,\n", "                    \"layers_per_stage\": layers_per_stage,\n", "                    \"memory_params_gb\": metrics.memory_params / (1024**3),\n", "                    \"memory_activations_gb\": metrics.memory_activations / (1024**3),\n", "                    \"flops_forward\": metrics.flops_forward,\n", "                }\n", "            )\n", "\n", "            print(\n", "                f\"PP={pp_size}: {layers_per_stage} layers per stage, \"\n", "                f\"Param memory: {metrics.memory_params/(1024**3):.2f} GB per stage\"\n", "            )\n", "\n", "        except Exception as e:\n", "            print(f\"PP={pp_size}: Failed - {e}\")\n", "\n", "    if pp_results:\n", "        pp_df = pd.DataFrame(pp_results)\n", "\n", "        # Plot pipeline parallelism effects\n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "        # Memory per stage\n", "        ax1.bar(pp_df[\"pp_size\"], pp_df[\"memory_params_gb\"], alpha=0.7, color=\"skyblue\")\n", "        ax1.set_xlabel(\"Pipeline Parallel Size\")\n", "        ax1.set_ylabel(\"Parameter Memory per Stage (GB)\")\n", "        ax1.set_title(\"Memory Distribution with Pipeline Parallelism\")\n", "        ax1.grid(True, alpha=0.3)\n", "\n", "        # Layers per stage\n", "        ax2.bar(\n", "            pp_df[\"pp_size\"], pp_df[\"layers_per_stage\"], alpha=0.7, color=\"lightcoral\"\n", "        )\n", "        ax2.set_xlabel(\"Pipeline Parallel Size\")\n", "        ax2.set_ylabel(\"Layers per Stage\")\n", "        ax2.set_title(\"Layer Distribution with Pipeline Parallelism\")\n", "        ax2.grid(True, alpha=0.3)\n", "\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "        print(\"\\n=== Pipeline Parallelism Results ===\")\n", "        print(pp_df.to_string(index=False))\n", "    else:\n", "        print(\"No valid pipeline parallelism configurations\")\n", "else:\n", "    print(\"Cannot determine number of layers for pipeline analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Hybrid Parallelism Strategies\n", "\n", "Let's explore combinations of tensor and pipeline parallelism."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Hybrid Parallelism Analysis ===\n", "TP=1, PP=1: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "TP=2, PP=1: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "TP=1, PP=2: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "TP=2, PP=2: ✗ Failed - 'dict' object has no attribute '__dict__'\n", "No valid hybrid configurations found\n"]}], "source": ["# Test hybrid parallelism strategies\n", "print(\"=== Hybrid Parallelism Analysis ===\")\n", "\n", "# Define hybrid configurations to test\n", "hybrid_configs = [\n", "    ParallelConfig(tensor_parallel_size=1, pipeline_parallel_size=1),  # Baseline\n", "    ParallelConfig(tensor_parallel_size=2, pipeline_parallel_size=1),  # TP only\n", "    ParallelConfig(tensor_parallel_size=1, pipeline_parallel_size=2),  # PP only\n", "    ParallelConfig(tensor_parallel_size=2, pipeline_parallel_size=2),  # Hybrid\n", "]\n", "\n", "hybrid_results = []\n", "\n", "for config in hybrid_configs:\n", "    try:\n", "        # Validate configuration\n", "        is_valid = ParallelStrategyCalculator.validate_parallel_config(\n", "            config, model.config.__dict__\n", "        )\n", "\n", "        if not is_valid:\n", "            print(\n", "                f\"TP={config.tensor_parallel_size}, PP={config.pipeline_parallel_size}: Invalid\"\n", "            )\n", "            continue\n", "\n", "        metrics = model.get_metrics(sequence_length=2048, parallel_config=config)\n", "\n", "        total_devices = config.tensor_parallel_size * config.pipeline_parallel_size\n", "\n", "        hybrid_results.append(\n", "            {\n", "                \"tp_size\": config.tensor_parallel_size,\n", "                \"pp_size\": config.pipeline_parallel_size,\n", "                \"total_devices\": total_devices,\n", "                \"memory_params_gb\": metrics.memory_params / (1024**3),\n", "                \"memory_activations_gb\": metrics.memory_activations / (1024**3),\n", "                \"total_memory_gb\": (metrics.memory_params + metrics.memory_activations)\n", "                / (1024**3),\n", "                \"flops_forward\": metrics.flops_forward,\n", "            }\n", "        )\n", "\n", "        print(\n", "            f\"TP={config.tensor_parallel_size}, PP={config.pipeline_parallel_size}: \"\n", "            f\"✓ Valid, {total_devices} devices, \"\n", "            f\"{(metrics.memory_params + metrics.memory_activations)/(1024**3):.2f} GB per device\"\n", "        )\n", "\n", "    except Exception as e:\n", "        print(\n", "            f\"TP={config.tensor_parallel_size}, PP={config.pipeline_parallel_size}: ✗ Failed - {e}\"\n", "        )\n", "\n", "if hybrid_results:\n", "    hybrid_df = pd.DataFrame(hybrid_results)\n", "\n", "    print(\"\\n=== Hybrid Parallelism Comparison ===\")\n", "    display_cols = [\"tp_size\", \"pp_size\", \"total_devices\", \"total_memory_gb\"]\n", "    print(hybrid_df[display_cols].to_string(index=False))\n", "\n", "    # Create a heatmap-style visualization\n", "    fig, ax = plt.subplots(figsize=(10, 8))\n", "\n", "    # Create a pivot table for visualization\n", "    pivot_data = hybrid_df.pivot(\n", "        index=\"pp_size\", columns=\"tp_size\", values=\"total_memory_gb\"\n", "    )\n", "\n", "    im = ax.imshow(pivot_data.values, cmap=\"YlOrRd\", aspect=\"auto\")\n", "\n", "    # Set ticks and labels\n", "    ax.set_xticks(range(len(pivot_data.columns)))\n", "    ax.set_yticks(range(len(pivot_data.index)))\n", "    ax.set_xticklabels(pivot_data.columns)\n", "    ax.set_yticklabels(pivot_data.index)\n", "\n", "    ax.set_xlabel(\"Tensor Parallel Size\")\n", "    ax.set_ylabel(\"Pipeline Parallel Size\")\n", "    ax.set_title(\"Memory per Device (GB) - Hybrid Parallelism\")\n", "\n", "    # Add text annotations\n", "    for i in range(len(pivot_data.index)):\n", "        for j in range(len(pivot_data.columns)):\n", "            value = pivot_data.iloc[i, j]\n", "            if not pd.isna(value):\n", "                ax.text(\n", "                    j,\n", "                    i,\n", "                    f\"{value:.1f}\",\n", "                    ha=\"center\",\n", "                    va=\"center\",\n", "                    color=\"white\" if value > pivot_data.values.max() / 2 else \"black\",\n", "                )\n", "\n", "    plt.colorbar(im, ax=ax, label=\"Memory per Device (GB)\")\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"No valid hybrid configurations found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Optimal Strategy Recommendation\n", "\n", "Based on our analysis, let's recommend optimal parallelism strategies."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Parallelism Strategy Recommendations ===\n", "\n", "=== General Guidelines ===\n", "\n", "🔸 Tensor Parallelism (TP):\n", "   - Best for: Memory-bound models, reducing memory per device\n", "   - Considerations: Requires high-bandwidth interconnect (NVLink/InfiniBand)\n", "   - Communication: All-reduce operations after each layer\n", "\n", "🔸 Pipeline Parallelism (PP):\n", "   - Best for: Models with many layers, reducing memory per device\n", "   - Considerations: Pipeline bubbles reduce efficiency\n", "   - Communication: Point-to-point between adjacent stages\n", "\n", "🔸 Hybrid (TP + PP):\n", "   - Best for: Very large models requiring both memory and compute distribution\n", "   - Considerations: More complex to tune and debug\n", "   - Communication: Both all-reduce and point-to-point\n", "\n", "🔸 Data Parallelism (DP):\n", "   - Best for: Scaling batch size, when model fits on single device\n", "   - Considerations: Gradient synchronization overhead\n", "   - Communication: All-reduce for gradients\n"]}], "source": ["# Analyze and recommend optimal strategies\n", "print(\"=== Parallelism Strategy Recommendations ===\")\n", "\n", "if hybrid_results:\n", "    hybrid_df_sorted = hybrid_df.sort_values(\"total_memory_gb\")\n", "\n", "    print(\"\\nRanked by Memory Efficiency (lowest memory per device):\")\n", "    for idx, row in hybrid_df_sorted.iterrows():\n", "        efficiency_score = (\n", "            row[\"total_devices\"] / row[\"total_memory_gb\"]\n", "        )  # devices per GB\n", "        print(\n", "            f\"{idx+1}. TP={row['tp_size']}, PP={row['pp_size']}: \"\n", "            f\"{row['total_memory_gb']:.2f} GB/device, \"\n", "            f\"{row['total_devices']} devices, \"\n", "            f\"Efficiency: {efficiency_score:.2f}\"\n", "        )\n", "\n", "    # Recommendations based on different scenarios\n", "    print(\"\\n=== Scenario-Based Recommendations ===\")\n", "\n", "    # Memory-constrained scenario\n", "    best_memory = hybrid_df_sorted.iloc[0]\n", "    print(f\"\\n🔹 Memory-Constrained (minimize memory per device):\")\n", "    print(f\"   Recommended: TP={best_memory['tp_size']}, PP={best_memory['pp_size']}\")\n", "    print(f\"   Memory per device: {best_memory['total_memory_gb']:.2f} GB\")\n", "    print(f\"   Total devices needed: {best_memory['total_devices']}\")\n", "\n", "    # Device-constrained scenario (minimize total devices)\n", "    min_devices = hybrid_df.loc[hybrid_df[\"total_devices\"].idxmin()]\n", "    print(f\"\\n🔹 Device-Constrained (minimize total devices):\")\n", "    print(f\"   Recommended: TP={min_devices['tp_size']}, PP={min_devices['pp_size']}\")\n", "    print(f\"   Total devices needed: {min_devices['total_devices']}\")\n", "    print(f\"   Memory per device: {min_devices['total_memory_gb']:.2f} GB\")\n", "\n", "    # Balanced scenario\n", "    hybrid_df[\"balance_score\"] = (\n", "        hybrid_df[\"total_devices\"] * hybrid_df[\"total_memory_gb\"]\n", "    )\n", "    balanced = hybrid_df.loc[hybrid_df[\"balance_score\"].idxmin()]\n", "    print(f\"\\n🔹 Balanced (optimize devices × memory):\")\n", "    print(f\"   Recommended: TP={balanced['tp_size']}, PP={balanced['pp_size']}\")\n", "    print(\n", "        f\"   Total devices: {balanced['total_devices']}, Memory: {balanced['total_memory_gb']:.2f} GB/device\"\n", "    )\n", "    print(f\"   Balance score: {balanced['balance_score']:.2f}\")\n", "\n", "# General recommendations\n", "print(\"\\n=== General Guidelines ===\")\n", "print(\"\\n🔸 Tensor Parallelism (TP):\")\n", "print(\"   - Best for: Memory-bound models, reducing memory per device\")\n", "print(\"   - Considerations: Requires high-bandwidth interconnect (NVLink/InfiniBand)\")\n", "print(\"   - Communication: All-reduce operations after each layer\")\n", "\n", "print(\"\\n🔸 Pipeline Parallelism (PP):\")\n", "print(\"   - Best for: Models with many layers, reducing memory per device\")\n", "print(\"   - Considerations: Pipeline bubbles reduce efficiency\")\n", "print(\"   - Communication: Point-to-point between adjacent stages\")\n", "\n", "print(\"\\n🔸 Hybrid (TP + PP):\")\n", "print(\"   - Best for: Very large models requiring both memory and compute distribution\")\n", "print(\"   - Considerations: More complex to tune and debug\")\n", "print(\"   - Communication: Both all-reduce and point-to-point\")\n", "\n", "print(\"\\n🔸 Data Parallelism (DP):\")\n", "print(\"   - Best for: Scaling batch size, when model fits on single device\")\n", "print(\"   - Considerations: Gradient synchronization overhead\")\n", "print(\"   - Communication: All-reduce for gradients\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Export Analysis Results\n", "\n", "Let's save our parallel strategy analysis for future reference."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Exporting Analysis Results ===\n", "✓ Exported comprehensive summary\n", "\n", "=== Files Created ===\n", "- tensor_parallelism_analysis.csv\n", "- pipeline_parallelism_analysis.csv (if applicable)\n", "- hybrid_parallelism_analysis.csv\n", "- communication_analysis.csv (if applicable)\n", "- parallel_strategies_summary.json\n", "\n", "=== Analysis Summary ===\n", "Model: meta-llama/Llama-2-7b-hf\n", "Base memory: 12.55 GB parameters\n"]}], "source": ["# Export all analysis results\n", "print(\"=== Exporting Analysis Results ===\")\n", "\n", "# Export tensor parallelism results\n", "if valid_results:\n", "    tp_df.to_csv(\"tensor_parallelism_analysis.csv\", index=False)\n", "    print(\"✓ Exported tensor parallelism analysis\")\n", "\n", "# Export pipeline parallelism results\n", "if \"pp_df\" in locals() and not pp_df.empty:\n", "    pp_df.to_csv(\"pipeline_parallelism_analysis.csv\", index=False)\n", "    print(\"✓ Exported pipeline parallelism analysis\")\n", "\n", "# Export hybrid results\n", "if hybrid_results:\n", "    hybrid_df.to_csv(\"hybrid_parallelism_analysis.csv\", index=False)\n", "    print(\"✓ Exported hybrid parallelism analysis\")\n", "\n", "# Export communication analysis\n", "if \"comm_df\" in locals() and not comm_df.empty:\n", "    comm_df.to_csv(\"communication_analysis.csv\", index=False)\n", "    print(\"✓ Exported communication analysis\")\n", "\n", "# Create a comprehensive summary\n", "summary = {\n", "    \"model_name\": model_name,\n", "    \"analysis_timestamp\": pd.Timestamp.now().isoformat(),\n", "    \"base_metrics\": {\n", "        \"total_params\": int(base_metrics.total_params),\n", "        \"memory_params_gb\": float(base_metrics.memory_params / (1024**3)),\n", "        \"memory_activations_gb\": float(base_metrics.memory_activations / (1024**3)),\n", "    },\n", "    \"tensor_parallelism\": {\n", "        \"valid_tp_sizes\": (\n", "            [r[\"tp_size\"] for r in valid_results] if valid_results else []\n", "        ),\n", "        \"max_memory_reduction\": (\n", "            float(\n", "                max(\n", "                    [\n", "                        base_metrics.memory_params / (r[\"memory_params_gb\"] * 1024**3)\n", "                        for r in valid_results\n", "                    ]\n", "                )\n", "            )\n", "            if valid_results\n", "            else 1.0\n", "        ),\n", "    },\n", "}\n", "\n", "if hybrid_results:\n", "    best_config = hybrid_df_sorted.iloc[0]\n", "    summary[\"recommendations\"] = {\n", "        \"memory_optimal\": {\n", "            \"tp_size\": int(best_config[\"tp_size\"]),\n", "            \"pp_size\": int(best_config[\"pp_size\"]),\n", "            \"memory_per_device_gb\": float(best_config[\"total_memory_gb\"]),\n", "            \"total_devices\": int(best_config[\"total_devices\"]),\n", "        }\n", "    }\n", "\n", "# Save summary\n", "import json\n", "\n", "with open(\"parallel_strategies_summary.json\", \"w\") as f:\n", "    json.dump(summary, f, indent=2, default=str)\n", "\n", "print(\"✓ Exported comprehensive summary\")\n", "print(\"\\n=== Files Created ===\")\n", "print(\"- tensor_parallelism_analysis.csv\")\n", "print(\"- pipeline_parallelism_analysis.csv (if applicable)\")\n", "print(\"- hybrid_parallelism_analysis.csv\")\n", "print(\"- communication_analysis.csv (if applicable)\")\n", "print(\"- parallel_strategies_summary.json\")\n", "\n", "print(f\"\\n=== Analysis Summary ===\")\n", "print(f\"Model: {model_name}\")\n", "print(f\"Base memory: {base_metrics.memory_params/(1024**3):.2f} GB parameters\")\n", "if valid_results:\n", "    print(f\"Valid TP sizes: {[r['tp_size'] for r in valid_results]}\")\n", "    max_reduction = max(\n", "        [\n", "            base_metrics.memory_params / (r[\"memory_params_gb\"] * 1024**3)\n", "            for r in valid_results\n", "        ]\n", "    )\n", "    print(f\"Max memory reduction: {max_reduction:.1f}x\")\n", "if hybrid_results:\n", "    print(\n", "        f\"Best hybrid config: TP={best_config['tp_size']}, PP={best_config['pp_size']}\"\n", "    )\n", "    print(f\"Memory per device: {best_config['total_memory_gb']:.2f} GB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "In this tutorial, we've comprehensively analyzed parallel strategies for LLMs:\n", "\n", "### Key Findings\n", "\n", "1. **Tensor Parallelism**:\n", "   - Effectively reduces memory per device\n", "   - Requires high-bandwidth interconnect\n", "   - Limited by dimension divisibility constraints\n", "\n", "2. **Pipeline Parallelism**:\n", "   - Distributes layers across devices\n", "   - Reduces memory linearly with pipeline stages\n", "   - Introduces pipeline bubbles (efficiency loss)\n", "\n", "3. **Hybrid Strategies**:\n", "   - Combine benefits of both TP and PP\n", "   - More complex to optimize\n", "   - Best for very large models\n", "\n", "4. **Communication Overhead**:\n", "   - Increases with parallelism degree\n", "   - Different patterns for TP vs PP\n", "   - Critical for performance optimization\n", "\n", "### Best Practices\n", "\n", "- **Start Simple**: Begin with single parallelism type\n", "- **Measure Communication**: Profile actual communication overhead\n", "- **Consider Hardware**: Match strategy to interconnect capabilities\n", "- **Validate Configurations**: Always check dimension divisibility\n", "- **Monitor Memory**: Track both parameter and activation memory\n", "\n", "### Next Steps\n", "\n", "- Try [Custom Models Tutorial](04_custom_models.ipynb) to implement your own architectures\n", "- Explore [Web Interface Tutorial](05_web_interface.ipynb) for interactive analysis\n", "- Check out real deployment examples in the [examples](../../examples/) directory"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3"}}, "nbformat": 4, "nbformat_minor": 4}
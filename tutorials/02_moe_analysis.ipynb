{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Mixture of Experts (MoE) Analysis Tutorial\n", "\n", "This tutorial focuses on analyzing Mixture of Experts models, which have unique characteristics compared to dense models.\n", "\n", "## What are MoE Models?\n", "\n", "Mixture of Experts models use multiple \"expert\" networks, but only activate a subset of them for each token. This allows for larger model capacity while maintaining computational efficiency."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import os\n", "from llm_modeling_metrics import ModelFactory, ParallelConfig\n", "from llm_modeling_metrics.models import MoEModel\n", "from llm_modeling_metrics.comparison import Comparator\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Set up plotting\n", "plt.style.use(\"default\")\n", "plt.rcParams[\"figure.figsize\"] = (10, 6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"HF_TOKEN\"] = \"*************************************\"\n", "\n", "os.environ[\"http_proxy\"] = \"http://127.0.0.1:7890\"\n", "os.environ[\"https_proxy\"] = os.environ[\"http_proxy\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Loading MoE Models\n", "\n", "Let's load some popular MoE models and examine their characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully loaded MoE model: deepseek-ai/DeepSeek-V3-Base\n", "Model type: MoEModel\n", "✓ This is indeed an MoE model\n"]}], "source": ["# List of MoE models to analyze\n", "moe_models = [\n", "    \"deepseek-ai/DeepSeek-V3-Base\",\n", "    # Add more MoE models as they become available\n", "]\n", "\n", "# Load the first model\n", "model_name = moe_models[0]\n", "try:\n", "    model = ModelFactory.create_model(model_name)\n", "    print(f\"Successfully loaded MoE model: {model_name}\")\n", "    print(f\"Model type: {type(model).__name__}\")\n", "\n", "    # Verify it's an MoE model\n", "    if isinstance(model, MoEModel):\n", "        print(\"✓ This is indeed an MoE model\")\n", "    else:\n", "        print(\"⚠ This model was not recognized as MoE\")\n", "\n", "except Exception as e:\n", "    print(f\"Failed to load model: {e}\")\n", "    print(\"This might be due to model availability or access restrictions.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. MoE-Specific Metrics\n", "\n", "MoE models have unique metrics related to expert utilization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== MoE Expert Configuration ===\n", "Total Experts: 257\n", "Experts per Token: 8\n", "Shared Experts: 1\n", "Routed Experts: 256\n", "Expert Utilization: 3.1% per token\n"]}], "source": ["# Get MoE-specific metrics\n", "if isinstance(model, MoEModel):\n", "    expert_metrics = model.get_expert_metrics()\n", "\n", "    print(\"=== MoE Expert Configuration ===\")\n", "    print(f\"Total Experts: {expert_metrics.get('num_experts', 'N/A')}\")\n", "    print(f\"Experts per Token: {expert_metrics.get('experts_per_token', 'N/A')}\")\n", "    print(f\"Shared Experts: {expert_metrics.get('shared_experts', 'N/A')}\")\n", "    print(f\"Routed Experts: {expert_metrics.get('routed_experts', 'N/A')}\")\n", "\n", "    # Calculate efficiency metrics\n", "    if \"num_experts\" in expert_metrics and \"experts_per_token\" in expert_metrics:\n", "        total_experts = expert_metrics[\"num_experts\"]\n", "        active_experts = expert_metrics[\"experts_per_token\"]\n", "        efficiency = (active_experts / total_experts) * 100\n", "        print(f\"Expert Utilization: {efficiency:.1f}% per token\")\n", "else:\n", "    print(\"Model is not MoE - skipping expert metrics\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Comparing Total vs Active Parameters\n", "\n", "One key advantage of MoE is that not all parameters are active for each token."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Parameter Analysis ===\n", "Total Parameters: 671,253,266,432\n", "Active Parameters (estimated): 32,116,267,175\n", "Parameter Efficiency: 4.8%\n", "\n", "Memory Requirements:\n", "Parameter Memory: 1250.31 GB\n", "Activation Memory: 101.25 GB\n"]}], "source": ["# Get comprehensive metrics\n", "metrics = model.get_metrics(sequence_length=2048)\n", "\n", "print(\"=== Parameter Analysis ===\")\n", "print(f\"Total Parameters: {metrics.total_params:,}\")\n", "\n", "# For MoE models, calculate active parameters\n", "if isinstance(model, MoEModel):\n", "    expert_metrics = model.get_expert_metrics()\n", "\n", "    # Estimate active parameters (this is a simplified calculation)\n", "    if \"num_experts\" in expert_metrics and \"experts_per_token\" in expert_metrics:\n", "        total_experts = expert_metrics[\"num_experts\"]\n", "        active_experts = expert_metrics[\"experts_per_token\"]\n", "\n", "        # Rough estimation of active parameters\n", "        # This assumes experts are the main variable component\n", "        expert_ratio = active_experts / total_experts\n", "\n", "        # Get non-expert parameters (attention, embeddings, etc.)\n", "        attention_params = model.compute_attention_params()\n", "        total_mlp_params = model.compute_mlp_params()\n", "\n", "        # Estimate active MLP parameters\n", "        active_mlp_params = int(total_mlp_params * expert_ratio)\n", "        active_total = attention_params + active_mlp_params\n", "\n", "        print(f\"Active Parameters (estimated): {active_total:,}\")\n", "        print(f\"Parameter Efficiency: {(active_total/metrics.total_params)*100:.1f}%\")\n", "\n", "print(f\"\\nMemory Requirements:\")\n", "print(f\"Parameter Memory: {metrics.memory_params / (1024**3):.2f} GB\")\n", "print(f\"Activation Memory: {metrics.memory_activations / (1024**3):.2f} GB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. FLOP Analysis for MoE\n", "\n", "MoE models have different FLOP characteristics due to sparse activation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== FLOP Analysis ===\n", "embeddings: 0 (0.0%)\n", "attention: 34,321,083,662,336 (39.9%)\n", "dense_mlp: 2,435,246,456,832 (2.8%)\n", "moe: 47,307,064,082,432 (55.0%)\n", "layernorm: 8,954,839,040 (0.0%)\n", "final_layernorm: 73,400,320 (0.0%)\n", "lm_head: 1,897,838,673,920 (2.2%)\n", "\n", "Total FLOPs: 85,970,261,114,880\n", "FLOPs per Token: 41,977,666,560\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Get detailed FLOP breakdown\n", "flop_breakdown = model.compute_flops(sequence_length=2048)\n", "\n", "print(\"=== FLOP Analysis ===\")\n", "total_flops = sum(flop_breakdown.values())\n", "\n", "# Create a breakdown visualization\n", "components = list(flop_breakdown.keys())\n", "flop_values = list(flop_breakdown.values())\n", "percentages = [(f / total_flops) * 100 for f in flop_values]\n", "\n", "for component, flops, pct in zip(components, flop_values, percentages):\n", "    print(f\"{component}: {flops:,} ({pct:.1f}%)\")\n", "\n", "print(f\"\\nTotal FLOPs: {total_flops:,}\")\n", "print(f\"FLOPs per Token: {metrics.flops_per_token:,}\")\n", "\n", "# Plot FLOP breakdown\n", "plt.figure(figsize=(10, 6))\n", "plt.pie(percentages, labels=components, autopct=\"%1.1f%%\", startangle=90)\n", "plt.title(\"FLOP Distribution in MoE Model\")\n", "plt.axis(\"equal\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON>llel Strategies\n", "\n", "MoE models have unique considerations for parallelism, especially expert parallelism."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== MoE Parallel Strategy Analysis ===\n", " tp_size  memory_activations_gb  flops_forward\n", "       1             101.253906 85970261114880\n", "       2             101.253906 85970261114880\n", "       4             101.253906 85970261114880\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Analyze different parallel strategies for MoE\n", "parallel_configs = [\n", "    ParallelConfig(tensor_parallel_size=1),\n", "    ParallelConfig(tensor_parallel_size=2),\n", "    ParallelConfig(tensor_parallel_size=4),\n", "]\n", "\n", "parallel_results = []\n", "\n", "for config in parallel_configs:\n", "    try:\n", "        parallel_metrics = model.get_metrics(\n", "            sequence_length=2048, parallel_config=config\n", "        )\n", "        shapes = model.get_matrix_shapes(config)\n", "\n", "        parallel_results.append(\n", "            {\n", "                \"tp_size\": config.tensor_parallel_size,\n", "                \"memory_activations_gb\": parallel_metrics.memory_activations\n", "                / (1024**3),\n", "                \"flops_forward\": parallel_metrics.flops_forward,\n", "            }\n", "        )\n", "    except Exception as e:\n", "        print(f\"Failed for TP size {config.tensor_parallel_size}: {e}\")\n", "\n", "if parallel_results:\n", "    parallel_df = pd.DataFrame(parallel_results)\n", "    print(\"=== MoE Parallel Strategy Analysis ===\")\n", "    print(parallel_df.to_string(index=False))\n", "\n", "    # Plot memory scaling\n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(parallel_df[\"tp_size\"], parallel_df[\"memory_activations_gb\"], \"bo-\")\n", "    plt.xlabel(\"Tensor Parallel Size\")\n", "    plt.ylabel(\"Activation Memory (GB)\")\n", "    plt.title(\"Memory Scaling with Tensor Parallelism (MoE)\")\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Comparing MoE vs Dense Models\n", "\n", "Let's compare our MoE model with a similar-sized dense model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["self.config={'vocab_size': 32000, 'max_position_embeddings': 4096, 'hidden_size': 4096, 'intermediate_size': 11008, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-05, 'pretraining_tp': 1, 'use_cache': True, 'rope_theta': 10000.0, 'rope_scaling': None, 'attention_bias': False, 'attention_dropout': 0.0, 'mlp_bias': False, 'head_dim': 128, 'return_dict': True, 'output_hidden_states': False, 'torchscript': False, 'torch_dtype': 'float16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['LlamaForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 1, 'pad_token_id': None, 'eos_token_id': 2, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': 'meta-llama/Llama-2-7b-hf', 'transformers_version': '4.53.0', 'model_type': 'llama', 'output_attentions': False}\n", "self._parsed_config={'hidden_size': 4096, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'intermediate_size': 11008, 'vocab_size': 32000, 'max_position_embeddings': 4096, 'model_type': 'llama', 'tie_word_embeddings': False, 'rms_norm_eps': 1e-05}\n", "=== MoE vs Dense Model Comparison ===\n", "                          model architecture  total_params  attention_params    mlp_params  embedding_params   flops_forward  flops_per_token  memory_params  memory_activations   memory_total  experts_per_token  active_params_per_token  param_efficiency  flop_efficiency  memory_efficiency  total_params_relative  flops_per_token_relative  memory_total_relative  param_efficiency_relative  flop_efficiency_relative  memory_efficiency_relative  tensor_parallel_size  pipeline_parallel_size  data_parallel_size  expert_parallel_size  expert_data_parallel_size  sequence_length  batch_size         analysis_timestamp\n", "0  deepseek-ai/DeepSeek-V3-Base     deepseek  671253266432       11641290752  657758617600        1853358080  85970261114880      41977666560  1342506532864        108720553984  5478746685440                  8              37779144704          0.056282         0.062536           8.161967               98.01232                 70.908587              97.540526                 -89.343465                -88.862226                  -10.726817                     1                       1                   1                     1                          1             2048           1 2025-08-05 10:55:41.719554\n", "1      meta-llama/Llama-2-7b-hf        llama    6738149376        2147483648    4328521728         262144000  14633532391424       7145279488    13476298752         14307819520    68213014528                  0               6738149376          1.000000         1.060422          10.123405              -98.01232                -70.908587             -97.540526                  89.343465                 88.862226                   10.726817                     1                       1                   1                     1                          1             2048           1 2025-08-05 10:55:41.719554\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compare MoE with dense models\n", "comparison_models = [\n", "    model_name,  # Our MoE model\n", "    \"meta-llama/Llama-2-7b-hf\",  # Dense model for comparison\n", "]\n", "\n", "try:\n", "    comparator = Comparator()\n", "    comparison_results = comparator.compare_models(\n", "        model_names=comparison_models, sequence_length=2048\n", "    )\n", "\n", "    # Convert to DataFrame\n", "    comparison_df = comparison_results.to_dataframe()\n", "\n", "    print(\"=== MoE vs Dense Model Comparison ===\")\n", "    print(comparison_df.to_string())\n", "\n", "    # Create comparison visualization\n", "    metrics_to_plot = [\"total_params\", \"flops_forward\", \"memory_params\"]\n", "\n", "    fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "\n", "    for i, metric in enumerate(metrics_to_plot):\n", "        if metric in comparison_df.columns:\n", "            values = comparison_df[metric].values\n", "            models = comparison_df[\"model\"].values\n", "\n", "            axes[i].bar(models, values)\n", "            axes[i].set_title(metric.replace(\"_\", \" \").title())\n", "            axes[i].tick_params(axis=\"x\", rotation=45)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "except Exception as e:\n", "    print(f\"Comparison failed: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Expert Utilization Analysis\n", "\n", "Let's analyze how expert utilization affects computational efficiency."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== MoE Efficiency by Sequence Length ===\n", " sequence_length  flops_per_token  memory_activations_gb     total_flops\n", "             512      38139878400              13.875977  19527617740800\n", "            1024      39419141120              35.376953  40365200506880\n", "            2048      41977666560             101.253906  85970261114880\n", "            4096      47094717440             324.507812 192899962634240\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Analyze different sequence lengths for MoE efficiency\n", "sequence_lengths = [512, 1024, 2048, 4096]\n", "moe_efficiency_results = []\n", "\n", "for seq_len in sequence_lengths:\n", "    metrics = model.get_metrics(sequence_length=seq_len)\n", "\n", "    moe_efficiency_results.append(\n", "        {\n", "            \"sequence_length\": seq_len,\n", "            \"flops_per_token\": metrics.flops_per_token,\n", "            \"memory_activations_gb\": metrics.memory_activations / (1024**3),\n", "            \"total_flops\": metrics.flops_forward,\n", "        }\n", "    )\n", "\n", "efficiency_df = pd.DataFrame(moe_efficiency_results)\n", "print(\"=== MoE Efficiency by Sequence Length ===\")\n", "print(efficiency_df.to_string(index=False))\n", "\n", "# Plot efficiency metrics\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# FLOPs per token (should be relatively constant for MoE)\n", "ax1.plot(efficiency_df[\"sequence_length\"], efficiency_df[\"flops_per_token\"], \"ro-\")\n", "ax1.set_xlabel(\"Sequence Length\")\n", "ax1.set_ylabel(\"FLOPs per Token\")\n", "ax1.set_title(\"MoE: FLOPs per Token vs Sequence Length\")\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Memory scaling\n", "ax2.plot(\n", "    efficiency_df[\"sequence_length\"], efficiency_df[\"memory_activations_gb\"], \"bo-\"\n", ")\n", "ax2.set_xlabel(\"Sequence Length\")\n", "ax2.set_ylabel(\"Activation Memory (GB)\")\n", "ax2.set_title(\"MoE: Memory vs Sequence Length\")\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Export MoE Analysis Results\n", "\n", "Let's save our MoE analysis for further study."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exported MoE efficiency analysis to 'moe_efficiency_analysis.csv'\n", "Exported MoE parallel analysis to 'moe_parallel_analysis.csv'\n", "Comparison results not available for export\n", "\n", "=== Analysis Summary ===\n", "model_name: deepseek-ai/DeepSeek-V3-Base\n", "model_type: MoE\n", "total_parameters: 671253266432\n", "flops_per_token: 47094717440\n", "memory_params_gb: 1250.306640625\n", "num_experts: 257\n", "experts_per_token: 8\n", "shared_experts: 1\n"]}], "source": ["# Export efficiency analysis\n", "efficiency_df.to_csv(\"moe_efficiency_analysis.csv\", index=False)\n", "print(\"Exported MoE efficiency analysis to 'moe_efficiency_analysis.csv'\")\n", "\n", "# Export parallel strategy results\n", "if parallel_results:\n", "    parallel_df.to_csv(\"moe_parallel_analysis.csv\", index=False)\n", "    print(\"Exported MoE parallel analysis to 'moe_parallel_analysis.csv'\")\n", "\n", "# Export comparison results if available\n", "try:\n", "    comparison_results.export_json(\"moe_vs_dense_comparison.json\")\n", "    comparison_results.export_excel(\"moe_vs_dense_comparison.xlsx\")\n", "    print(\"Exported MoE vs Dense comparison results\")\n", "except:\n", "    print(\"Comparison results not available for export\")\n", "\n", "# Create a summary report\n", "summary = {\n", "    \"model_name\": model_name,\n", "    \"model_type\": \"MoE\",\n", "    \"total_parameters\": metrics.total_params,\n", "    \"flops_per_token\": metrics.flops_per_token,\n", "    \"memory_params_gb\": metrics.memory_params / (1024**3),\n", "}\n", "\n", "if isinstance(model, MoEModel):\n", "    expert_metrics = model.get_expert_metrics()\n", "    summary.update(\n", "        {\n", "            \"num_experts\": expert_metrics.get(\"num_experts\", \"N/A\"),\n", "            \"experts_per_token\": expert_metrics.get(\"experts_per_token\", \"N/A\"),\n", "            \"shared_experts\": expert_metrics.get(\"shared_experts\", \"N/A\"),\n", "        }\n", "    )\n", "\n", "import json\n", "\n", "with open(\"moe_analysis_summary.json\", \"w\") as f:\n", "    json.dump(summary, f, indent=2, default=str)\n", "\n", "print(\"\\n=== Analysis Summary ===\")\n", "for key, value in summary.items():\n", "    print(f\"{key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "In this tutorial, we've explored the unique characteristics of MoE models:\n", "\n", "1. **Expert Configuration**: Understanding the number of experts and activation patterns\n", "2. **Parameter Efficiency**: How MoE achieves large capacity with sparse activation\n", "3. **FLOP Analysis**: Understanding computational patterns in MoE models\n", "4. **Para<PERSON>l <PERSON>gies**: How parallelism affects MoE models differently\n", "5. **Efficiency Metrics**: Analyzing the trade-offs between capacity and computation\n", "6. **Comparison with Dense Models**: Understanding the advantages of MoE architecture\n", "\n", "## Key Insights\n", "\n", "- **Sparse Activation**: MoE models activate only a fraction of their parameters per token\n", "- **Scalability**: MoE allows for larger models without proportional compute increase\n", "- **Memory Efficiency**: Parameter memory is high, but activation memory can be more efficient\n", "- **Parallel Considerations**: Expert parallelism adds complexity to deployment strategies\n", "\n", "## Next Steps\n", "\n", "- Explore [Parallel Strategies Tutorial](03_parallel_strategies.ipynb) for advanced parallelism\n", "- Try [Custom Models Tutorial](04_custom_models.ipynb) to implement your own MoE variants\n", "- Check out the [Web Interface Tutorial](05_web_interface.ipynb) for interactive analysis"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3"}}, "nbformat": 4, "nbformat_minor": 4}
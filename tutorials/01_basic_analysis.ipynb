{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Basic Model Analysis Tutorial\n", "\n", "This tutorial demonstrates how to use the LLM Modeling Metrics package to analyze the computational requirements of Large Language Models.\n", "\n", "## Prerequisites\n", "\n", "Make sure you have the package installed:\n", "\n", "```bash\n", "pip install llm-modeling-metrics\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Basic Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/share/mamba/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import os\n", "from llm_modeling_metrics import ModelFactory, ParallelConfig, ConfigManager\n", "from llm_modeling_metrics.comparison import Comparator\n", "import pandas as pd\n", "\n", "# Optional: Set HuggingFace token for private models\n", "os.environ[\"HF_TOKEN\"] = \"*************************************\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Analyzing a Single Model\n", "\n", "Let's start by analyzing a popular model like Llama-2-7B."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"HF_TOKEN\"] = \"*************************************\"\n", "\n", "os.environ[\"http_proxy\"] = \"http://127.0.0.1:7890\"\n", "os.environ[\"https_proxy\"] = os.environ[\"http_proxy\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoConfig\n", "\n", "conf = AutoConfig.from_pretrained(\"meta-llama/Llama-2-7b-chat-hf\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["self.config={'vocab_size': 32000, 'max_position_embeddings': 4096, 'hidden_size': 4096, 'intermediate_size': 11008, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-05, 'pretraining_tp': 1, 'use_cache': True, 'rope_theta': 10000.0, 'rope_scaling': None, 'attention_bias': False, 'attention_dropout': 0.0, 'mlp_bias': False, 'head_dim': 128, 'return_dict': True, 'output_hidden_states': False, 'torchscript': False, 'torch_dtype': 'float16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['LlamaForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 1, 'pad_token_id': None, 'eos_token_id': 2, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': 'meta-llama/Llama-2-7b-hf', 'transformers_version': '4.53.0', 'model_type': 'llama', 'output_attentions': False}\n", "self._parsed_config={'hidden_size': 4096, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'intermediate_size': 11008, 'vocab_size': 32000, 'max_position_embeddings': 4096, 'model_type': 'llama', 'tie_word_embeddings': False, 'rms_norm_eps': 1e-05}\n", "Successfully loaded model: meta-llama/Llama-2-7b-hf\n", "Model type: DenseModel\n"]}], "source": ["# Create a model instance\n", "model_name = \"meta-llama/Llama-2-7b-hf\"\n", "model = ModelFactory.create_model(model_name)\n", "\n", "print(f\"Successfully loaded model: {model_name}\")\n", "print(f\"Model type: {type(model).__name__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Getting Basic Metrics\n", "\n", "Now let's compute the basic metrics for this model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Model Metrics ===\n", "Total Parameters: 6,738,149,376\n", "Forward Pass FLOPs: 14,633,532,391,424\n", "FLOPs per Token: 7,145,279,488\n", "Parameter Memory: 12.55 GB\n", "Activation Memory: 13.33 GB\n"]}], "source": ["# Get metrics with default sequence length (2048)\n", "metrics = model.get_metrics()\n", "\n", "print(\"=== Model Metrics ===\")\n", "print(f\"Total Parameters: {metrics.total_params:,}\")\n", "print(f\"Forward Pass FLOPs: {metrics.flops_forward:,}\")\n", "print(f\"FLOPs per Token: {metrics.flops_per_token:,}\")\n", "print(f\"Parameter Memory: {metrics.memory_params / (1024**3):.2f} GB\")\n", "print(f\"Activation Memory: {metrics.memory_activations / (1024**3):.2f} GB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Analyzing Different Sequence Lengths\n", "\n", "Let's see how metrics change with different sequence lengths."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Metrics by Sequence Length ===\n", " sequence_length  flops_forward  flops_per_token  memory_activations_gb\n", "             512  3452224667648       6742626304               1.831299\n", "            1024  7041888288768       6876844032               4.662598\n", "            2048 14633532391424       7145279488              13.325195\n", "            4096 31466088038400       7682150400              42.650391\n", "            8192 71728269099008       8755892224             149.300781\n"]}], "source": ["sequence_lengths = [512, 1024, 2048, 4096, 8192]\n", "results = []\n", "\n", "for seq_len in sequence_lengths:\n", "    metrics = model.get_metrics(sequence_length=seq_len)\n", "    results.append(\n", "        {\n", "            \"sequence_length\": seq_len,\n", "            \"flops_forward\": metrics.flops_forward,\n", "            \"flops_per_token\": metrics.flops_per_token,\n", "            \"memory_activations_gb\": metrics.memory_activations / (1024**3),\n", "        }\n", "    )\n", "\n", "df = pd.DataFrame(results)\n", "print(\"=== Metrics by Sequence Length ===\")\n", "print(df.to_string(index=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Detailed FLOP Breakdown\n", "\n", "Let's get a detailed breakdown of where FLOPs are spent."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== FLOP Breakdown ===\n", "embeddings: 0 (0.0%)\n", "attention: 5,497,558,138,880 (37.6%)\n", "mlp: 8,864,812,498,944 (60.6%)\n", "layernorm: 2,684,354,560 (0.0%)\n", "final_layernorm: 41,943,040 (0.0%)\n", "lm_head: 268,435,456,000 (1.8%)\n", "\n", "Total: 14,633,532,391,424\n"]}], "source": ["# Get detailed FLOP breakdown\n", "flop_breakdown = model.compute_flops(sequence_length=2048)\n", "\n", "print(\"=== FLOP Breakdown ===\")\n", "total_flops = sum(flop_breakdown.values())\n", "\n", "for component, flops in flop_breakdown.items():\n", "    percentage = (flops / total_flops) * 100\n", "    print(f\"{component}: {flops:,} ({percentage:.1f}%)\")\n", "\n", "print(f\"\\nTotal: {total_flops:,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Matrix Shape Analysis\n", "\n", "Let's examine the matrix shapes in the model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== <PERSON> Shapes ===\n", "\n", "Attention Shapes:\n", "  q_proj: (4096, 4096)\n", "  k_proj: (4096, 4096)\n", "  v_proj: (4096, 4096)\n", "  o_proj: (4096, 4096)\n", "\n", "MLP Shapes:\n", "  gate_proj: (4096, 11008)\n", "  up_proj: (4096, 11008)\n", "  down_proj: (11008, 4096)\n"]}], "source": ["# Get matrix shapes without parallelism\n", "base_config = ParallelConfig(tensor_parallel_size=1)\n", "shapes = model.get_matrix_shapes(base_config)\n", "\n", "print(\"=== Matrix Shapes ===\")\n", "print(\"\\nAttention Shapes:\")\n", "for name, shape in shapes[\"attention\"].items():\n", "    print(f\"  {name}: {shape}\")\n", "\n", "print(\"\\nMLP Shapes:\")\n", "for name, shape in shapes[\"mlp\"].items():\n", "    print(f\"  {name}: {shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON>sor Parallelism Analysis\n", "\n", "Now let's see how tensor parallelism affects the model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Tensor Parallelism Analysis ===\n", " tp_size  memory_activations_gb q_proj_shape\n", "       1              13.325195 (4096, 4096)\n", "       2              13.325195 (4096, 2048)\n", "       4              13.325195 (4096, 1024)\n", "       8              13.325195  (4096, 512)\n"]}], "source": ["# Compare different tensor parallel sizes\n", "tp_sizes = [1, 2, 4, 8]\n", "tp_results = []\n", "\n", "for tp_size in tp_sizes:\n", "    try:\n", "        parallel_config = ParallelConfig(tensor_parallel_size=tp_size)\n", "        metrics = model.get_metrics(\n", "            sequence_length=2048, parallel_config=parallel_config\n", "        )\n", "        shapes = model.get_matrix_shapes(parallel_config)\n", "\n", "        # Get a representative shape (e.g., query projection)\n", "        q_proj_shape = shapes[\"attention\"].get(\"q_proj\", \"N/A\")\n", "\n", "        tp_results.append(\n", "            {\n", "                \"tp_size\": tp_size,\n", "                \"memory_activations_gb\": metrics.memory_activations / (1024**3),\n", "                \"q_proj_shape\": str(q_proj_shape),\n", "            }\n", "        )\n", "    except Exception as e:\n", "        print(f\"TP size {tp_size} failed: {e}\")\n", "\n", "tp_df = pd.DataFrame(tp_results)\n", "print(\"=== Tensor Parallelism Analysis ===\")\n", "print(tp_df.to_string(index=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Comparing Multiple Models\n", "\n", "Let's compare this model with others using the Comparator."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["self.config={'vocab_size': 32000, 'max_position_embeddings': 4096, 'hidden_size': 4096, 'intermediate_size': 11008, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-05, 'pretraining_tp': 1, 'use_cache': True, 'rope_theta': 10000.0, 'rope_scaling': None, 'attention_bias': False, 'attention_dropout': 0.0, 'mlp_bias': False, 'head_dim': 128, 'return_dict': True, 'output_hidden_states': False, 'torchscript': False, 'torch_dtype': 'float16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['LlamaForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 1, 'pad_token_id': None, 'eos_token_id': 2, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': 'meta-llama/Llama-2-7b-hf', 'transformers_version': '4.53.0', 'model_type': 'llama', 'output_attentions': False}\n", "self._parsed_config={'hidden_size': 4096, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'intermediate_size': 11008, 'vocab_size': 32000, 'max_position_embeddings': 4096, 'model_type': 'llama', 'tie_word_embeddings': False, 'rms_norm_eps': 1e-05}\n", "load config success config=GPT2Config {\n", "  \"activation_function\": \"gelu_new\",\n", "  \"architectures\": [\n", "    \"GPT2LMHeadModel\"\n", "  ],\n", "  \"attn_pdrop\": 0.1,\n", "  \"bos_token_id\": 50256,\n", "  \"embd_pdrop\": 0.1,\n", "  \"eos_token_id\": 50256,\n", "  \"initializer_range\": 0.02,\n", "  \"layer_norm_epsilon\": 1e-05,\n", "  \"model_type\": \"gpt2\",\n", "  \"n_ctx\": 1024,\n", "  \"n_embd\": 1024,\n", "  \"n_head\": 16,\n", "  \"n_inner\": null,\n", "  \"n_layer\": 24,\n", "  \"n_positions\": 1024,\n", "  \"reorder_and_upcast_attn\": false,\n", "  \"resid_pdrop\": 0.1,\n", "  \"scale_attn_by_inverse_layer_idx\": false,\n", "  \"scale_attn_weights\": true,\n", "  \"summary_activation\": null,\n", "  \"summary_first_dropout\": 0.1,\n", "  \"summary_proj_to_labels\": true,\n", "  \"summary_type\": \"cls_index\",\n", "  \"summary_use_proj\": true,\n", "  \"task_specific_params\": {\n", "    \"conversational\": {\n", "      \"max_length\": 1000\n", "    }\n", "  },\n", "  \"transformers_version\": \"4.53.0\",\n", "  \"use_cache\": true,\n", "  \"vocab_size\": 50257\n", "}\n", "\n", "Error analyzing model microsoft/DialoGPT-medium: Cannot determine architecture for model 'microsoft/DialoGPT-medium'. Config model_type: gpt2, architectures: ['GPT2LMHeadModel']\n", "=== Model Comparison ===\n", "                       model architecture  total_params  attention_params    mlp_params  embedding_params  flops_forward  flops_per_token  memory_params  memory_activations  memory_total  experts_per_token  active_params_per_token  param_efficiency  flop_efficiency  memory_efficiency  tensor_parallel_size  pipeline_parallel_size  data_parallel_size  expert_parallel_size  expert_data_parallel_size  sequence_length  batch_size         analysis_timestamp\n", "0   meta-llama/Llama-2-7b-hf        llama  6.738149e+09      2.147484e+09  4.328522e+09       262144000.0   1.463353e+13     7.145279e+09   1.347630e+10        1.430782e+10  6.821301e+10                  0               6738149376               1.0         1.060422          10.123405                     1                       1                   1                     1                          1             2048           1 2025-08-05 10:45:06.048605\n", "1  microsoft/DialoGPT-medium         None           NaN               NaN           NaN               NaN            NaN              NaN            NaN                 NaN           NaN                  0                        0               NaN              NaN                NaN                     1                       1                   1                     1                          1             2048           1 2025-08-05 10:45:06.048605\n"]}], "source": ["# Compare multiple models\n", "model_names = [\n", "    \"meta-llama/Llama-2-7b-hf\",\n", "    \"microsoft/DialoGPT-medium\",  # Smaller model for comparison\n", "    # Add more models as needed\n", "]\n", "\n", "comparator = Comparator()\n", "\n", "try:\n", "    comparison_results = comparator.compare_models(\n", "        model_names=model_names, sequence_length=2048\n", "    )\n", "\n", "    # Convert to DataFrame for easy viewing\n", "    comparison_df = comparison_results.to_dataframe()\n", "    print(\"=== Model Comparison ===\")\n", "    print(comparison_df.to_string())\n", "\n", "except Exception as e:\n", "    print(f\"Comparison failed: {e}\")\n", "    print(\"This might be due to model availability or network issues.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Exporting Results\n", "\n", "Finally, let's export our analysis results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exported sequence length analysis to 'sequence_length_analysis.csv'\n", "Exported tensor parallelism analysis to 'tensor_parallelism_analysis.csv'\n", "Comparison results not available for export\n"]}], "source": ["# Export sequence length analysis\n", "df.to_csv(\"sequence_length_analysis.csv\", index=False)\n", "print(\"Exported sequence length analysis to 'sequence_length_analysis.csv'\")\n", "\n", "# Export tensor parallelism analysis\n", "tp_df.to_csv(\"tensor_parallelism_analysis.csv\", index=False)\n", "print(\"Exported tensor parallelism analysis to 'tensor_parallelism_analysis.csv'\")\n", "\n", "# If comparison was successful, export it too\n", "try:\n", "    comparison_results.export_json(\"model_comparison.json\")\n", "    comparison_results.export_excel(\"model_comparison.xlsx\")\n", "    print(\n", "        \"Exported model comparison to 'model_comparison.json' and 'model_comparison.xlsx'\"\n", "    )\n", "except:\n", "    print(\"Comparison results not available for export\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "In this tutorial, we've learned how to:\n", "\n", "1. **Load models** using the ModelFactory\n", "2. **Compute basic metrics** like parameters, FLOPs, and memory\n", "3. **Analyze different sequence lengths** and their impact\n", "4. **Get detailed FLOP breakdowns** by component\n", "5. **Examine matrix shapes** in the model\n", "6. **Analyze tensor parallelism** effects\n", "7. **Compare multiple models** using the Comparator\n", "8. **Export results** for further analysis\n", "\n", "## Next Steps\n", "\n", "- Try the [MoE Analysis Tutorial](02_moe_analysis.ipynb) to learn about Mixture of Experts models\n", "- Explore [Parallel Strategies Tutorial](03_parallel_strategies.ipynb) for advanced parallelism analysis\n", "- Check out [Custom Models Tutorial](04_custom_models.ipynb) to implement your own model architectures"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3"}}, "nbformat": 4, "nbformat_minor": 4}
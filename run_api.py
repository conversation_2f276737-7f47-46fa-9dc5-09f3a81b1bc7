"""
Startup script for the LLM Modeling Metrics API.
"""

import os
import sys
from pathlib import Path

import uvicorn

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))


def main():
    """Run the FastAPI application."""
    # Configuration
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    reload = os.getenv("RELOAD", "true").lower() == "true"
    log_level = os.getenv("LOG_LEVEL", "info")

    print(f"Starting LLM Modeling Metrics API...")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Reload: {reload}")
    print(f"Log level: {log_level}")

    # Optional environment variables
    if os.getenv("API_TOKEN"):
        print("API token authentication: Enabled")
    else:
        print("API token authentication: Disabled")

    rate_limit_requests = os.getenv("RATE_LIMIT_REQUESTS", "100")
    rate_limit_window = os.getenv("RATE_LIMIT_WINDOW", "6")
    print(f"Rate limiting: {rate_limit_requests} requests per {rate_limit_window}s")

    print("\nStarting server...")
    print("=" * 50)

    # Run the server
    uvicorn.run(
        "llm_modeling_metrics.web.app:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True,
    )


if __name__ == "__main__":
    main()

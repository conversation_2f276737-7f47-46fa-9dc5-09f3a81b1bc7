import torch
from torch import nn
from loguru import logger
import math
import numpy as np

import torch.nn.functional as F

bs = 2
seq_len = 3
hidden_size = 8


def softmax(x):
    return torch.exp(x) / torch.sum(torch.exp(x), axis=-1, keepdim=True)


def stable_softmax(x, dim=-1):
    max_val = torch.max(x, dim=dim, keepdim=True).values
    exp_x = torch.exp(x - max_val)

    logger.debug(f"row exp sum {torch.sum(exp_x, axis=dim, keepdim=True)}")
    return exp_x / torch.sum(exp_x, axis=dim, keepdim=True)


x = torch.randn(bs, seq_len, hidden_size)

a = softmax(x)

golden = torch.softmax(x, dim=-1)


logger.debug(f"{x=}")
logger.debug(f"golden {golden}")

torch.allclose(softmax(x), torch.softmax(x, dim=-1))
torch.allclose(stable_softmax(x), torch.softmax(x, dim=-1))


def online_softmax(x, block_size=4):
    bs, seq_len, hidden_size = x.shape

    result = torch.zeros_like(x)
    acc = torch.zeros((bs, seq_len, 1))
    current_max = torch.zeros((bs, seq_len, 1))
    for i in range(0, hidden_size, block_size):
        new_max = torch.max(x[:, :, i : i + block_size], dim=-1, keepdim=True).values
        new_max = torch.max(new_max, current_max)
        # print(new_max)
        acc = torch.sum(
            torch.exp(x[:, :, i : i + block_size] - new_max), axis=-1, keepdim=True
        ) + acc * torch.exp(current_max - new_max)
        current_max = new_max

    for i in range(0, hidden_size, block_size):
        result[:, :, i : i + block_size] = (
            torch.exp(x[:, :, i : i + block_size] - current_max) / acc
        )
    return result


online_softmax(x)

torch.allclose(online_softmax(x), torch.softmax(x, dim=-1))


q_len = 2
seq_len = 3
hidden_size = 8

torch.manual_seed(0)

# num_heads = 4
# num_kv_heads = 2

# bs = 1
# q_len = 2
# seq_len = 3
# hidden_size = 4

# num_heads = 2
# num_kv_heads = 1

q = torch.randn(q_len, hidden_size)
k = torch.randn(seq_len, hidden_size)
v = torch.randn(seq_len, hidden_size)

def simple_attention(q, k, v):
    scale = 1 / np.sqrt(q.shape[-1])
    q = q * scale

    scores = torch.matmul(q, k.transpose(-2, -1))

    logger.debug(f"scores: {scores.shape}")

    # scores = scores / math.sqrt(head_dim)
    scores = torch.softmax(scores, dim=-1)
    output = torch.matmul(scores, v)

    logger.debug(f"scores: {scores.shape}")

    output = torch.matmul(scores, v)

    return output


# for mha, single_batch
def flash_attn(q, k, v, dropout_p=0.0, softmax_scale=None ,debug=False):
    qlen, klen = q.shape[-2], k.shape[-2]
    hidden_size = q.shape[-1]
    scale = 1 / np.sqrt(q.shape[-1])
    q = q * scale
    tile_row = 2
    tile_col = 3
    o = torch.zeros_like(q)
    l = torch.zeros(qlen, dtype=torch.float32)
    m = torch.ones(qlen, dtype=torch.float32) * -torch.inf

    for i in range(0, klen, tile_col):
        k_tile = k[i : i + tile_col, :]
        v_tile = v[i : i + tile_col, :]
        for j in range(0, qlen, tile_row):
            q_tile = q[j : j + tile_row, :]
            o_tile = q[j : j + tile_row, :]
            s_tile = torch.matmul(q_tile, k_tile.transpose(-2, -1))
            m_tile = torch.max(s_tile, dim=-1, keepdim=False).values

            # logger.debug(f"s_tile: {s_tile}")
            # logger.debug(f"m_tile: {m_tile}")
            p_tile = torch.exp(s_tile - m_tile.unsqueeze(-1))

            l_tile = torch.sum(p_tile, dim=-1, keepdim=False)

            # m_tile = m_tile.squeeze(-1)
            m_new = torch.maximum(m_tile, m[j : j + tile_row])

            l_new = (
                torch.exp(m[j : j + tile_row] - m_new) * l[j : j + tile_row]
                + torch.exp(m_tile - m_new) * l_tile
            )

            # logger.debug(f"p_tile: {p_tile}")
            # logger.debug(f"l_tile: {l_tile}")
            # logger.debug(f"m_new: {m_new}")
            # logger.debug(f"l_new: {l_new}")
            # logger.debug(f"o_tile: {o_tile}")



            # torch.exp(m_tile - m_new)
            # print((torch.exp(m_tile - m_new)/l_new).shape )
            # print((p_tile @ v_tile).shape )
            # (torch.exp(m_tile - m_new)/l_new).unsqueeze(-1) * (p_tile @ v_tile)


            t1= (l[j : j + tile_row]/ l_new.squeeze(-1) * torch.exp(m[j : j + tile_row] - m_new)).unsqueeze(-1) * o_tile

            t21 = torch.exp(m_tile - m_new)/l_new
            t22 = p_tile @ v_tile
            t2 = (torch.exp(m_tile - m_new)/l_new).unsqueeze(-1) * (p_tile @ v_tile)

            if debug:
                print(f'{t1.shape=}')
                print(f'{t21.shape=}')
                print(f'{t22.shape=}')

                print(f'{t2.shape=}')

            o[j : j + tile_row, :] = t1 + t2

            l[j : j + tile_row] = l_new.squeeze(-1)
            m[j : j + tile_row] = m_new


        #     break
        # break
    # print(f'{o=}')
    if debug:
        print(f'{l=}')
        print(f'{m=}')
    return o

o= flash_attn(q, k, v)

print(f'{o=}')

o2= simple_attention(q, k, v)

print(f'{o2=}')

torch.allclose(o, o2)

bs = 2
q_len = 3
seq_len = 5
hidden_size = 12

num_heads = 4
num_kv_heads = 2

# bs = 1
# q_len = 2
# seq_len = 3
# hidden_size = 4

# num_heads = 2
# num_kv_heads = 1

head_dim = hidden_size // num_heads

q = torch.randn(bs, q_len, num_heads, head_dim)
k = torch.randn(bs, seq_len, num_kv_heads, head_dim)
v = torch.randn(bs, seq_len, num_kv_heads, head_dim)

logger.debug(f"q: {q.shape}")


def attention(q, k, v):
    q = q.view(bs, q_len, num_heads, head_dim).transpose(1, 2)
    k = (
        k.view(bs, seq_len, num_kv_heads, head_dim)
        .transpose(1, 2)
        .repeat(1, num_heads // num_kv_heads, 1, 1)
    )
    v = (
        v.view(bs, seq_len, num_kv_heads, head_dim)
        .transpose(1, 2)
        .repeat(1, num_heads // num_kv_heads, 1, 1)
    )

    scores = torch.matmul(q, k.transpose(-2, -1))

    logger.debug(f"scores: {scores.shape}")

    scores = scores / math.sqrt(head_dim)
    scores = torch.softmax(scores, dim=-1)
    output = torch.matmul(scores, v)

    logger.debug(f"scores: {scores.shape}")

    output = torch.matmul(scores, v)

    return output.view(bs, q_len, hidden_size)


o = attention(q, k, v)

logger.debug(f"{o=}")

q2 = q.view(bs, q_len, num_heads, head_dim).transpose(1, 2)
k2 = k.view(bs, seq_len, num_kv_heads, head_dim).transpose(1, 2)
v2 = v.view(bs, seq_len, num_kv_heads, head_dim).transpose(1, 2)

attn_output = (
    F.scaled_dot_product_attention(q2, k2, v2, dropout_p=0.0, enable_gqa=True)
    .transpose(1, 2)
    .reshape(bs, q_len, num_heads * head_dim)
)

logger.debug(f"{attn_output=}")

torch.allclose(attn_output, o)

q.shape[0]

q2

total_batch_size = 1024

expert_batch_size = total_batch_size // (num_routed_expert)
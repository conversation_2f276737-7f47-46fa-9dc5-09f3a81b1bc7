2025-09-02 16:55:15,865 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:55:15,865 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:55:15,865 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - validate_directory:80 - Created directory: exports
2025-09-02 16:55:15,865 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:55:15,865 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 16:57:15,617 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:57:15,617 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:57:15,617 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:57:15,617 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 16:57:15,619 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:57:15,619 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:57:15,619 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:57:15,619 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.utils.error_handler - ERROR - handle_configuration_error:52 - Configuration error: Invalid configuration parameter
NoneType: None
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_available_models:181 - Loaded 14 available models
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - __init__:62 - ModelSelectionComponent initialized
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_dense_preset:297 - Loaded dense preset with 2 models
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_moe_preset:322 - Loaded MoE preset with 3 models
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 16:57:15,620 - llm_modeling_metrics.gradio_interface.components.analysis_controls - INFO - __init__:63 - AnalysisControlsComponent initialized
2025-09-02 16:57:15,621 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:57:15,621 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:57:15,621 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:57:15,621 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 16:57:15,621 - llm_modeling_metrics.gradio_interface.components.analysis_engine - INFO - __init__:73 - AnalysisEngine initialized
2025-09-02 16:57:15,621 - llm_modeling_metrics.gradio_interface.main - INFO - __init__:60 - GradioLLMAnalyzer initialized
2025-09-02 16:57:15,622 - llm_modeling_metrics.gradio_interface.main - INFO - reset_state:373 - UI state reset to defaults
2025-09-02 16:58:51,566 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:58:51,567 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:58:51,567 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:58:51,567 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 16:58:51,569 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:58:51,569 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:58:51,569 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:58:51,569 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 16:58:51,569 - llm_modeling_metrics.gradio_interface.components.analysis_engine - INFO - __init__:73 - AnalysisEngine initialized
2025-09-02 16:58:51,569 - llm_modeling_metrics.gradio_interface.main - INFO - __init__:60 - GradioLLMAnalyzer initialized
2025-09-02 16:59:17,718 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 16:59:17,718 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 16:59:17,718 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 16:59:17,718 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:09,530 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:09,531 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:09,531 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:09,531 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.utils.error_handler - ERROR - handle_configuration_error:52 - Configuration error: Invalid configuration parameter
NoneType: None
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:09,533 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_available_models:181 - Loaded 14 available models
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - __init__:62 - ModelSelectionComponent initialized
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_dense_preset:297 - Loaded dense preset with 2 models
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_moe_preset:322 - Loaded MoE preset with 3 models
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:09,534 - llm_modeling_metrics.gradio_interface.components.analysis_controls - INFO - __init__:63 - AnalysisControlsComponent initialized
2025-09-02 17:08:09,536 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:09,536 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:09,536 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:09,536 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:09,536 - llm_modeling_metrics.gradio_interface.components.analysis_engine - INFO - __init__:73 - AnalysisEngine initialized
2025-09-02 17:08:09,536 - llm_modeling_metrics.gradio_interface.main - INFO - __init__:60 - GradioLLMAnalyzer initialized
2025-09-02 17:08:09,537 - llm_modeling_metrics.gradio_interface.main - INFO - reset_state:373 - UI state reset to defaults
2025-09-02 17:08:39,589 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:39,589 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:39,589 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:39,589 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.utils.error_handler - ERROR - handle_configuration_error:52 - Configuration error: Invalid configuration parameter
NoneType: None
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_available_models:181 - Loaded 14 available models
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - __init__:62 - ModelSelectionComponent initialized
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_dense_preset:297 - Loaded dense preset with 2 models
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_moe_preset:322 - Loaded MoE preset with 3 models
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:39,592 - llm_modeling_metrics.gradio_interface.components.analysis_controls - INFO - __init__:63 - AnalysisControlsComponent initialized
2025-09-02 17:08:39,595 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:08:39,595 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:08:39,595 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:08:39,596 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-02 17:08:39,596 - llm_modeling_metrics.gradio_interface.components.analysis_engine - INFO - __init__:73 - AnalysisEngine initialized
2025-09-02 17:08:39,596 - llm_modeling_metrics.gradio_interface.main - INFO - __init__:60 - GradioLLMAnalyzer initialized
2025-09-02 17:08:39,596 - llm_modeling_metrics.gradio_interface.main - INFO - reset_state:373 - UI state reset to defaults
2025-09-02 17:09:09,602 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-02 17:09:09,603 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-02 17:09:09,603 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-02 17:09:09,603 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-03 15:42:23,693 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-03 15:42:23,693 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-03 15:42:23,693 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-03 15:42:23,693 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-03 15:59:10,562 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-03 15:59:10,562 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-03 15:59:10,563 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-03 15:59:10,563 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-03 16:00:10,048 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-03 16:00:10,048 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-03 16:00:10,048 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-03 16:00:10,048 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-03 16:00:10,060 - llm_modeling_metrics.hardware.config_manager - INFO - _load_all_configurations:126 - Loaded primary configuration from data/gpu_specs.yaml
2025-09-03 16:00:10,068 - llm_modeling_metrics.hardware.config_manager - INFO - _start_file_watching:203 - Started hardware configuration file watching
2025-09-03 16:00:10,069 - llm_modeling_metrics.hardware.monitoring - INFO - register_health_check:230 - Registered health check: hardware_config
2025-09-03 16:00:10,069 - llm_modeling_metrics.hardware.monitoring - INFO - register_health_check:230 - Registered health check: memory_usage
2025-09-03 16:00:10,069 - llm_modeling_metrics.hardware.monitoring - INFO - register_health_check:230 - Registered health check: error_rate
2025-09-03 16:00:10,069 - llm_modeling_metrics.hardware.monitoring - WARNING - _run_health_checks:164 - Health check memory_usage: High memory usage: 84.0%
2025-09-03 16:00:10,069 - llm_modeling_metrics.hardware.monitoring - INFO - start_monitoring:125 - Hardware system monitoring started
2025-09-03 16:00:10,069 - llm_modeling_metrics.hardware.monitoring - INFO - set_fallback_config:420 - Set fallback configuration for hardware_service
2025-09-03 16:00:10,069 - llm_modeling_metrics.gradio_interface.components.hardware_config - INFO - __init__:78 - Hardware service initialized successfully
2025-09-03 16:00:10,069 - llm_modeling_metrics.gradio_interface.components.hardware_config - INFO - _load_hardware_specifications:117 - Loaded 7 hardware specifications from backend service
2025-09-03 16:14:34,978 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-03 16:14:34,978 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-03 16:14:34,978 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-03 16:14:34,979 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-03 16:14:34,982 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-03 16:14:34,982 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-03 16:14:34,982 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-03 16:14:34,982 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-03 16:14:34,982 - llm_modeling_metrics.gradio_interface.components.analysis_engine - INFO - __init__:73 - AnalysisEngine initialized
2025-09-03 16:14:34,982 - llm_modeling_metrics.gradio_interface.main - INFO - __init__:60 - GradioLLMAnalyzer initialized
2025-09-03 16:43:03,037 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-03 16:43:03,038 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-03 16:43:03,038 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-03 16:43:03,038 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-03 16:43:03,040 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-03 16:43:03,040 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-03 16:43:03,040 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-03 16:43:03,040 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-03 16:43:03,040 - llm_modeling_metrics.gradio_interface.components.analysis_engine - INFO - __init__:73 - AnalysisEngine initialized
2025-09-03 16:43:03,040 - llm_modeling_metrics.gradio_interface.main - INFO - __init__:60 - GradioLLMAnalyzer initialized
2025-09-03 16:43:03,042 - asyncio - DEBUG - __init__:54 - Using selector: KqueueSelector
2025-09-03 16:43:03,077 - urllib3.connectionpool - DEBUG - _new_conn:1049 - Starting new HTTPS connection (1): huggingface.co:443
2025-09-03 16:43:03,078 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_available_models:181 - Loaded 14 available models
2025-09-03 16:43:03,078 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - __init__:62 - ModelSelectionComponent initialized
2025-09-03 16:43:03,084 - httpcore.connection - DEBUG - trace:47 - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=3 socket_options=None
2025-09-03 16:43:03,084 - httpcore.connection - DEBUG - trace:47 - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x163f6e7d0>
2025-09-03 16:43:03,085 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.started request=<Request [b'CONNECT']>
2025-09-03 16:43:03,085 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.complete
2025-09-03 16:43:03,085 - httpcore.http11 - DEBUG - trace:47 - send_request_body.started request=<Request [b'CONNECT']>
2025-09-03 16:43:03,085 - httpcore.http11 - DEBUG - trace:47 - send_request_body.complete
2025-09-03 16:43:03,085 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.started request=<Request [b'CONNECT']>
2025-09-03 16:43:03,085 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-09-03 16:43:03,085 - httpcore.proxy - DEBUG - trace:47 - start_tls.started ssl_context=<ssl.SSLContext object at 0x163f7c050> server_hostname='api.gradio.app' timeout=3
2025-09-03 16:43:03,700 - urllib3.connectionpool - DEBUG - _make_request:544 - https://huggingface.co:443 "HEAD /api/telemetry/gradio/initiated HTTP/1.1" 200 0
2025-09-03 16:43:06,088 - httpcore.proxy - DEBUG - trace:47 - start_tls.failed exception=ConnectTimeout(TimeoutError('_ssl.c:975: The handshake operation timed out'))
2025-09-03 16:43:06,088 - httpcore.connection - DEBUG - trace:47 - close.started
2025-09-03 16:43:06,088 - httpcore.connection - DEBUG - trace:47 - close.complete
2025-09-04 08:48:46,543 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-04 08:48:46,544 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-04 08:48:46,544 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-04 08:48:46,544 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-04 08:48:46,549 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-04 08:48:46,550 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-04 08:48:46,550 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-04 08:48:46,550 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-04 08:48:46,550 - llm_modeling_metrics.gradio_interface.components.memory_controls - INFO - __init__:79 - MemoryControlsComponent initialized
2025-09-04 08:49:59,223 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-04 08:49:59,223 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-04 08:49:59,224 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-04 08:49:59,224 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-04 08:49:59,228 - llm_modeling_metrics.gradio_interface.utils.config_utils - INFO - setup_logging_for_environment:179 - Logging configured for development environment
2025-09-04 08:49:59,228 - llm_modeling_metrics.gradio_interface.config - INFO - _load_from_environment:116 - Configuration loaded for development environment with 0 overrides
2025-09-04 08:49:59,228 - llm_modeling_metrics.gradio_interface.config - INFO - _validate_configuration:137 - Configuration validation completed successfully
2025-09-04 08:49:59,228 - llm_modeling_metrics.gradio_interface.config - INFO - _setup_directories:150 - Directories created: exports
2025-09-04 08:49:59,228 - llm_modeling_metrics.gradio_interface.components.analysis_engine - INFO - __init__:73 - AnalysisEngine initialized
2025-09-04 08:49:59,228 - llm_modeling_metrics.gradio_interface.main - INFO - __init__:60 - GradioLLMAnalyzer initialized
2025-09-04 08:49:59,231 - asyncio - DEBUG - __init__:54 - Using selector: KqueueSelector
2025-09-04 08:49:59,257 - urllib3.connectionpool - DEBUG - _new_conn:1049 - Starting new HTTPS connection (1): huggingface.co:443
2025-09-04 08:49:59,269 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - _load_available_models:181 - Loaded 14 available models
2025-09-04 08:49:59,269 - llm_modeling_metrics.gradio_interface.components.model_selection - INFO - __init__:62 - ModelSelectionComponent initialized
2025-09-04 08:49:59,273 - httpcore.connection - DEBUG - trace:47 - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=3 socket_options=None
2025-09-04 08:49:59,274 - httpcore.connection - DEBUG - trace:47 - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x30ded5110>
2025-09-04 08:49:59,274 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.started request=<Request [b'CONNECT']>
2025-09-04 08:49:59,274 - httpcore.http11 - DEBUG - trace:47 - send_request_headers.complete
2025-09-04 08:49:59,274 - httpcore.http11 - DEBUG - trace:47 - send_request_body.started request=<Request [b'CONNECT']>
2025-09-04 08:49:59,274 - httpcore.http11 - DEBUG - trace:47 - send_request_body.complete
2025-09-04 08:49:59,275 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.started request=<Request [b'CONNECT']>
2025-09-04 08:49:59,275 - httpcore.http11 - DEBUG - trace:47 - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-09-04 08:49:59,276 - httpcore.proxy - DEBUG - trace:47 - start_tls.started ssl_context=<ssl.SSLContext object at 0x30deb08c0> server_hostname='api.gradio.app' timeout=3
2025-09-04 08:49:59,658 - urllib3.connectionpool - DEBUG - _make_request:544 - https://huggingface.co:443 "HEAD /api/telemetry/gradio/initiated HTTP/1.1" 200 0
2025-09-04 08:50:02,280 - httpcore.proxy - DEBUG - trace:47 - start_tls.failed exception=ConnectTimeout(TimeoutError('_ssl.c:975: The handshake operation timed out'))
2025-09-04 08:50:02,296 - httpcore.connection - DEBUG - trace:47 - close.started
2025-09-04 08:50:02,296 - httpcore.connection - DEBUG - trace:47 - close.complete

"""
Integration tests for complete model analysis workflow.
"""

import json
import tempfile
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from llm_modeling_metrics.comparison.comparator import Comparator
from llm_modeling_metrics.core.base_model import ParallelConfig
from llm_modeling_metrics.core.config_manager import ConfigManager
from llm_modeling_metrics.core.model_factory import ModelFactory
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel
from tests.conftest import (
    MockConfig,
    assert_flops_breakdown_valid,
    assert_memory_breakdown_valid,
    assert_metrics_valid,
    create_mock_model_config,
)


class TestModelAnalysisWorkflow:
    """Test complete model analysis workflow."""

    def setup_method(self):
        """Set up test environment."""
        # Register models
        ModelFactory.register_model("llama", DenseModel)
        ModelFactory.register_model("deepseek", MoEModel)

        # Create mock config manager
        self.mock_config_manager = Mock(spec=ConfigManager)
        ModelFactory.set_config_manager(self.mock_config_manager)

    def test_dense_model_complete_workflow(self):
        """Test complete workflow for dense model analysis."""
        # Setup mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Step 1: Create model
        model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")
        assert isinstance(model, DenseModel)

        # Step 2: Compute basic metrics
        metrics = model.get_metrics(sequence_length=2048, batch_size=1)
        assert_metrics_valid(metrics)

        # Step 3: Test with different configurations
        test_configs = [
            {"sequence_length": 1024, "batch_size": 2},
            {"sequence_length": 4096, "batch_size": 1},
            {"sequence_length": 2048, "batch_size": 4},
        ]

        for config in test_configs:
            test_metrics = model.get_metrics(**config)
            assert_metrics_valid(test_metrics)
            assert test_metrics.sequence_length == config["sequence_length"]
            assert test_metrics.batch_size == config["batch_size"]

        # Step 4: Test parallel configurations
        parallel_configs = [
            ParallelConfig(tensor_parallel_size=2),
            ParallelConfig(tensor_parallel_size=4),
            ParallelConfig(pipeline_parallel_size=2),
            ParallelConfig(tensor_parallel_size=2, pipeline_parallel_size=2),
        ]

        for parallel_config in parallel_configs:
            if model.validate_parallel_config(parallel_config):
                parallel_metrics = model.get_metrics(parallel_config=parallel_config)
                assert_metrics_valid(parallel_metrics)
                assert parallel_metrics.parallel_config == parallel_config

        # Step 5: Verify FLOP and memory calculations
        flops = model.compute_flops(sequence_length=2048, batch_size=1)
        assert_flops_breakdown_valid(flops)

        memory = model.compute_memory_requirements(sequence_length=2048, batch_size=1)
        assert_memory_breakdown_valid(memory)

        # Step 6: Test matrix shapes
        shapes = model.get_matrix_shapes()
        assert "attention" in shapes
        assert "mlp" in shapes

        # Step 7: Test with tensor parallelism
        tp_config = ParallelConfig(tensor_parallel_size=2)
        if model.validate_parallel_config(tp_config):
            tp_shapes = model.get_matrix_shapes(tp_config)
            assert tp_shapes != shapes  # Should be different with parallelism

    def test_moe_model_complete_workflow(self):
        """Test complete workflow for MoE model analysis."""
        # Setup mock config
        deepseek_config = create_mock_model_config("deepseek_v3")
        self.mock_config_manager.fetch_config.return_value = deepseek_config

        # Step 1: Create model
        model = ModelFactory.create_model("deepseek-ai/DeepSeek-R1")
        assert isinstance(model, MoEModel)

        # Step 2: Compute basic metrics
        metrics = model.get_metrics(sequence_length=2048, batch_size=1)
        assert_metrics_valid(metrics)

        # MoE-specific checks
        assert metrics.experts_per_token is not None
        assert metrics.active_params_per_token is not None
        assert metrics.active_params_per_token < metrics.total_params

        # Step 3: Test MoE-specific functionality
        active_params = model.compute_active_params_per_token()
        assert active_params > 0
        assert active_params < model.get_total_params()

        # Step 4: Test expert utilization analysis
        if hasattr(model, "get_expert_utilization_analysis"):
            expert_analysis = model.get_expert_utilization_analysis()
            assert "total_experts" in expert_analysis
            assert "experts_per_token" in expert_analysis
            assert "parameter_efficiency" in expert_analysis

        # Step 5: Test MoE parallel configurations
        moe_parallel_configs = [
            ParallelConfig(tensor_parallel_size=2),
            ParallelConfig(expert_parallel_size=2),
            ParallelConfig(tensor_parallel_size=2, expert_parallel_size=2),
        ]

        for parallel_config in moe_parallel_configs:
            if model.validate_parallel_config(parallel_config):
                parallel_metrics = model.get_metrics(parallel_config=parallel_config)
                assert_metrics_valid(parallel_metrics)

        # Step 6: Verify MoE FLOP calculations
        flops = model.compute_flops(sequence_length=2048, batch_size=1)
        assert_flops_breakdown_valid(flops)

        # Should have MoE-specific FLOP components
        assert any(
            "expert" in key.lower() or "moe" in key.lower() for key in flops.keys()
        )

    def test_model_comparison_workflow(self):
        """Test model comparison workflow."""
        # Setup mock configs
        llama_config = create_mock_model_config("llama")
        deepseek_config = create_mock_model_config("deepseek_v3")

        def mock_fetch_config(model_name):
            if "llama" in model_name.lower():
                return llama_config
            elif "deepseek" in model_name.lower():
                return deepseek_config
            else:
                raise ValueError(f"Unknown model: {model_name}")

        self.mock_config_manager.fetch_config.side_effect = mock_fetch_config

        # Step 1: Create models
        models = [
            ModelFactory.create_model("meta-llama/Llama-2-7b-hf"),
            ModelFactory.create_model("deepseek-ai/DeepSeek-R1"),
        ]

        assert len(models) == 2
        assert isinstance(models[0], DenseModel)
        assert isinstance(models[1], MoEModel)

        # Step 2: Create comparator
        comparator = Comparator()

        # Step 3: Add models to comparison
        for model in models:
            comparator.add_model(model)

        # Step 4: Run comparison
        comparison_result = comparator.compare_models(
            sequence_length=2048, batch_size=1, parallel_config=ParallelConfig()
        )

        # Step 5: Verify comparison results
        assert len(comparison_result.models) == 2
        assert "total_params" in comparison_result.metrics
        assert "flops_forward" in comparison_result.metrics
        assert "memory_total" in comparison_result.metrics

        # Each metric should have values for both models
        for metric_name, values in comparison_result.metrics.items():
            assert len(values) == 2
            assert all(isinstance(v, (int, float)) for v in values)

        # Step 6: Test export functionality
        comparison_dict = comparison_result.to_dict()
        assert "models" in comparison_dict
        assert "metrics" in comparison_dict
        assert "timestamp" in comparison_dict

        # Step 7: Test DataFrame conversion
        df = comparison_result.to_dataframe()
        assert len(df) == 2  # Two models
        assert "model_name" in df.columns
        assert "total_params" in df.columns

    def test_configuration_management_workflow(self):
        """Test configuration management workflow."""
        # Create temporary config directory
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(cache_dir=temp_dir)

            # Mock HuggingFace API response
            mock_config = create_mock_model_config("llama")

            with patch(
                "transformers.AutoConfig.from_pretrained"
            ) as mock_from_pretrained:
                mock_from_pretrained.return_value = MockConfig(mock_config)

                # Step 1: Fetch config (should cache it)
                config1 = config_manager.fetch_config("meta-llama/Llama-2-7b-hf")
                assert config1 is not None

                # Step 2: Fetch same config again (should use cache)
                config2 = config_manager.fetch_config("meta-llama/Llama-2-7b-hf")
                assert config2 == config1

                # Should only call API once due to caching
                assert mock_from_pretrained.call_count == 1

                # Step 3: Force refresh
                config3 = config_manager.fetch_config(
                    "meta-llama/Llama-2-7b-hf", force_refresh=True
                )
                assert config3 is not None

                # Should call API again
                assert mock_from_pretrained.call_count == 2

                # Step 4: Check cache file exists
                cache_files = list(Path(temp_dir).glob("*.json"))
                assert len(cache_files) > 0

    def test_error_handling_workflow(self):
        """Test error handling throughout the workflow."""
        # Test with invalid model name
        self.mock_config_manager.fetch_config.side_effect = Exception("Model not found")

        with pytest.raises(Exception):
            ModelFactory.create_model("invalid/model")

        # Test with unsupported architecture
        unsupported_config = {"model_type": "unsupported_arch"}
        self.mock_config_manager.fetch_config.side_effect = None
        self.mock_config_manager.fetch_config.return_value = unsupported_config

        with pytest.raises(Exception):  # Should raise ModelNotSupportedError
            ModelFactory.create_model("unsupported/model")

        # Test with invalid parallel configuration
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")

        # Invalid tensor parallel size (not divisible by num_heads)
        invalid_config = ParallelConfig(
            tensor_parallel_size=5
        )  # 32 heads not divisible by 5
        assert not model.validate_parallel_config(invalid_config)

        # Should still be able to get metrics with invalid config (might use defaults)
        metrics = model.get_metrics(parallel_config=invalid_config)
        assert_metrics_valid(metrics)

    def test_caching_integration(self):
        """Test caching integration throughout the workflow."""
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")

        # First call should compute and cache
        metrics1 = model.get_metrics(sequence_length=2048, batch_size=1)

        # Second call should use cache
        metrics2 = model.get_metrics(sequence_length=2048, batch_size=1)

        # Should be identical
        assert metrics1.total_params == metrics2.total_params
        assert metrics1.flops_forward == metrics2.flops_forward
        assert metrics1.memory_total == metrics2.memory_total

        # Different parameters should not use cache
        metrics3 = model.get_metrics(sequence_length=1024, batch_size=1)
        assert metrics3.flops_forward != metrics1.flops_forward

        # Test cache invalidation
        model.invalidate_cache()

        # Should recompute after invalidation
        metrics4 = model.get_metrics(sequence_length=2048, batch_size=1)
        assert metrics4.total_params == metrics1.total_params  # Same values

    def test_parallel_strategy_integration(self):
        """Test parallel strategy integration."""
        from llm_modeling_metrics.core.parallel_strategies import (
            ParallelStrategyCalculator,
        )

        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")

        # Step 1: Get optimal parallel configuration suggestions
        suggestions = ParallelStrategyCalculator.suggest_optimal_parallel_config(
            model, num_devices=8, memory_per_device_gb=80.0
        )

        assert len(suggestions) > 0

        # Step 2: Validate suggested configurations
        for suggestion in suggestions:
            assert ParallelStrategyCalculator.validate_parallel_config(
                suggestion, model._parsed_config
            )

        # Step 3: Analyze memory requirements for best suggestion
        best_config = suggestions[0]
        memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
            model, best_config
        )

        assert "total_memory_gb" in memory_analysis
        assert "memory_per_device_gb" in memory_analysis
        assert memory_analysis["memory_per_device_gb"] <= 80.0  # Should meet constraint

        # Step 4: Analyze performance characteristics
        perf_analysis = ParallelStrategyCalculator.analyze_performance_characteristics(
            model, best_config
        )

        assert "tokens_per_second" in perf_analysis
        assert "compute_efficiency" in perf_analysis
        assert perf_analysis["tokens_per_second"] > 0

        # Step 5: Get optimization suggestions
        constraints = {
            "num_devices": 8,
            "memory_per_device_gb": 80.0,
            "bandwidth_gbps": 100.0,
            "latency_us": 10.0,
        }

        optimization_suggestions = (
            ParallelStrategyCalculator.generate_optimization_suggestions(
                model, best_config, constraints
            )
        )

        assert "configuration_alternatives" in optimization_suggestions
        assert "current_performance" in optimization_suggestions


class TestEndToEndScenarios:
    """Test realistic end-to-end scenarios."""

    def setup_method(self):
        """Set up test environment."""
        ModelFactory.register_model("llama", DenseModel)
        ModelFactory.register_model("deepseek", MoEModel)

    def test_research_comparison_scenario(self):
        """Test scenario: Researcher comparing multiple models for deployment."""
        # Mock configuration manager
        mock_config_manager = Mock(spec=ConfigManager)

        def mock_fetch_config(model_name):
            if "llama-2-7b" in model_name.lower():
                return create_mock_model_config(
                    "llama",
                    hidden_size=4096,
                    num_hidden_layers=32,
                    num_attention_heads=32,
                )
            elif "llama-2-13b" in model_name.lower():
                return create_mock_model_config(
                    "llama",
                    hidden_size=5120,
                    num_hidden_layers=40,
                    num_attention_heads=40,
                )
            elif "deepseek" in model_name.lower():
                return create_mock_model_config("deepseek_v3")
            else:
                raise ValueError(f"Unknown model: {model_name}")

        mock_config_manager.fetch_config.side_effect = mock_fetch_config
        ModelFactory.set_config_manager(mock_config_manager)

        # Models to compare
        model_names = [
            "meta-llama/Llama-2-7b-hf",
            "meta-llama/Llama-2-13b-hf",
            "deepseek-ai/DeepSeek-R1",
        ]

        # Create comparator
        comparator = Comparator()

        # Add models
        models = []
        for model_name in model_names:
            model = ModelFactory.create_model(model_name)
            models.append(model)
            comparator.add_model(model)

        # Compare with different configurations
        configurations = [
            {"sequence_length": 2048, "batch_size": 1},
            {"sequence_length": 4096, "batch_size": 1},
            {"sequence_length": 2048, "batch_size": 4},
        ]

        results = []
        for config in configurations:
            result = comparator.compare_models(**config)
            results.append(result)

            # Verify results
            assert len(result.models) == 3
            assert len(result.metrics["total_params"]) == 3

            # Verify ordering (13B should have more params than 7B)
            params = result.metrics["total_params"]
            llama_7b_params = params[0]  # First model
            llama_13b_params = params[1]  # Second model
            assert llama_13b_params > llama_7b_params

        # Export results for analysis
        for i, result in enumerate(results):
            result_dict = result.to_dict()
            assert "models" in result_dict
            assert "metrics" in result_dict

            # Convert to DataFrame for analysis
            df = result.to_dataframe()
            assert len(df) == 3
            assert "model_name" in df.columns
            assert "total_params" in df.columns
            assert "flops_forward" in df.columns
            assert "memory_total" in df.columns

    def test_deployment_optimization_scenario(self):
        """Test scenario: Optimizing model deployment for specific hardware."""
        # Mock configuration
        mock_config_manager = Mock(spec=ConfigManager)
        llama_config = create_mock_model_config("llama")
        mock_config_manager.fetch_config.return_value = llama_config
        ModelFactory.set_config_manager(mock_config_manager)

        # Create model
        model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")

        # Hardware constraints
        hardware_configs = [
            {"num_devices": 2, "memory_per_device_gb": 40.0, "name": "Small cluster"},
            {"num_devices": 8, "memory_per_device_gb": 80.0, "name": "Medium cluster"},
            {"num_devices": 16, "memory_per_device_gb": 160.0, "name": "Large cluster"},
        ]

        from llm_modeling_metrics.core.parallel_strategies import (
            ParallelStrategyCalculator,
        )

        optimization_results = []

        for hw_config in hardware_configs:
            # Get optimal configurations
            suggestions = ParallelStrategyCalculator.suggest_optimal_parallel_config(
                model,
                num_devices=hw_config["num_devices"],
                memory_per_device_gb=hw_config["memory_per_device_gb"],
            )

            assert len(suggestions) > 0

            # Analyze best configuration
            best_config = suggestions[0]

            # Memory analysis
            memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
                model, best_config
            )

            # Performance analysis
            perf_analysis = (
                ParallelStrategyCalculator.analyze_performance_characteristics(
                    model, best_config
                )
            )

            # Store results
            optimization_results.append(
                {
                    "hardware": hw_config["name"],
                    "config": best_config,
                    "memory_per_device_gb": memory_analysis["memory_per_device_gb"],
                    "tokens_per_second": perf_analysis["tokens_per_second"],
                    "compute_efficiency": perf_analysis["compute_efficiency"],
                }
            )

            # Verify constraints are met
            assert (
                memory_analysis["memory_per_device_gb"]
                <= hw_config["memory_per_device_gb"]
            )

        # Verify that larger clusters generally have better performance
        assert len(optimization_results) == 3

        # Should have different configurations for different hardware
        configs = [result["config"] for result in optimization_results]
        assert not all(
            c.tensor_parallel_size == configs[0].tensor_parallel_size for c in configs
        )

    def test_model_development_scenario(self):
        """Test scenario: Model developer analyzing architecture choices."""
        # Mock different model architectures
        mock_config_manager = Mock(spec=ConfigManager)

        def mock_fetch_config(model_name):
            if "small" in model_name:
                return create_mock_model_config(
                    "llama",
                    hidden_size=2048,
                    num_hidden_layers=16,
                    num_attention_heads=16,
                    intermediate_size=5504,
                )
            elif "medium" in model_name:
                return create_mock_model_config(
                    "llama",
                    hidden_size=4096,
                    num_hidden_layers=32,
                    num_attention_heads=32,
                    intermediate_size=11008,
                )
            elif "large" in model_name:
                return create_mock_model_config(
                    "llama",
                    hidden_size=8192,
                    num_hidden_layers=64,
                    num_attention_heads=64,
                    intermediate_size=22016,
                )
            else:
                raise ValueError(f"Unknown model: {model_name}")

        mock_config_manager.fetch_config.side_effect = mock_fetch_config
        ModelFactory.set_config_manager(mock_config_manager)

        # Different model sizes
        model_variants = [
            "custom/small-model",
            "custom/medium-model",
            "custom/large-model",
        ]

        # Create models and analyze
        analysis_results = []

        for model_name in model_variants:
            model = ModelFactory.create_model(model_name)

            # Get comprehensive metrics
            metrics = model.get_metrics(sequence_length=2048, batch_size=1)

            # Analyze different sequence lengths
            seq_length_analysis = {}
            for seq_len in [1024, 2048, 4096, 8192]:
                seq_metrics = model.get_metrics(sequence_length=seq_len, batch_size=1)
                seq_length_analysis[seq_len] = {
                    "flops_forward": seq_metrics.flops_forward,
                    "memory_total": seq_metrics.memory_total,
                    "flops_per_token": seq_metrics.flops_per_token,
                }

            # Analyze parallel configurations
            parallel_analysis = {}
            for tp_size in [1, 2, 4, 8]:
                if tp_size <= metrics.total_params // 1000000:  # Rough check
                    parallel_config = ParallelConfig(tensor_parallel_size=tp_size)
                    if model.validate_parallel_config(parallel_config):
                        parallel_metrics = model.get_metrics(
                            parallel_config=parallel_config
                        )
                        parallel_analysis[tp_size] = {
                            "memory_total": parallel_metrics.memory_total,
                            "valid": True,
                        }
                    else:
                        parallel_analysis[tp_size] = {"valid": False}

            analysis_results.append(
                {
                    "model_name": model_name,
                    "base_metrics": metrics,
                    "sequence_length_analysis": seq_length_analysis,
                    "parallel_analysis": parallel_analysis,
                }
            )

        # Verify analysis results
        assert len(analysis_results) == 3

        # Verify scaling relationships
        for i in range(1, len(analysis_results)):
            current = analysis_results[i]["base_metrics"]
            previous = analysis_results[i - 1]["base_metrics"]

            # Larger models should have more parameters and FLOPs
            assert current.total_params > previous.total_params
            assert current.flops_forward > previous.flops_forward
            assert current.memory_total > previous.memory_total

        # Verify sequence length scaling
        for result in analysis_results:
            seq_analysis = result["sequence_length_analysis"]
            seq_lengths = sorted(seq_analysis.keys())

            for i in range(1, len(seq_lengths)):
                current_seq = seq_lengths[i]
                previous_seq = seq_lengths[i - 1]

                # FLOPs should scale with sequence length
                assert (
                    seq_analysis[current_seq]["flops_forward"]
                    > seq_analysis[previous_seq]["flops_forward"]
                )

    def test_production_monitoring_scenario(self):
        """Test scenario: Production monitoring and optimization."""
        # Mock configuration
        mock_config_manager = Mock(spec=ConfigManager)
        production_config = create_mock_model_config("llama")
        mock_config_manager.fetch_config.return_value = production_config
        ModelFactory.set_config_manager(mock_config_manager)

        # Create production model
        model = ModelFactory.create_model("production/llama-model")

        # Current production configuration
        current_config = ParallelConfig(tensor_parallel_size=4, data_parallel_size=2)

        # Baseline metrics
        baseline_metrics = model.get_metrics(
            sequence_length=2048,
            batch_size=8,  # Production batch size
            parallel_config=current_config,
        )

        # Performance monitoring over different workloads
        workload_scenarios = [
            {
                "sequence_length": 1024,
                "batch_size": 16,
                "name": "Short sequences, large batch",
            },
            {
                "sequence_length": 4096,
                "batch_size": 4,
                "name": "Long sequences, small batch",
            },
            {"sequence_length": 2048, "batch_size": 8, "name": "Balanced workload"},
            {"sequence_length": 8192, "batch_size": 1, "name": "Very long sequences"},
        ]

        workload_analysis = []

        for scenario in workload_scenarios:
            metrics = model.get_metrics(
                sequence_length=scenario["sequence_length"],
                batch_size=scenario["batch_size"],
                parallel_config=current_config,
            )

            # Calculate relative performance
            flops_ratio = metrics.flops_forward / baseline_metrics.flops_forward
            memory_ratio = metrics.memory_total / baseline_metrics.memory_total

            workload_analysis.append(
                {
                    "scenario": scenario["name"],
                    "metrics": metrics,
                    "flops_ratio": flops_ratio,
                    "memory_ratio": memory_ratio,
                    "sequence_length": scenario["sequence_length"],
                    "batch_size": scenario["batch_size"],
                }
            )

        # Optimization analysis
        from llm_modeling_metrics.core.parallel_strategies import (
            ParallelStrategyCalculator,
        )

        constraints = {
            "num_devices": 8,
            "memory_per_device_gb": 80.0,
            "bandwidth_gbps": 100.0,
            "latency_us": 10.0,
        }

        optimization_suggestions = (
            ParallelStrategyCalculator.generate_optimization_suggestions(
                model, current_config, constraints
            )
        )

        # Verify monitoring results
        assert len(workload_analysis) == 4

        # Verify that different workloads have different resource requirements
        flops_ratios = [w["flops_ratio"] for w in workload_analysis]
        memory_ratios = [w["memory_ratio"] for w in workload_analysis]

        assert max(flops_ratios) > min(flops_ratios)  # Should have variation
        assert max(memory_ratios) > min(memory_ratios)  # Should have variation

        # Verify optimization suggestions
        assert "configuration_alternatives" in optimization_suggestions
        assert "current_performance" in optimization_suggestions
        assert len(optimization_suggestions["configuration_alternatives"]) > 0

        # All suggested configurations should be valid
        for alt in optimization_suggestions["configuration_alternatives"]:
            assert ParallelStrategyCalculator.validate_parallel_config(
                alt["config"], model._parsed_config
            )


class TestRealWorldIntegration:
    """Test integration with real-world-like scenarios."""

    def setup_method(self):
        """Set up test environment."""
        ModelFactory.register_model("llama", DenseModel)
        ModelFactory.register_model("deepseek", MoEModel)

    @patch("transformers.AutoConfig.from_pretrained")
    def test_huggingface_integration_simulation(self, mock_from_pretrained):
        """Test integration with HuggingFace-like API."""

        # Simulate HuggingFace API responses
        def mock_hf_response(model_name, **kwargs):
            if "llama" in model_name.lower():
                config_dict = create_mock_model_config("llama")
            elif "deepseek" in model_name.lower():
                config_dict = create_mock_model_config("deepseek_v3")
            else:
                raise ValueError(f"Model {model_name} not found")

            return MockConfig(config_dict)

        mock_from_pretrained.side_effect = mock_hf_response

        # Create config manager (will use mocked HF API)
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(cache_dir=temp_dir)
            ModelFactory.set_config_manager(config_manager)

            # Test fetching different models
            model_names = ["meta-llama/Llama-2-7b-hf", "deepseek-ai/DeepSeek-R1"]

            models = []
            for model_name in model_names:
                model = ModelFactory.create_model(model_name)
                models.append(model)

                # Verify model was created correctly
                assert model.model_name == model_name
                assert hasattr(model, "_parsed_config")

                # Test basic functionality
                metrics = model.get_metrics()
                assert_metrics_valid(metrics)

            # Verify caching worked
            cache_files = list(Path(temp_dir).glob("*.json"))
            assert len(cache_files) >= 2  # Should have cached both models

            # Test cache usage (should not call API again)
            call_count_before = mock_from_pretrained.call_count
            model_cached = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")
            assert (
                mock_from_pretrained.call_count == call_count_before
            )  # No additional calls

    def test_large_scale_comparison(self):
        """Test large-scale model comparison scenario."""
        # Mock multiple model configurations
        mock_config_manager = Mock(spec=ConfigManager)

        model_configs = {
            "small-llama": create_mock_model_config(
                "llama", hidden_size=2048, num_hidden_layers=16, num_attention_heads=16
            ),
            "medium-llama": create_mock_model_config(
                "llama", hidden_size=4096, num_hidden_layers=32, num_attention_heads=32
            ),
            "large-llama": create_mock_model_config(
                "llama", hidden_size=8192, num_hidden_layers=64, num_attention_heads=64
            ),
            "small-deepseek": create_mock_model_config(
                "deepseek_v3",
                hidden_size=4096,
                num_hidden_layers=28,
                n_routed_experts=64,
            ),
            "large-deepseek": create_mock_model_config(
                "deepseek_v3",
                hidden_size=7168,
                num_hidden_layers=61,
                n_routed_experts=256,
            ),
        }

        def mock_fetch_config(model_name):
            for key, config in model_configs.items():
                if key in model_name:
                    return config
            raise ValueError(f"Unknown model: {model_name}")

        mock_config_manager.fetch_config.side_effect = mock_fetch_config
        ModelFactory.set_config_manager(mock_config_manager)

        # Create all models
        model_names = [f"test/{key}" for key in model_configs.keys()]
        models = []

        for model_name in model_names:
            model = ModelFactory.create_model(model_name)
            models.append(model)

        # Large-scale comparison
        comparator = Comparator()
        for model in models:
            comparator.add_model(model)

        # Compare with multiple configurations
        comparison_configs = [
            {"sequence_length": 1024, "batch_size": 1},
            {"sequence_length": 2048, "batch_size": 1},
            {"sequence_length": 4096, "batch_size": 1},
        ]

        comparison_results = []
        for config in comparison_configs:
            result = comparator.compare_models(**config)
            comparison_results.append(result)

            # Verify results
            assert len(result.models) == len(models)
            assert len(result.metrics["total_params"]) == len(models)

            # Export to different formats
            result_dict = result.to_dict()
            df = result.to_dataframe()

            assert len(df) == len(models)
            assert "model_name" in df.columns

        # Verify scaling relationships across all models
        for result in comparison_results:
            df = result.to_dataframe()

            # Sort by total parameters
            df_sorted = df.sort_values("total_params")

            # Verify that FLOPs generally increase with parameters
            params = df_sorted["total_params"].values
            flops = df_sorted["flops_forward"].values

            # Should be generally increasing (allowing for some variation due to architecture differences)
            assert (
                flops[-1] > flops[0]
            )  # Largest model should have more FLOPs than smallest

    def test_performance_regression_detection(self):
        """Test performance regression detection scenario."""
        # Mock baseline and current model configurations
        mock_config_manager = Mock(spec=ConfigManager)

        baseline_config = create_mock_model_config("llama")
        current_config = create_mock_model_config(
            "llama",
            # Simulate a change that might affect performance
            intermediate_size=baseline_config["intermediate_size"] + 1000,
        )

        def mock_fetch_config(model_name):
            if "baseline" in model_name:
                return baseline_config
            elif "current" in model_name:
                return current_config
            else:
                raise ValueError(f"Unknown model: {model_name}")

        mock_config_manager.fetch_config.side_effect = mock_fetch_config
        ModelFactory.set_config_manager(mock_config_manager)

        # Create baseline and current models
        baseline_model = ModelFactory.create_model("test/baseline-model")
        current_model = ModelFactory.create_model("test/current-model")

        # Performance comparison
        test_configs = [
            {"sequence_length": 2048, "batch_size": 1},
            {"sequence_length": 4096, "batch_size": 1},
        ]

        performance_comparisons = []

        for config in test_configs:
            baseline_metrics = baseline_model.get_metrics(**config)
            current_metrics = current_model.get_metrics(**config)

            # Calculate performance differences
            param_ratio = current_metrics.total_params / baseline_metrics.total_params
            flops_ratio = current_metrics.flops_forward / baseline_metrics.flops_forward
            memory_ratio = current_metrics.memory_total / baseline_metrics.memory_total

            performance_comparisons.append(
                {
                    "config": config,
                    "param_ratio": param_ratio,
                    "flops_ratio": flops_ratio,
                    "memory_ratio": memory_ratio,
                    "baseline_metrics": baseline_metrics,
                    "current_metrics": current_metrics,
                }
            )

        # Regression detection logic
        regression_threshold = 1.1  # 10% increase threshold

        for comparison in performance_comparisons:
            # Check for regressions
            param_regression = comparison["param_ratio"] > regression_threshold
            flops_regression = comparison["flops_ratio"] > regression_threshold
            memory_regression = comparison["memory_ratio"] > regression_threshold

            # In this test, we expect some increase due to larger intermediate_size
            assert comparison["param_ratio"] > 1.0  # Should have more parameters
            assert comparison["flops_ratio"] > 1.0  # Should have more FLOPs
            assert comparison["memory_ratio"] > 1.0  # Should use more memory

            # But increases should be reasonable (not too large)
            assert comparison["param_ratio"] < 2.0  # Less than 2x increase
            assert comparison["flops_ratio"] < 2.0  # Less than 2x increase
            assert comparison["memory_ratio"] < 2.0  # Less than 2x increase

        # Verify that both configurations show consistent trends
        assert len(performance_comparisons) == 2

        # Ratios should be similar across different sequence lengths
        ratio_diff_params = abs(
            performance_comparisons[0]["param_ratio"]
            - performance_comparisons[1]["param_ratio"]
        )
        assert (
            ratio_diff_params < 0.01
        )  # Parameters shouldn't change with sequence length

"""
Unit tests for DenseModel implementation.
"""

from unittest.mock import Mock, patch

import pytest

from llm_modeling_metrics.core.base_model import ModelMetrics, ParallelConfig
from llm_modeling_metrics.models.dense_model import DenseModel
from tests.conftest import (
    MockConfig,
    assert_metrics_valid,
    assert_parallel_config_valid,
    assert_shapes_valid,
    create_mock_model_config,
)


class TestDenseModelInitialization:
    """Test DenseModel initialization and configuration parsing."""

    def test_initialization_with_valid_config(self):
        """Test DenseModel initialization with valid configuration."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("meta-llama/Llama-2-7b-hf", config)

        assert model.model_name == "meta-llama/Llama-2-7b-hf"
        assert model.config == config
        assert model._parsed_config["hidden_size"] == 4096
        assert model._parsed_config["num_attention_heads"] == 32
        assert model._parsed_config["num_key_value_heads"] == 32
        assert model._parsed_config["intermediate_size"] == 11008
        assert model._parsed_config["head_dim"] == 128  # 4096 / 32

    def test_initialization_without_config(self):
        """Test DenseModel initialization without config."""
        model = DenseModel("test-model", None)

        # Should be able to create model but methods should fail
        assert model.model_name == "test-model"
        assert model.config is None

        # Calling methods should raise error since config is None
        with pytest.raises(KeyError):
            model.compute_attention_params()

    def test_config_parsing_with_gqa(self):
        """Test configuration parsing with Grouped Query Attention."""
        config_dict = create_mock_model_config("llama")
        config_dict["num_key_value_heads"] = 8  # GQA with 8 KV heads
        config = MockConfig(config_dict)

        model = DenseModel("test-model", config)

        assert model._parsed_config["num_attention_heads"] == 32
        assert model._parsed_config["num_key_value_heads"] == 8
        assert model._parsed_config["head_dim"] == 128  # Still based on attention heads

    def test_config_parsing_without_kv_heads(self):
        """Test configuration parsing when num_key_value_heads is not specified."""
        config_dict = create_mock_model_config("llama")
        del config_dict["num_key_value_heads"]  # Remove KV heads
        config = MockConfig(config_dict)

        model = DenseModel("test-model", config)

        # Should default to same as attention heads
        assert model._parsed_config["num_key_value_heads"] == 32

    def test_config_validation_invalid_dimensions(self):
        """Test configuration validation with invalid dimensions."""
        # Test with zero hidden size
        config_dict = create_mock_model_config("llama")
        config_dict["hidden_size"] = 0
        config = MockConfig(config_dict)

        with pytest.raises(
            ValueError, match="Invalid or missing configuration parameter: hidden_size"
        ):
            DenseModel("test-model", config)

        # Test with zero attention heads
        config_dict = create_mock_model_config("llama")
        config_dict["num_attention_heads"] = 0
        config = MockConfig(config_dict)

        with pytest.raises((ValueError, ZeroDivisionError)):
            DenseModel("test-model", config)

    def test_config_validation_head_dimension_mismatch(self):
        """Test configuration validation with non-divisible head dimensions."""
        config_dict = create_mock_model_config("llama")
        config_dict["hidden_size"] = 4097  # Not divisible by 32 heads
        config = MockConfig(config_dict)

        with pytest.raises(
            ValueError, match="hidden_size must be divisible by num_attention_heads"
        ):
            DenseModel("test-model", config)


class TestDenseModelParameterComputation:
    """Test parameter computation methods for DenseModel."""

    def test_attention_params_standard(self):
        """Test attention parameter computation for standard attention."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        attention_params = model.compute_attention_params()

        # Expected: Q proj + K proj + V proj + O proj, all multiplied by num_layers
        hidden_size = 4096
        num_heads = 32
        num_kv_heads = 32
        head_dim = 128
        num_layers = 32

        q_params = hidden_size * (num_heads * head_dim)
        k_params = hidden_size * (num_kv_heads * head_dim)
        v_params = hidden_size * (num_kv_heads * head_dim)
        o_params = (num_heads * head_dim) * hidden_size

        expected = (q_params + k_params + v_params + o_params) * num_layers
        assert attention_params == expected

    def test_attention_params_gqa(self):
        """Test attention parameter computation with Grouped Query Attention."""
        config_dict = create_mock_model_config("llama")
        config_dict["num_key_value_heads"] = 8  # GQA
        config = MockConfig(config_dict)
        model = DenseModel("test-model", config)

        attention_params = model.compute_attention_params()

        # Expected: Q proj (full) + K,V proj (reduced) + O proj (full), multiplied by num_layers
        hidden_size = 4096
        num_heads = 32
        num_kv_heads = 8
        head_dim = 128
        num_layers = 32

        q_params = hidden_size * (num_heads * head_dim)
        k_params = hidden_size * (num_kv_heads * head_dim)
        v_params = hidden_size * (num_kv_heads * head_dim)
        o_params = (num_heads * head_dim) * hidden_size
        expected = (q_params + k_params + v_params + o_params) * num_layers

        assert attention_params == expected

    def test_mlp_params_gated_activation(self):
        """Test MLP parameter computation with gated activation."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        mlp_params = model.compute_mlp_params()

        # Expected: (gate_proj + up_proj + down_proj) * num_layers
        hidden_size = 4096
        intermediate_size = 11008
        num_layers = 32
        expected = (
            hidden_size * intermediate_size  # gate_proj
            + hidden_size * intermediate_size  # up_proj
            + intermediate_size * hidden_size  # down_proj
        ) * num_layers
        assert mlp_params == expected

    def test_embedding_params_tied(self):
        """Test embedding parameter computation with tied embeddings."""
        config_dict = create_mock_model_config("llama")
        config_dict["tie_word_embeddings"] = True
        config = MockConfig(config_dict)
        model = DenseModel("test-model", config)

        embedding_params = model.compute_embedding_params()

        # Expected: only input embeddings (output embeddings are tied)
        expected = 32000 * 4096
        assert embedding_params == expected

    def test_embedding_params_untied(self):
        """Test embedding parameter computation with untied embeddings."""
        config_dict = create_mock_model_config("llama")
        config_dict["tie_word_embeddings"] = False
        config = MockConfig(config_dict)
        model = DenseModel("test-model", config)

        embedding_params = model.compute_embedding_params()

        # Expected: input + output embeddings
        expected = 2 * 32000 * 4096
        assert embedding_params == expected

    def test_total_params_consistency(self):
        """Test that total parameters equals sum of components."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        attention_params = model.compute_attention_params()
        mlp_params = model.compute_mlp_params()
        embedding_params = model.compute_embedding_params()
        total_params = model.get_total_params()

        expected_total = attention_params + mlp_params + embedding_params
        assert total_params == expected_total


class TestDenseModelFLOPsComputation:
    """Test FLOP computation methods for DenseModel."""

    def test_flops_computation_basic(self):
        """Test basic FLOP computation."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        flops = model.compute_flops(sequence_length=2048, batch_size=1)

        assert "attention" in flops
        assert "mlp" in flops
        assert "layernorm" in flops
        assert "final_layernorm" in flops
        assert "embeddings" in flops
        assert flops["attention"] > 0
        assert flops["mlp"] > 0
        assert flops["layernorm"] > 0

    def test_flops_scaling_with_sequence_length(self):
        """Test FLOP scaling with sequence length."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        flops_1k = model.compute_flops(sequence_length=1024, batch_size=1)
        flops_2k = model.compute_flops(sequence_length=2048, batch_size=1)

        # FLOPs should scale roughly quadratically with sequence length for attention
        # and linearly for MLP
        total_1k = sum(flops_1k.values())
        total_2k = sum(flops_2k.values())
        assert total_2k > total_1k

        # Attention should scale more than MLP
        attention_ratio = flops_2k["attention"] / flops_1k["attention"]
        mlp_ratio = flops_2k["mlp"] / flops_1k["mlp"]
        assert attention_ratio > mlp_ratio

    def test_flops_scaling_with_batch_size(self):
        """Test FLOP scaling with batch size."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        flops_b1 = model.compute_flops(sequence_length=2048, batch_size=1)
        flops_b4 = model.compute_flops(sequence_length=2048, batch_size=4)

        # FLOPs should scale linearly with batch size
        total_b1 = sum(flops_b1.values())
        total_b4 = sum(flops_b4.values())
        assert abs(total_b4 / total_b1 - 4.0) < 0.1

    def test_flops_with_gqa(self):
        """Test FLOP computation with Grouped Query Attention."""
        config_dict = create_mock_model_config("llama")
        config_dict["num_key_value_heads"] = 8  # GQA
        config = MockConfig(config_dict)
        model = DenseModel("test-model", config)

        flops_gqa = model.compute_flops(sequence_length=2048, batch_size=1)

        # Standard attention for comparison
        config_std = MockConfig(create_mock_model_config("llama"))
        model_std = DenseModel("test-model-std", config_std)
        flops_std = model_std.compute_flops(sequence_length=2048, batch_size=1)

        # GQA should have fewer FLOPs due to reduced KV computation
        assert flops_gqa["attention"] < flops_std["attention"]
        total_gqa = sum(flops_gqa.values())
        total_std = sum(flops_std.values())
        assert total_gqa < total_std


class TestDenseModelMemoryComputation:
    """Test memory computation methods for DenseModel."""

    def test_memory_requirements_basic(self):
        """Test basic memory requirements computation."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        memory = model.compute_memory_requirements(sequence_length=2048, batch_size=1)

        assert "parameters" in memory
        assert "activations" in memory
        assert "gradients" in memory
        assert "optimizer_states" in memory
        assert memory["parameters"] > 0
        assert memory["activations"] > 0
        assert memory["gradients"] > 0
        assert memory["optimizer_states"] > 0

    def test_memory_scaling_with_sequence_length(self):
        """Test memory scaling with sequence length."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        memory_1k = model.compute_memory_requirements(
            sequence_length=1024, batch_size=1
        )
        memory_2k = model.compute_memory_requirements(
            sequence_length=2048, batch_size=1
        )

        # Parameters should be the same
        assert memory_1k["parameters"] == memory_2k["parameters"]

        # Activations should scale with sequence length
        assert memory_2k["activations"] > memory_1k["activations"]

    def test_memory_with_gqa(self):
        """Test memory computation with Grouped Query Attention."""
        config_dict = create_mock_model_config("llama")
        config_dict["num_key_value_heads"] = 8  # GQA
        config = MockConfig(config_dict)
        model = DenseModel("test-model", config)

        memory_gqa = model.compute_memory_requirements(
            sequence_length=2048, batch_size=1
        )

        # Standard attention for comparison
        config_std = MockConfig(create_mock_model_config("llama"))
        model_std = DenseModel("test-model-std", config_std)
        memory_std = model_std.compute_memory_requirements(
            sequence_length=2048, batch_size=1
        )

        # GQA should have less parameter memory due to fewer KV parameters
        assert memory_gqa["parameters"] < memory_std["parameters"]


class TestDenseModelMatrixShapes:
    """Test matrix shape computation for DenseModel."""

    def test_matrix_shapes_basic(self):
        """Test basic matrix shape computation."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        shapes = model.get_matrix_shapes()

        assert_shapes_valid(shapes)
        assert "attention" in shapes
        assert "mlp" in shapes

        # Check attention shapes
        attention_shapes = shapes["attention"]
        assert attention_shapes["q_proj"] == (4096, 4096)
        assert attention_shapes["k_proj"] == (4096, 4096)
        assert attention_shapes["v_proj"] == (4096, 4096)
        assert attention_shapes["o_proj"] == (4096, 4096)

        # Check MLP shapes
        mlp_shapes = shapes["mlp"]
        assert mlp_shapes["gate_proj"] == (4096, 11008)
        assert mlp_shapes["up_proj"] == (4096, 11008)
        assert mlp_shapes["down_proj"] == (11008, 4096)

    def test_matrix_shapes_with_gqa(self):
        """Test matrix shapes with Grouped Query Attention."""
        config_dict = create_mock_model_config("llama")
        config_dict["num_key_value_heads"] = 8  # GQA
        config = MockConfig(config_dict)
        model = DenseModel("test-model", config)

        shapes = model.get_matrix_shapes()

        # Q projection should be full size
        assert shapes["attention"]["q_proj"] == (4096, 4096)

        # K and V projections should be reduced
        head_dim = 128
        kv_size = 8 * head_dim  # 8 KV heads * 128 head_dim = 1024
        assert shapes["attention"]["k_proj"] == (4096, kv_size)
        assert shapes["attention"]["v_proj"] == (4096, kv_size)

        # O projection should be full size
        assert shapes["attention"]["o_proj"] == (4096, 4096)

    def test_matrix_shapes_with_tensor_parallel(self):
        """Test matrix shapes with tensor parallelism."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        parallel_config = ParallelConfig(tensor_parallel_size=4)
        shapes = model.get_matrix_shapes(parallel_config)

        # Column-parallel operations (split output dimension)
        assert shapes["attention"]["q_proj"] == (4096, 1024)  # 4096 / 4
        assert shapes["attention"]["k_proj"] == (4096, 1024)
        assert shapes["attention"]["v_proj"] == (4096, 1024)
        assert shapes["mlp"]["gate_proj"] == (4096, 2752)  # 11008 / 4
        assert shapes["mlp"]["up_proj"] == (4096, 2752)

        # Row-parallel operations (split input dimension)
        assert shapes["attention"]["o_proj"] == (1024, 4096)  # 4096 / 4
        assert shapes["mlp"]["down_proj"] == (2752, 4096)  # 11008 / 4


class TestDenseModelValidation:
    """Test validation methods for DenseModel."""

    def test_parallel_config_validation_valid(self):
        """Test validation of valid parallel configurations."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        valid_configs = [
            ParallelConfig(tensor_parallel_size=1),
            ParallelConfig(tensor_parallel_size=2),
            ParallelConfig(tensor_parallel_size=4),
            ParallelConfig(tensor_parallel_size=8),
            ParallelConfig(tensor_parallel_size=16),
            ParallelConfig(tensor_parallel_size=32),  # Equal to num_heads
        ]

        for parallel_config in valid_configs:
            assert model.validate_parallel_config(parallel_config)

    def test_parallel_config_validation_invalid(self):
        """Test validation of invalid parallel configurations."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        invalid_configs = [
            ParallelConfig(tensor_parallel_size=3),  # 32 not divisible by 3
            ParallelConfig(tensor_parallel_size=5),  # 32 not divisible by 5
            ParallelConfig(tensor_parallel_size=64),  # Larger than num_heads
        ]

        for parallel_config in invalid_configs:
            assert not model.validate_parallel_config(parallel_config)

    def test_parallel_config_validation_with_gqa(self):
        """Test parallel config validation with GQA."""
        config_dict = create_mock_model_config("llama")
        config_dict["num_key_value_heads"] = 8  # GQA
        config = MockConfig(config_dict)
        model = DenseModel("test-model", config)

        # Should validate based on both attention heads and KV heads
        valid_config = ParallelConfig(
            tensor_parallel_size=8
        )  # Both 32 and 8 are divisible by 8
        assert model.validate_parallel_config(valid_config)

        # This should be invalid because KV heads (8) is not divisible by 16
        invalid_config = ParallelConfig(tensor_parallel_size=16)  # 8 % 16 != 0
        assert not model.validate_parallel_config(invalid_config)


class TestDenseModelMetrics:
    """Test comprehensive metrics generation for DenseModel."""

    def test_get_metrics_basic(self):
        """Test basic metrics generation."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("meta-llama/Llama-2-7b-hf", config)

        metrics = model.get_metrics()

        assert_metrics_valid(metrics)
        assert metrics.model_name == "meta-llama/Llama-2-7b-hf"
        assert metrics.architecture == "llama"
        assert metrics.sequence_length == 2048  # default
        assert metrics.batch_size == 1  # default

    def test_get_metrics_with_custom_params(self):
        """Test metrics generation with custom parameters."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        parallel_config = ParallelConfig(tensor_parallel_size=4)
        metrics = model.get_metrics(
            sequence_length=1024, batch_size=2, parallel_config=parallel_config
        )

        assert_metrics_valid(metrics)
        assert metrics.sequence_length == 1024
        assert metrics.batch_size == 2
        assert metrics.parallel_config == parallel_config

    def test_get_metrics_with_gqa(self):
        """Test metrics generation with GQA model."""
        config_dict = create_mock_model_config("llama")
        config_dict["num_key_value_heads"] = 8  # GQA
        config = MockConfig(config_dict)
        model = DenseModel("test-model", config)

        metrics = model.get_metrics()

        assert_metrics_valid(metrics)
        # Should have fewer parameters due to reduced KV projections
        assert metrics.attention_params > 0
        assert metrics.total_params > 0


class TestDenseModelEdgeCases:
    """Test edge cases and error conditions for DenseModel."""

    def test_minimal_model_config(self):
        """Test with minimal valid model configuration."""
        minimal_config = MockConfig(
            {
                "hidden_size": 64,
                "num_hidden_layers": 1,
                "num_attention_heads": 1,
                "intermediate_size": 128,
                "vocab_size": 1000,
                "model_type": "llama",
            }
        )

        model = DenseModel("minimal-model", minimal_config)
        metrics = model.get_metrics(sequence_length=1, batch_size=1)

        assert_metrics_valid(metrics)
        assert metrics.total_params > 0
        assert metrics.flops_forward > 0

    def test_zero_sequence_length(self):
        """Test behavior with zero sequence length."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        flops = model.compute_flops(sequence_length=0, batch_size=1)
        total_flops = sum(flops.values())
        assert total_flops == 0

        memory = model.compute_memory_requirements(sequence_length=0, batch_size=1)
        assert memory["activations"] == 0
        assert memory["parameters"] > 0  # Parameters should still exist

    def test_large_model_dimensions(self):
        """Test with large model dimensions."""
        large_config = MockConfig(
            {
                "hidden_size": 16384,
                "num_hidden_layers": 128,
                "num_attention_heads": 128,
                "intermediate_size": 65536,
                "vocab_size": 100000,
                "model_type": "llama",
            }
        )

        model = DenseModel("large-model", large_config)
        metrics = model.get_metrics()

        assert_metrics_valid(metrics)
        assert metrics.total_params > 100_000_000_000  # Should be > 100B params

    def test_architecture_inference(self):
        """Test architecture inference from model name and config."""
        test_cases = [
            ("meta-llama/Llama-2-7b-hf", {"model_type": "llama"}, "llama"),
            ("Qwen/Qwen2-7B", {"model_type": "qwen2"}, "qwen2"),
            (
                "mistralai/Mistral-7B-v0.1",
                {"model_type": "llama"},
                "llama",
            ),  # Uses llama architecture
            ("unknown/model", {"model_type": "custom"}, "custom"),
        ]

        for model_name, config_dict, expected_arch in test_cases:
            full_config = {**create_mock_model_config("llama"), **config_dict}
            config = MockConfig(full_config)
            model = DenseModel(model_name, config)
            metrics = model.get_metrics()
            assert metrics.architecture == expected_arch


class TestDenseModelCaching:
    """Test caching functionality for DenseModel."""

    @patch("llm_modeling_metrics.utils.caching.get_cache_manager")
    def test_parameter_caching(self, mock_cache_manager):
        """Test that parameter computations are cached."""
        # Setup mock cache
        mock_cache = Mock()
        mock_cache_manager.return_value.computation_cache = mock_cache

        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Call cached methods multiple times
        params1 = model._get_cached_attention_params()
        params2 = model._get_cached_attention_params()

        # Should return same result
        assert params1 == params2

    @patch("llm_modeling_metrics.utils.caching.get_cache_manager")
    def test_cache_invalidation(self, mock_cache_manager):
        """Test cache invalidation."""
        mock_cache = Mock()
        mock_cache_manager.return_value.computation_cache = mock_cache

        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Invalidate cache
        model.invalidate_cache()

        # Should call invalidate on cache (may be 0 if cache is not initialized)
        # The important thing is that the method doesn't crash
        assert mock_cache.invalidate.call_count >= 0

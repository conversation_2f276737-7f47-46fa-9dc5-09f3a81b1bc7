"""
Tests for attention analysis export utilities.
"""

import json
import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from llm_modeling_metrics.web.export_utils import (
    AttentionExporter,
    export_attention_analysis_csv,
    export_attention_analysis_png,
    export_attention_analysis_svg,
    create_export_metadata
)
from llm_modeling_metrics.web.models import (
    AttentionAnalysisResponse,
    AttentionMetricsModel,
    RooflinePointModel
)


@pytest.fixture
def sample_attention_metrics():
    """Create sample attention metrics for testing."""
    return [
        AttentionMetricsModel(
            mechanism_type="MLA",
            model_name="DeepSeek-V3",
            sequence_length=8192,
            batch_size=1,
            total_flops=147000000000,
            attention_flops=117600000000,
            projection_flops=29400000000,
            memory_capacity_bytes=288000000,
            memory_movement_bytes=288000000,
            kv_cache_bytes=201600000,
            operational_intensity=0.51,
            achieved_performance_tflops=0.147,
            hardware_name="H100",
            peak_performance_tflops=989.4,
            memory_bandwidth_gbps=3352,
            flash_attention_enabled=True,
            precision="bf16"
        ),
        AttentionMetricsModel(
            mechanism_type="GQA",
            model_name="Qwen3-MoE",
            sequence_length=8192,
            batch_size=1,
            total_flops=25200000000,
            attention_flops=20160000000,
            projection_flops=5040000000,
            memory_capacity_bytes=789000000,
            memory_movement_bytes=789000000,
            kv_cache_bytes=552300000,
            operational_intensity=0.032,
            achieved_performance_tflops=0.0252,
            hardware_name="H100",
            peak_performance_tflops=989.4,
            memory_bandwidth_gbps=3352,
            flash_attention_enabled=True,
            precision="bf16"
        )
    ]


@pytest.fixture
def sample_roofline_points():
    """Create sample roofline points for testing."""
    return [
        RooflinePointModel(
            x=0.288,  # memory_access_gb
            y=147.0,  # compute_gflops
            model_name="DeepSeek-V3",
            attention_type="MLA",
            sequence_length=8192,
            operational_intensity=0.51,
            bottleneck_type="memory"
        ),
        RooflinePointModel(
            x=0.789,
            y=25.2,
            model_name="Qwen3-MoE",
            attention_type="GQA",
            sequence_length=8192,
            operational_intensity=0.032,
            bottleneck_type="memory"
        )
    ]


@pytest.fixture
def sample_hardware_limits():
    """Create sample hardware limits for testing."""
    return {
        "H100": {
            "bf16": [0.0, 0.0, 1.0, 295.0, 2.0, 590.0, 3.0, 885.0, 3.5, 989.4]
        },
        "A800": {
            "bf16": [0.0, 0.0, 1.0, 200.0, 2.0, 312.0, 3.0, 312.0]
        }
    }


@pytest.fixture
def sample_analysis_response(sample_attention_metrics, sample_roofline_points, sample_hardware_limits):
    """Create a sample attention analysis response."""
    return AttentionAnalysisResponse(
        metrics=sample_attention_metrics,
        roofline_points=sample_roofline_points,
        hardware_limits=sample_hardware_limits,
        execution_time=2.5,
        timestamp=datetime(2024, 1, 15, 10, 30, 0)
    )


class TestAttentionExporter:
    """Test cases for AttentionExporter class."""
    
    def test_init(self):
        """Test exporter initialization."""
        exporter = AttentionExporter()
        assert exporter.default_dpi == 300
        assert exporter.default_figsize == (12, 8)
    
    def test_export_to_csv_with_metadata(self, sample_analysis_response):
        """Test CSV export with metadata."""
        exporter = AttentionExporter()
        csv_content = exporter.export_to_csv(sample_analysis_response, include_metadata=True)
        
        # Check that metadata is included
        assert "# Attention Analysis Export" in csv_content
        assert "# Generated:" in csv_content
        assert "# Execution Time (seconds):" in csv_content
        
        # Check that headers are present
        assert "model_name" in csv_content
        assert "mechanism_type" in csv_content
        assert "total_flops" in csv_content
        
        # Check that data is present
        assert "DeepSeek-V3" in csv_content
        assert "Qwen3-MoE" in csv_content
        assert "MLA" in csv_content
        assert "GQA" in csv_content
    
    def test_export_to_csv_without_metadata(self, sample_analysis_response):
        """Test CSV export without metadata."""
        exporter = AttentionExporter()
        csv_content = exporter.export_to_csv(sample_analysis_response, include_metadata=False)
        
        # Check that metadata is not included
        assert "# Attention Analysis Export" not in csv_content
        assert "# Generated:" not in csv_content
        
        # Check that headers and data are still present
        assert "model_name" in csv_content
        assert "DeepSeek-V3" in csv_content
    
    def test_export_to_csv_with_roofline_points(self, sample_analysis_response):
        """Test CSV export includes roofline points section."""
        exporter = AttentionExporter()
        csv_content = exporter.export_to_csv(sample_analysis_response)
        
        # Check roofline points section
        assert "# Roofline Plot Points" in csv_content
        assert "memory_access_gb" in csv_content
        assert "compute_gflops" in csv_content
        assert "bottleneck_type" in csv_content
    
    def test_export_to_csv_with_hardware_limits(self, sample_analysis_response):
        """Test CSV export includes hardware limits section."""
        exporter = AttentionExporter()
        csv_content = exporter.export_to_csv(sample_analysis_response)
        
        # Check hardware limits section
        assert "# Hardware Limits" in csv_content
        assert "hardware_name" in csv_content
        assert "H100" in csv_content
        assert "A800" in csv_content
    
    @patch('matplotlib.pyplot.savefig')
    @patch('matplotlib.pyplot.close')
    def test_export_to_png(self, mock_close, mock_savefig, sample_analysis_response):
        """Test PNG export functionality."""
        exporter = AttentionExporter()
        
        # Mock the savefig to avoid actual file operations
        mock_buffer = Mock()
        mock_buffer.getvalue.return_value = b"fake_png_data"
        
        with patch('io.BytesIO', return_value=mock_buffer):
            png_data = exporter.export_to_png(sample_analysis_response)
        
        assert png_data == b"fake_png_data"
        mock_savefig.assert_called_once()
        mock_close.assert_called_once()
    
    @patch('matplotlib.pyplot.savefig')
    @patch('matplotlib.pyplot.close')
    def test_export_to_svg(self, mock_close, mock_savefig, sample_analysis_response):
        """Test SVG export functionality."""
        exporter = AttentionExporter()
        
        # Mock the savefig to avoid actual file operations
        mock_buffer = Mock()
        mock_buffer.getvalue.return_value = "fake_svg_data"
        
        with patch('io.StringIO', return_value=mock_buffer):
            svg_data = exporter.export_to_svg(sample_analysis_response)
        
        assert svg_data == "fake_svg_data"
        mock_savefig.assert_called_once()
        mock_close.assert_called_once()
    
    @patch('matplotlib.pyplot.subplots')
    def test_create_roofline_plot(self, mock_subplots, sample_analysis_response):
        """Test roofline plot creation."""
        # Mock matplotlib components
        mock_fig = Mock()
        mock_ax = Mock()
        mock_subplots.return_value = (mock_fig, mock_ax)
        
        exporter = AttentionExporter()
        fig = exporter.create_roofline_plot(sample_analysis_response)
        
        # Verify plot setup calls
        mock_subplots.assert_called_once_with(1, 1, figsize=(12, 8))
        mock_ax.set_xlabel.assert_called_once()
        mock_ax.set_ylabel.assert_called_once()
        mock_ax.set_title.assert_called_once()
        mock_ax.grid.assert_called_once()
        mock_ax.legend.assert_called_once()
        
        assert fig == mock_fig
    
    def test_create_export_metadata(self, sample_analysis_response):
        """Test export metadata creation."""
        exporter = AttentionExporter()
        metadata = exporter.create_export_metadata(
            sample_analysis_response, 
            "csv",
            {"custom_field": "custom_value"}
        )
        
        # Check required fields
        assert metadata["export_format"] == "csv"
        assert "export_timestamp" in metadata
        assert "analysis_timestamp" in metadata
        assert metadata["execution_time_seconds"] == 2.5
        assert metadata["models_analyzed"] == 2
        assert metadata["hardware_analyzed"] == 1
        assert metadata["sequence_lengths"] == [8192]
        assert set(metadata["attention_mechanisms"]) == {"MLA", "GQA"}
        assert metadata["total_data_points"] == 2
        assert metadata["roofline_points"] == 2
        assert "H100" in metadata["hardware_limits"]
        assert "A800" in metadata["hardware_limits"]
        
        # Check custom field
        assert metadata["custom_field"] == "custom_value"


class TestExportFunctions:
    """Test cases for module-level export functions."""
    
    def test_export_attention_analysis_csv(self, sample_analysis_response):
        """Test CSV export function."""
        csv_content = export_attention_analysis_csv(sample_analysis_response)
        
        assert isinstance(csv_content, str)
        assert "model_name" in csv_content
        assert "DeepSeek-V3" in csv_content
    
    @patch('llm_modeling_metrics.web.export_utils._exporter.export_to_png')
    def test_export_attention_analysis_png(self, mock_export, sample_analysis_response):
        """Test PNG export function."""
        mock_export.return_value = b"fake_png_data"
        
        png_data = export_attention_analysis_png(sample_analysis_response)
        
        assert png_data == b"fake_png_data"
        mock_export.assert_called_once_with(sample_analysis_response, None, None, None)
    
    @patch('llm_modeling_metrics.web.export_utils._exporter.export_to_svg')
    def test_export_attention_analysis_svg(self, mock_export, sample_analysis_response):
        """Test SVG export function."""
        mock_export.return_value = "fake_svg_data"
        
        svg_data = export_attention_analysis_svg(sample_analysis_response)
        
        assert svg_data == "fake_svg_data"
        mock_export.assert_called_once_with(sample_analysis_response, None, None)
    
    def test_create_export_metadata_function(self, sample_analysis_response):
        """Test metadata creation function."""
        metadata = create_export_metadata(sample_analysis_response, "png")
        
        assert metadata["export_format"] == "png"
        assert "export_timestamp" in metadata


class TestExportEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_empty_metrics_list(self):
        """Test handling of empty metrics list."""
        empty_response = AttentionAnalysisResponse(
            metrics=[],
            roofline_points=[],
            hardware_limits={},
            execution_time=0.0,
            timestamp=datetime.now()
        )
        
        exporter = AttentionExporter()
        csv_content = exporter.export_to_csv(empty_response)
        
        # Should still have headers but no data rows
        assert "model_name" in csv_content
        lines = csv_content.strip().split('\n')
        # Should have metadata lines + header line, but no data lines
        data_lines = [line for line in lines if not line.startswith('#') and line.strip()]
        assert len(data_lines) == 1  # Just the header
    
    def test_missing_roofline_points(self, sample_attention_metrics):
        """Test handling when roofline points are missing."""
        response_no_roofline = AttentionAnalysisResponse(
            metrics=sample_attention_metrics,
            roofline_points=[],
            hardware_limits={},
            execution_time=1.0,
            timestamp=datetime.now()
        )
        
        exporter = AttentionExporter()
        csv_content = exporter.export_to_csv(response_no_roofline)
        
        # Should not have roofline section
        assert "# Roofline Plot Points" not in csv_content
        # But should still have main data
        assert "DeepSeek-V3" in csv_content
    
    def test_missing_hardware_limits(self, sample_attention_metrics):
        """Test handling when hardware limits are missing."""
        response_no_limits = AttentionAnalysisResponse(
            metrics=sample_attention_metrics,
            roofline_points=[],
            hardware_limits={},
            execution_time=1.0,
            timestamp=datetime.now()
        )
        
        exporter = AttentionExporter()
        csv_content = exporter.export_to_csv(response_no_limits)
        
        # Should not have hardware limits section
        assert "# Hardware Limits" not in csv_content
        # But should still have main data
        assert "DeepSeek-V3" in csv_content
    
    @patch('matplotlib.pyplot.subplots')
    def test_custom_plot_parameters(self, mock_subplots, sample_analysis_response):
        """Test plot creation with custom parameters."""
        mock_fig = Mock()
        mock_ax = Mock()
        mock_subplots.return_value = (mock_fig, mock_ax)
        
        exporter = AttentionExporter()
        
        # Test custom title and figsize
        fig = exporter.create_roofline_plot(
            sample_analysis_response,
            title="Custom Title",
            figsize=(10, 6)
        )
        
        mock_subplots.assert_called_once_with(1, 1, figsize=(10, 6))
        mock_ax.set_title.assert_called_with("Custom Title", fontsize=14, pad=20)
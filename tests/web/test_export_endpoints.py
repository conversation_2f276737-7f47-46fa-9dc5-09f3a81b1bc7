"""
Tests for attention analysis export API endpoints.
"""

import json
import pytest
from datetime import datetime
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient

from llm_modeling_metrics.web.app import app
from llm_modeling_metrics.web.models import (
    AttentionAnalysisResponse,
    AttentionMetricsModel,
    RooflinePointModel,
    ExportFormat
)


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_analysis_data():
    """Create sample analysis data for testing."""
    metrics = [
        AttentionMetricsModel(
            mechanism_type="MLA",
            model_name="DeepSeek-V3",
            sequence_length=8192,
            batch_size=1,
            total_flops=147000000000,
            attention_flops=117600000000,
            projection_flops=29400000000,
            memory_capacity_bytes=288000000,
            memory_movement_bytes=288000000,
            kv_cache_bytes=201600000,
            operational_intensity=0.51,
            achieved_performance_tflops=0.147,
            hardware_name="H100",
            peak_performance_tflops=989.4,
            memory_bandwidth_gbps=3352,
            flash_attention_enabled=True,
            precision="bf16"
        )
    ]
    
    roofline_points = [
        RooflinePointModel(
            x=0.288,
            y=147.0,
            model_name="DeepSeek-V3",
            attention_type="MLA",
            sequence_length=8192,
            operational_intensity=0.51,
            bottleneck_type="memory"
        )
    ]
    
    hardware_limits = {
        "H100": {
            "bf16": [0.0, 0.0, 1.0, 295.0, 2.0, 590.0]
        }
    }
    
    return AttentionAnalysisResponse(
        metrics=metrics,
        roofline_points=roofline_points,
        hardware_limits=hardware_limits,
        execution_time=2.5,
        timestamp=datetime(2024, 1, 15, 10, 30, 0)
    )


class TestExportEndpoint:
    """Test cases for the /api/attention/export endpoint."""
    
    @patch('llm_modeling_metrics.web.export_utils.export_attention_analysis_csv')
    def test_export_csv_success(self, mock_export_csv, client, sample_analysis_data):
        """Test successful CSV export."""
        mock_export_csv.return_value = "model_name,mechanism_type\nDeepSeek-V3,MLA"
        
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_format": "csv",
            "include_metadata": True
        }
        
        response = client.post("/api/attention/export", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["export_format"] == "csv"
        assert data["content_type"] == "text/csv"
        assert data["filename"].endswith(".csv")
        assert data["file_size_bytes"] > 0
        assert "metadata" in data
        
        mock_export_csv.assert_called_once()
    
    @patch('llm_modeling_metrics.web.export_utils.export_attention_analysis_png')
    def test_export_png_success(self, mock_export_png, client, sample_analysis_data):
        """Test successful PNG export."""
        mock_export_png.return_value = b"fake_png_data"
        
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_format": "png",
            "dpi": 300,
            "figure_width": 12.0,
            "figure_height": 8.0
        }
        
        response = client.post("/api/attention/export", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["export_format"] == "png"
        assert data["content_type"] == "image/png"
        assert data["filename"].endswith(".png")
        assert data["file_size_bytes"] == len(b"fake_png_data")
        
        mock_export_png.assert_called_once()
    
    @patch('llm_modeling_metrics.web.export_utils.export_attention_analysis_svg')
    def test_export_svg_success(self, mock_export_svg, client, sample_analysis_data):
        """Test successful SVG export."""
        mock_export_svg.return_value = "<svg>fake_svg_data</svg>"
        
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_format": "svg",
            "title": "Custom Title"
        }
        
        response = client.post("/api/attention/export", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["export_format"] == "svg"
        assert data["content_type"] == "image/svg+xml"
        assert data["filename"].endswith(".svg")
        assert data["file_size_bytes"] > 0
        
        mock_export_svg.assert_called_once()
    
    def test_export_invalid_format(self, client, sample_analysis_data):
        """Test export with invalid format."""
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_format": "invalid_format"
        }
        
        response = client.post("/api/attention/export", json=request_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_export_empty_metrics(self, client):
        """Test export with empty metrics."""
        empty_analysis = AttentionAnalysisResponse(
            metrics=[],
            roofline_points=[],
            hardware_limits={},
            execution_time=0.0,
            timestamp=datetime.now()
        )
        
        request_data = {
            "analysis_data": empty_analysis.dict(),
            "export_format": "csv"
        }
        
        response = client.post("/api/attention/export", json=request_data)
        
        assert response.status_code == 400
        assert "must contain at least one metric" in response.json()["detail"]
    
    def test_export_invalid_dpi(self, client, sample_analysis_data):
        """Test export with invalid DPI value."""
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_format": "png",
            "dpi": 50  # Below minimum of 72
        }
        
        response = client.post("/api/attention/export", json=request_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_export_invalid_figure_size(self, client, sample_analysis_data):
        """Test export with invalid figure size."""
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_format": "png",
            "figure_width": 25.0  # Above maximum of 20.0
        }
        
        response = client.post("/api/attention/export", json=request_data)
        
        assert response.status_code == 422  # Validation error


class TestBulkExportEndpoint:
    """Test cases for the /api/attention/export/bulk endpoint."""
    
    @patch('llm_modeling_metrics.web.app.export_attention_analysis')
    async def test_bulk_export_success(self, mock_export, client, sample_analysis_data):
        """Test successful bulk export."""
        # Mock individual export responses
        mock_csv_response = Mock()
        mock_csv_response.dict.return_value = {
            "export_format": "csv",
            "content_type": "text/csv",
            "filename": "test.csv",
            "file_size_bytes": 1000,
            "metadata": {},
            "timestamp": "2024-01-15T10:30:00"
        }
        
        mock_png_response = Mock()
        mock_png_response.dict.return_value = {
            "export_format": "png",
            "content_type": "image/png",
            "filename": "test.png",
            "file_size_bytes": 5000,
            "metadata": {},
            "timestamp": "2024-01-15T10:30:00"
        }
        
        mock_export.side_effect = [mock_csv_response, mock_png_response]
        
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_formats": ["csv", "png"]
        }
        
        response = client.post("/api/attention/export/bulk", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["total_exports"] == 2
        assert data["total_size_bytes"] == 6000
        assert "csv" in data["exports"]
        assert "png" in data["exports"]
    
    def test_bulk_export_duplicate_formats(self, client, sample_analysis_data):
        """Test bulk export with duplicate formats."""
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_formats": ["csv", "csv"]  # Duplicate
        }
        
        response = client.post("/api/attention/export/bulk", json=request_data)
        
        assert response.status_code == 422  # Validation error
        assert "Duplicate export formats" in response.json()["detail"][0]["msg"]
    
    def test_bulk_export_empty_formats(self, client, sample_analysis_data):
        """Test bulk export with empty formats list."""
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_formats": []
        }
        
        response = client.post("/api/attention/export/bulk", json=request_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_bulk_export_invalid_format(self, client, sample_analysis_data):
        """Test bulk export with invalid format."""
        request_data = {
            "analysis_data": sample_analysis_data.dict(),
            "export_formats": ["csv", "invalid"]
        }
        
        response = client.post("/api/attention/export/bulk", json=request_data)
        
        assert response.status_code == 422  # Validation error


class TestDownloadEndpoint:
    """Test cases for the /api/attention/export/download endpoint."""
    
    def test_download_not_implemented(self, client):
        """Test that download endpoint returns not implemented."""
        response = client.get("/api/attention/export/download/test_id?format=csv")
        
        assert response.status_code == 501
        assert "not yet implemented" in response.json()["detail"]
    
    def test_download_invalid_format(self, client):
        """Test download with invalid format."""
        response = client.get("/api/attention/export/download/test_id?format=invalid")
        
        assert response.status_code == 400
        assert "Invalid format" in response.json()["detail"]


class TestExportFormatsEndpoint:
    """Test cases for the /api/attention/export/formats endpoint."""
    
    def test_get_export_formats(self, client):
        """Test getting supported export formats."""
        response = client.get("/api/attention/export/formats")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "supported_formats" in data
        assert "format_details" in data
        assert "default_settings" in data
        
        # Check that all expected formats are present
        expected_formats = ["csv", "png", "svg"]
        assert all(fmt in data["supported_formats"] for fmt in expected_formats)
        
        # Check format details structure
        for fmt in expected_formats:
            assert fmt in data["format_details"]
            assert "description" in data["format_details"][fmt]
            assert "mime_type" in data["format_details"][fmt]
            assert "use_cases" in data["format_details"][fmt]
        
        # Check default settings
        assert "png_dpi" in data["default_settings"]
        assert "figure_width" in data["default_settings"]
        assert "figure_height" in data["default_settings"]
        assert "include_metadata" in data["default_settings"]


class TestExportIntegration:
    """Integration tests for export functionality."""
    
    @patch('llm_modeling_metrics.web.export_utils.export_attention_analysis_csv')
    @patch('llm_modeling_metrics.web.export_utils.export_attention_analysis_png')
    def test_export_workflow(self, mock_png, mock_csv, client, sample_analysis_data):
        """Test complete export workflow."""
        mock_csv.return_value = "csv_data"
        mock_png.return_value = b"png_data"
        
        # First, export CSV
        csv_request = {
            "analysis_data": sample_analysis_data.dict(),
            "export_format": "csv",
            "include_metadata": True
        }
        
        csv_response = client.post("/api/attention/export", json=csv_request)
        assert csv_response.status_code == 200
        
        # Then, export PNG
        png_request = {
            "analysis_data": sample_analysis_data.dict(),
            "export_format": "png",
            "dpi": 300
        }
        
        png_response = client.post("/api/attention/export", json=png_request)
        assert png_response.status_code == 200
        
        # Verify both exports have different content types
        csv_data = csv_response.json()
        png_data = png_response.json()
        
        assert csv_data["content_type"] == "text/csv"
        assert png_data["content_type"] == "image/png"
        assert csv_data["filename"] != png_data["filename"]
    
    def test_export_metadata_consistency(self, client, sample_analysis_data):
        """Test that export metadata is consistent across formats."""
        formats_to_test = ["csv", "png", "svg"]
        
        with patch('llm_modeling_metrics.web.export_utils.export_attention_analysis_csv') as mock_csv, \
             patch('llm_modeling_metrics.web.export_utils.export_attention_analysis_png') as mock_png, \
             patch('llm_modeling_metrics.web.export_utils.export_attention_analysis_svg') as mock_svg:
            
            mock_csv.return_value = "csv_data"
            mock_png.return_value = b"png_data"
            mock_svg.return_value = "svg_data"
            
            responses = []
            
            for fmt in formats_to_test:
                request_data = {
                    "analysis_data": sample_analysis_data.dict(),
                    "export_format": fmt
                }
                
                response = client.post("/api/attention/export", json=request_data)
                assert response.status_code == 200
                responses.append(response.json())
            
            # Check that metadata contains consistent information
            for response_data in responses:
                metadata = response_data["metadata"]
                assert metadata["models_analyzed"] == 1
                assert metadata["hardware_analyzed"] == 1
                assert metadata["total_data_points"] == 1
                assert metadata["roofline_points"] == 1
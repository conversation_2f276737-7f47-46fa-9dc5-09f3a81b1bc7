"""Tests for roofline visualization service."""

from unittest.mock import Mock, patch

import numpy as np
import pytest

from llm_modeling_metrics.core.operators import BaseOperator
from llm_modeling_metrics.hardware.models import HardwareSpec, HardwareType
from llm_modeling_metrics.hardware.roofline_service import (
    ComparisonPlotData,
    KneePoint,
    OperatorPoint,
    RooflineData,
    RooflinePlotData,
    RooflineService,
)


class MockOperator(BaseOperator):
    """Mock operator for testing."""

    def __init__(
        self,
        name: str,
        flops: int = 1000000,
        memory_bytes: int = 10000,
        precision: str = "bf16",
    ):
        super().__init__(name, precision)
        self._flops = flops
        self._memory_bytes = memory_bytes

    def compute_flops(self, **kwargs) -> int:
        return self._flops

    def compute_memory_capacity_bytes(self, **kwargs) -> int:
        return self._memory_bytes

    def compute_memory_movement_bytes(self, **kwargs) -> int:
        return self._memory_bytes

    def compute_params(self, **kwargs) -> int:
        return 1000

    def _estimate_compute_time(self, flops: int, hardware) -> float:
        """Mock compute time estimation."""
        peak_flops = hardware.get_peak_flops_per_second(self.activation_precision)
        if peak_flops > 0:
            return (flops / peak_flops) * 1000  # Convert to ms
        return 1.0

    def _estimate_memory_time(self, memory_bytes: int, hardware) -> float:
        """Mock memory time estimation."""
        bandwidth_bps = hardware.get_memory_bandwidth_bps()
        if bandwidth_bps > 0:
            return (memory_bytes / bandwidth_bps) * 1000  # Convert to ms
        return 1.0


@pytest.fixture
def sample_hardware_specs():
    """Create sample hardware specifications for testing."""
    gpu_spec = HardwareSpec(
        id="test_gpu",
        name="Test GPU",
        type=HardwareType.GPU,
        memory_size_gb=80,
        memory_bandwidth_gbps=2000,
        tensor_performance={
            "fp16_tensor": 500.0,
            "bf16_tensor": 500.0,
            "fp8_tensor": 1000.0,
        },
        vector_performance={"fp32": 100.0, "fp16": 200.0, "bf16": 200.0},
        tensor_cores=432,
    )

    npu_spec = HardwareSpec(
        id="test_npu",
        name="Test NPU",
        type=HardwareType.NPU,
        memory_size_gb=64,
        memory_bandwidth_gbps=1600,
        tensor_performance={
            "fp16_tensor": 400.0,
            "bf16_tensor": 400.0,
            "int8_tensor": 800.0,
        },
        vector_performance={"fp32": 80.0, "fp16": 160.0, "bf16": 160.0},
    )

    return [gpu_spec, npu_spec]


@pytest.fixture
def sample_operators():
    """Create sample operators for testing."""
    return [
        MockOperator("attention", flops=1000000, memory_bytes=50000, precision="bf16"),
        MockOperator("matmul", flops=2000000, memory_bytes=40000, precision="bf16"),
        MockOperator("moe_gate", flops=500000, memory_bytes=80000, precision="fp16"),
    ]


@pytest.fixture
def roofline_service():
    """Create roofline service instance."""
    return RooflineService()


class TestRooflineService:
    """Test cases for RooflineService."""

    def test_initialization(self, roofline_service):
        """Test service initialization."""
        assert roofline_service is not None
        assert roofline_service._roofline_cache == {}
        assert roofline_service._knee_point_cache == {}

    def test_calculate_knee_points(self, roofline_service, sample_hardware_specs):
        """Test knee point calculation."""
        gpu_spec = sample_hardware_specs[0]

        # Test valid precision
        knee_point = roofline_service.calculate_knee_points(gpu_spec, "bf16")

        assert knee_point is not None
        assert isinstance(knee_point, KneePoint)
        assert knee_point.hardware_id == "test_gpu"
        assert knee_point.precision == "bf16"
        assert knee_point.operational_intensity > 0
        assert knee_point.performance_tflops > 0

        # Test caching
        knee_point2 = roofline_service.calculate_knee_points(gpu_spec, "bf16")
        assert knee_point == knee_point2

    def test_calculate_knee_points_invalid_precision(
        self, roofline_service, sample_hardware_specs
    ):
        """Test knee point calculation with invalid precision."""
        gpu_spec = sample_hardware_specs[0]

        knee_point = roofline_service.calculate_knee_points(
            gpu_spec, "invalid_precision"
        )
        assert knee_point is None

    def test_generate_roofline_data(self, roofline_service, sample_hardware_specs):
        """Test roofline data generation."""
        precisions = ["fp16", "bf16", "fp8"]

        roofline_data = roofline_service.generate_roofline_data(
            sample_hardware_specs, precisions
        )

        assert isinstance(roofline_data, RooflineData)
        assert len(roofline_data.operational_intensity_range) == 2000
        assert len(roofline_data.hardware_specs) == 2

        # Check that curves were generated for supported precisions
        for hw_spec in sample_hardware_specs:
            assert hw_spec.id in roofline_data.performance_curves
            assert hw_spec.id in roofline_data.knee_points

            # Check that only supported precisions have curves
            for precision in precisions:
                if hw_spec.supports_precision(precision):
                    assert precision in roofline_data.performance_curves[hw_spec.id]
                    assert precision in roofline_data.knee_points[hw_spec.id]

    def test_generate_roofline_data_default_precisions(
        self, roofline_service, sample_hardware_specs
    ):
        """Test roofline data generation with default precisions."""
        roofline_data = roofline_service.generate_roofline_data(sample_hardware_specs)

        assert isinstance(roofline_data, RooflineData)
        assert len(roofline_data.hardware_specs) == 2

        # Should use default precisions
        expected_precisions = ["fp32", "fp16", "bf16", "fp8", "int8"]
        for hw_spec in sample_hardware_specs:
            for precision in expected_precisions:
                if hw_spec.supports_precision(precision):
                    assert precision in roofline_data.performance_curves[hw_spec.id]

    def test_plot_operators_on_roofline(
        self, roofline_service, sample_hardware_specs, sample_operators
    ):
        """Test plotting operators on roofline."""
        gpu_spec = sample_hardware_specs[0]

        plot_data = roofline_service.plot_operators_on_roofline(
            sample_operators, gpu_spec, batch_size=32, sequence_length=2048
        )

        assert isinstance(plot_data, RooflinePlotData)
        assert plot_data.hardware_id == "test_gpu"
        assert plot_data.hardware_name == "Test GPU"
        assert len(plot_data.operational_intensity_range) == 2000
        assert len(plot_data.roofline_curves) > 0
        assert len(plot_data.knee_points) > 0
        assert len(plot_data.operator_points) == len(sample_operators)

        # Check operator points
        for op_point in plot_data.operator_points:
            assert isinstance(op_point, OperatorPoint)
            assert op_point.hardware_id == "test_gpu"
            assert op_point.operational_intensity > 0
            assert op_point.achieved_performance_tflops >= 0
            assert 0 <= op_point.utilization_percent <= 100

    def test_compare_hardware_rooflines(
        self, roofline_service, sample_hardware_specs, sample_operators
    ):
        """Test hardware roofline comparison."""
        comparison_data = roofline_service.compare_hardware_rooflines(
            sample_hardware_specs, sample_operators, batch_size=32, sequence_length=2048
        )

        assert isinstance(comparison_data, ComparisonPlotData)
        assert len(comparison_data.hardware_platforms) == 2
        assert len(comparison_data.roofline_data) == 2
        assert len(comparison_data.performance_rankings) == len(sample_operators)
        assert len(comparison_data.recommendations) > 0

        # Check that each hardware has roofline data
        for hw_spec in sample_hardware_specs:
            assert hw_spec.id in comparison_data.roofline_data
            plot_data = comparison_data.roofline_data[hw_spec.id]
            assert isinstance(plot_data, RooflinePlotData)
            assert len(plot_data.operator_points) == len(sample_operators)

        # Check performance rankings
        for operator in sample_operators:
            assert operator.name in comparison_data.performance_rankings
            rankings = comparison_data.performance_rankings[operator.name]
            assert len(rankings) == len(sample_hardware_specs)
            assert all(
                hw_id in [hw.id for hw in sample_hardware_specs] for hw_id in rankings
            )

    def test_compare_hardware_rooflines_without_operators(
        self, roofline_service, sample_hardware_specs
    ):
        """Test hardware roofline comparison without operators."""
        comparison_data = roofline_service.compare_hardware_rooflines(
            sample_hardware_specs
        )

        assert isinstance(comparison_data, ComparisonPlotData)
        assert len(comparison_data.hardware_platforms) == 2
        assert len(comparison_data.roofline_data) == 2
        assert len(comparison_data.performance_rankings) == 0  # No operators provided
        assert len(comparison_data.recommendations) > 0

        # Check that roofline data exists but no operator points
        for hw_spec in sample_hardware_specs:
            plot_data = comparison_data.roofline_data[hw_spec.id]
            assert len(plot_data.operator_points) == 0
            assert len(plot_data.roofline_curves) > 0

    def test_generate_roofline_curve(self, roofline_service, sample_hardware_specs):
        """Test roofline curve generation."""
        gpu_spec = sample_hardware_specs[0]
        oi_range = np.logspace(-1, 3, 100)

        curve = roofline_service._generate_roofline_curve(gpu_spec, "bf16", oi_range)

        assert isinstance(curve, np.ndarray)
        assert len(curve) == len(oi_range)
        assert np.all(curve >= 0)  # All performance values should be non-negative
        assert np.all(
            np.diff(curve) >= -1e-10
        )  # Curve should be non-decreasing (allowing for numerical precision)

    def test_generate_roofline_curve_invalid_precision(
        self, roofline_service, sample_hardware_specs
    ):
        """Test roofline curve generation with invalid precision."""
        gpu_spec = sample_hardware_specs[0]
        oi_range = np.logspace(-1, 3, 100)

        with pytest.raises(ValueError):
            roofline_service._generate_roofline_curve(
                gpu_spec, "invalid_precision", oi_range
            )

    def test_calculate_operator_point(
        self, roofline_service, sample_hardware_specs, sample_operators
    ):
        """Test operator point calculation."""
        gpu_spec = sample_hardware_specs[0]
        operator = sample_operators[0]

        op_point = roofline_service._calculate_operator_point(
            operator, gpu_spec, batch_size=32, sequence_length=2048
        )

        assert isinstance(op_point, OperatorPoint)
        assert op_point.operator_name == operator.name
        assert op_point.hardware_id == gpu_spec.id
        assert op_point.operational_intensity > 0
        assert op_point.achieved_performance_tflops >= 0
        assert 0 <= op_point.utilization_percent <= 100
        assert op_point.precision == operator.activation_precision

    def test_calculate_operator_point_zero_memory(
        self, roofline_service, sample_hardware_specs
    ):
        """Test operator point calculation with zero memory movement."""
        gpu_spec = sample_hardware_specs[0]
        operator = MockOperator("zero_memory", flops=1000000, memory_bytes=0)

        op_point = roofline_service._calculate_operator_point(operator, gpu_spec)

        # Should return None for zero memory movement
        assert op_point is None

    def test_generate_performance_rankings(
        self, roofline_service, sample_hardware_specs, sample_operators
    ):
        """Test performance rankings generation."""
        # Create mock roofline data
        roofline_data = {}
        for hw_spec in sample_hardware_specs:
            operator_points = []
            for i, operator in enumerate(sample_operators):
                # Create mock operator points with different performance
                op_point = OperatorPoint(
                    operator_name=operator.name,
                    operational_intensity=100.0,
                    achieved_performance_tflops=float(i + 1)
                    * (1.0 if hw_spec.id == "test_gpu" else 0.8),
                    utilization_percent=80.0,
                    is_compute_bound=True,
                    precision="bf16",
                    hardware_id=hw_spec.id,
                )
                operator_points.append(op_point)

            roofline_data[hw_spec.id] = RooflinePlotData(
                hardware_id=hw_spec.id,
                hardware_name=hw_spec.name,
                operational_intensity_range=np.array([1, 2, 3]),
                operator_points=operator_points,
            )

        rankings = roofline_service._generate_performance_rankings(
            roofline_data, sample_operators
        )

        assert len(rankings) == len(sample_operators)
        for operator in sample_operators:
            assert operator.name in rankings
            assert len(rankings[operator.name]) == len(sample_hardware_specs)

    def test_generate_comparison_recommendations(
        self, roofline_service, sample_hardware_specs
    ):
        """Test comparison recommendations generation."""
        # Create mock roofline data
        roofline_data = {}
        for hw_spec in sample_hardware_specs:
            knee_points = {}
            if hw_spec.supports_precision("bf16"):
                knee_points["bf16"] = KneePoint(
                    operational_intensity=10.0 if hw_spec.id == "test_gpu" else 5.0,
                    performance_tflops=500.0,
                    precision="bf16",
                    hardware_id=hw_spec.id,
                )

            roofline_data[hw_spec.id] = RooflinePlotData(
                hardware_id=hw_spec.id,
                hardware_name=hw_spec.name,
                operational_intensity_range=np.array([1, 2, 3]),
                knee_points=knee_points,
            )

        recommendations = roofline_service._generate_comparison_recommendations(
            sample_hardware_specs, roofline_data
        )

        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        assert all(isinstance(rec, str) for rec in recommendations)

    def test_clear_cache(self, roofline_service, sample_hardware_specs):
        """Test cache clearing."""
        # Populate cache
        gpu_spec = sample_hardware_specs[0]
        roofline_service.calculate_knee_points(gpu_spec, "bf16")

        assert len(roofline_service._knee_point_cache) > 0

        # Clear cache
        roofline_service.clear_cache()

        assert len(roofline_service._roofline_cache) == 0
        assert len(roofline_service._knee_point_cache) == 0


class TestRooflineDataModels:
    """Test cases for roofline data models."""

    def test_knee_point_creation(self):
        """Test KneePoint creation."""
        knee_point = KneePoint(
            operational_intensity=10.5,
            performance_tflops=500.0,
            precision="bf16",
            hardware_id="test_gpu",
        )

        assert knee_point.operational_intensity == 10.5
        assert knee_point.performance_tflops == 500.0
        assert knee_point.precision == "bf16"
        assert knee_point.hardware_id == "test_gpu"

    def test_operator_point_creation(self):
        """Test OperatorPoint creation."""
        op_point = OperatorPoint(
            operator_name="attention",
            operational_intensity=5.0,
            achieved_performance_tflops=100.0,
            utilization_percent=85.0,
            is_compute_bound=True,
            precision="bf16",
            hardware_id="test_gpu",
        )

        assert op_point.operator_name == "attention"
        assert op_point.operational_intensity == 5.0
        assert op_point.achieved_performance_tflops == 100.0
        assert op_point.utilization_percent == 85.0
        assert op_point.is_compute_bound is True
        assert op_point.precision == "bf16"
        assert op_point.hardware_id == "test_gpu"

    def test_roofline_data_creation(self):
        """Test RooflineData creation."""
        oi_range = np.logspace(-1, 3, 100)
        roofline_data = RooflineData(operational_intensity_range=oi_range)

        assert len(roofline_data.operational_intensity_range) == 100
        assert len(roofline_data.performance_curves) == 0
        assert len(roofline_data.knee_points) == 0
        assert len(roofline_data.operator_points) == 0
        assert len(roofline_data.hardware_specs) == 0

    def test_roofline_plot_data_creation(self):
        """Test RooflinePlotData creation."""
        oi_range = np.logspace(-1, 3, 100)
        plot_data = RooflinePlotData(
            hardware_id="test_gpu",
            hardware_name="Test GPU",
            operational_intensity_range=oi_range,
        )

        assert plot_data.hardware_id == "test_gpu"
        assert plot_data.hardware_name == "Test GPU"
        assert len(plot_data.operational_intensity_range) == 100
        assert len(plot_data.roofline_curves) == 0
        assert len(plot_data.knee_points) == 0
        assert len(plot_data.operator_points) == 0

    def test_comparison_plot_data_creation(self):
        """Test ComparisonPlotData creation."""
        oi_range = np.logspace(-1, 3, 100)
        comparison_data = ComparisonPlotData(
            hardware_platforms=["gpu1", "gpu2"], operational_intensity_range=oi_range
        )

        assert comparison_data.hardware_platforms == ["gpu1", "gpu2"]
        assert len(comparison_data.operational_intensity_range) == 100
        assert len(comparison_data.roofline_data) == 0
        assert len(comparison_data.performance_rankings) == 0
        assert len(comparison_data.recommendations) == 0


if __name__ == "__main__":
    pytest.main([__file__])

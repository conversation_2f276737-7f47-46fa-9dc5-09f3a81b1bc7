"""Integration tests for roofline service with real hardware data."""

from pathlib import Path

import numpy as np
import pytest

from llm_modeling_metrics.core.operators import MatMulOperator
from llm_modeling_metrics.hardware.roofline_service import RooflineService
from llm_modeling_metrics.hardware.service import HardwareService


class TestRooflineIntegration:
    """Integration tests for roofline service."""

    @pytest.fixture
    def hardware_service(self):
        """Create hardware service with real data."""
        try:
            return HardwareService()
        except FileNotFoundError:
            pytest.skip("GPU specs file not found - skipping integration tests")

    @pytest.fixture
    def roofline_service(self):
        """Create roofline service."""
        return RooflineService()

    @pytest.fixture
    def sample_operators(self):
        """Create sample operators for testing."""
        operators = [
            MatMulOperator(
                M=32, N=4096, K=4096, precision="bf16"
            ),  # Attention projection
            MatMulOperator(
                M=32, N=11008, K=4096, precision="bf16"
            ),  # MLP up projection
            MatMulOperator(
                M=32, N=4096, K=11008, precision="bf16"
            ),  # MLP down projection
        ]
        # Give them unique names
        operators[0].name = "attention_proj"
        operators[1].name = "mlp_up"
        operators[2].name = "mlp_down"
        return operators

    def test_roofline_with_real_hardware(
        self, hardware_service, roofline_service, sample_operators
    ):
        """Test roofline generation with real hardware specifications."""
        # Get available hardware
        available_hardware = hardware_service.get_available_hardware()

        if not available_hardware.get("gpu") and not available_hardware.get("npu"):
            pytest.skip("No hardware specifications available")

        # Get a few hardware specs for testing
        hardware_specs = []
        if available_hardware.get("gpu"):
            hardware_specs.extend(available_hardware["gpu"][:2])  # First 2 GPUs
        if available_hardware.get("npu"):
            hardware_specs.extend(available_hardware["npu"][:1])  # First NPU

        if not hardware_specs:
            pytest.skip("No valid hardware specifications found")

        # Generate roofline data
        roofline_data = roofline_service.generate_roofline_data(
            hardware_specs, precisions=["fp16", "bf16", "fp8"]
        )

        # Verify roofline data structure
        assert len(roofline_data.operational_intensity_range) == 2000
        assert len(roofline_data.hardware_specs) == len(hardware_specs)

        # Verify curves were generated for each hardware
        for hw_spec in hardware_specs:
            assert hw_spec.id in roofline_data.performance_curves
            assert hw_spec.id in roofline_data.knee_points

            # Check that at least one precision has data
            assert len(roofline_data.performance_curves[hw_spec.id]) > 0
            assert len(roofline_data.knee_points[hw_spec.id]) > 0

    def test_operator_plotting_with_real_hardware(
        self, hardware_service, roofline_service, sample_operators
    ):
        """Test operator plotting with real hardware."""
        available_hardware = hardware_service.get_available_hardware()

        if not available_hardware.get("gpu"):
            pytest.skip("No GPU specifications available")

        # Use first available GPU
        gpu_spec = available_hardware["gpu"][0]

        # Plot operators on roofline
        plot_data = roofline_service.plot_operators_on_roofline(
            sample_operators, gpu_spec, batch_size=32, sequence_length=2048
        )

        # Verify plot data
        assert plot_data.hardware_id == gpu_spec.id
        assert plot_data.hardware_name == gpu_spec.name
        assert len(plot_data.roofline_curves) > 0
        assert len(plot_data.knee_points) > 0
        assert len(plot_data.operator_points) == len(sample_operators)

        # Verify operator points have reasonable values
        for op_point in plot_data.operator_points:
            assert op_point.operational_intensity > 0
            assert op_point.achieved_performance_tflops >= 0
            assert 0 <= op_point.utilization_percent <= 100
            assert op_point.hardware_id == gpu_spec.id

    def test_hardware_comparison_with_real_data(
        self, hardware_service, roofline_service, sample_operators
    ):
        """Test hardware comparison with real data."""
        available_hardware = hardware_service.get_available_hardware()

        # Get at least 2 hardware specs for comparison
        hardware_specs = []
        if available_hardware.get("gpu"):
            hardware_specs.extend(available_hardware["gpu"][:2])
        if len(hardware_specs) < 2 and available_hardware.get("npu"):
            hardware_specs.extend(available_hardware["npu"][:2])

        if len(hardware_specs) < 2:
            pytest.skip("Need at least 2 hardware specs for comparison")

        # Compare hardware rooflines
        comparison_data = roofline_service.compare_hardware_rooflines(
            hardware_specs, sample_operators, batch_size=32, sequence_length=2048
        )

        # Verify comparison data
        assert len(comparison_data.hardware_platforms) == len(hardware_specs)
        assert len(comparison_data.roofline_data) == len(hardware_specs)
        assert len(comparison_data.performance_rankings) == len(sample_operators)
        assert len(comparison_data.recommendations) > 0

        # Verify each hardware has complete roofline data
        for hw_spec in hardware_specs:
            assert hw_spec.id in comparison_data.roofline_data
            plot_data = comparison_data.roofline_data[hw_spec.id]
            assert len(plot_data.operator_points) == len(sample_operators)
            assert len(plot_data.roofline_curves) > 0

        # Verify performance rankings
        for operator in sample_operators:
            assert operator.name in comparison_data.performance_rankings
            rankings = comparison_data.performance_rankings[operator.name]
            assert len(rankings) == len(hardware_specs)

    def test_knee_point_calculation_accuracy(self, hardware_service, roofline_service):
        """Test knee point calculation accuracy with real hardware."""
        available_hardware = hardware_service.get_available_hardware()

        if not available_hardware.get("gpu"):
            pytest.skip("No GPU specifications available")

        gpu_spec = available_hardware["gpu"][0]

        # Calculate knee point for a supported precision
        supported_precisions = [
            p for p in ["fp16", "bf16", "fp8"] if gpu_spec.supports_precision(p)
        ]
        if not supported_precisions:
            pytest.skip("No supported precisions found")

        precision = supported_precisions[0]
        knee_point = roofline_service.calculate_knee_points(gpu_spec, precision)

        assert knee_point is not None

        # Verify knee point makes sense
        peak_flops = gpu_spec.get_peak_flops(precision)
        mem_bandwidth = gpu_spec.memory_bandwidth_gbps

        # Knee point operational intensity should be peak_flops / memory_bandwidth
        expected_oi = (peak_flops * 1e12) / (mem_bandwidth * 1e9)  # FLOPS / (Bytes/s)

        # Allow for some numerical precision differences
        assert abs(knee_point.operational_intensity - expected_oi) < expected_oi * 0.01
        assert knee_point.performance_tflops == peak_flops

    def test_roofline_curve_properties(self, hardware_service, roofline_service):
        """Test roofline curve mathematical properties."""
        available_hardware = hardware_service.get_available_hardware()

        if not available_hardware.get("gpu"):
            pytest.skip("No GPU specifications available")

        gpu_spec = available_hardware["gpu"][0]

        # Get a supported precision
        supported_precisions = [
            p for p in ["fp16", "bf16", "fp8"] if gpu_spec.supports_precision(p)
        ]
        if not supported_precisions:
            pytest.skip("No supported precisions found")

        precision = supported_precisions[0]
        oi_range = np.logspace(-1, 3, 1000)

        # Generate roofline curve
        curve = roofline_service._generate_roofline_curve(gpu_spec, precision, oi_range)

        # Verify curve properties
        assert len(curve) == len(oi_range)
        assert np.all(curve >= 0)  # All values should be non-negative

        # Curve should be non-decreasing (roofline property)
        assert np.all(np.diff(curve) >= -1e-10)  # Allow for numerical precision

        # At high operational intensity, curve should approach peak performance
        peak_flops = gpu_spec.get_peak_flops(precision)
        high_oi_performance = curve[-100:]  # Last 100 points
        assert np.all(np.abs(high_oi_performance - peak_flops) < peak_flops * 0.01)

        # At low operational intensity, curve should be memory-bandwidth limited
        mem_bandwidth_gbps = gpu_spec.memory_bandwidth_gbps
        low_oi_idx = 10  # Use 10th point to avoid numerical issues at very low OI
        expected_low_oi_perf = (
            mem_bandwidth_gbps * 1e9 * oi_range[low_oi_idx]
        ) / 1e12  # Convert to TFLOPS
        assert (
            abs(curve[low_oi_idx] - expected_low_oi_perf) < expected_low_oi_perf * 0.1
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

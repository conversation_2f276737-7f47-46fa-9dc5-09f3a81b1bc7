"""Tests for hardware configuration management system."""

import json
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
import yaml

from llm_modeling_metrics.hardware.config_manager import HardwareConfigManager
from llm_modeling_metrics.hardware.editor import HardwareSpecEditor
from llm_modeling_metrics.hardware.models import HardwareType, ValidationResult
from llm_modeling_metrics.hardware.monitoring import (
    ErrorSeverity,
    HardwareErrorHandler,
    HardwareSystemMonitor,
)


class TestHardwareConfigManager:
    """Test hardware configuration manager."""

    @pytest.fixture
    def temp_config_dir(self):
        """Create temporary directory for custom configs."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def sample_gpu_config(self):
        """Sample GPU configuration data."""
        return {
            "gpus": {
                "test_gpu": {
                    "name": "Test GPU",
                    "memory_size_gb": 16,
                    "memory_bandwidth_gbps": 1000,
                    "tensor_performance": {"fp16_tensor": 200},
                    "vector_performance": {"fp32": 100},
                }
            }
        }

    @pytest.fixture
    def primary_config_file(self, sample_gpu_config):
        """Create temporary primary config file."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
            yaml.dump(sample_gpu_config, f)
            yield f.name
        Path(f.name).unlink()

    def test_config_manager_initialization(self, primary_config_file, temp_config_dir):
        """Test configuration manager initialization."""
        config_manager = HardwareConfigManager(
            primary_config_file=primary_config_file,
            custom_config_dir=temp_config_dir,
            enable_auto_reload=False,
        )

        assert config_manager.primary_config_file == primary_config_file
        assert str(config_manager.custom_config_dir) == temp_config_dir
        assert not config_manager.enable_auto_reload
        assert config_manager.adapter is not None

    def test_get_available_hardware(self, primary_config_file, temp_config_dir):
        """Test getting available hardware."""
        config_manager = HardwareConfigManager(
            primary_config_file=primary_config_file,
            custom_config_dir=temp_config_dir,
            enable_auto_reload=False,
        )

        hardware = config_manager.get_available_hardware()

        assert "gpu" in hardware
        assert "npu" in hardware
        assert len(hardware["gpu"]) == 1
        assert hardware["gpu"][0].id == "test_gpu"
        assert hardware["gpu"][0].name == "Test GPU"

    def test_save_custom_profile(self, primary_config_file, temp_config_dir):
        """Test saving custom hardware profile."""
        config_manager = HardwareConfigManager(
            primary_config_file=primary_config_file,
            custom_config_dir=temp_config_dir,
            enable_auto_reload=False,
        )

        custom_profile = {
            "gpus": {
                "custom_gpu": {
                    "name": "Custom GPU",
                    "memory_size_gb": 32,
                    "memory_bandwidth_gbps": 2000,
                    "tensor_performance": {"fp16_tensor": 400},
                    "vector_performance": {"fp32": 200},
                }
            }
        }

        result = config_manager.save_custom_profile("test_profile", custom_profile)

        assert result.is_valid

        # Verify file was created
        profile_file = Path(temp_config_dir) / "test_profile.yaml"
        assert profile_file.exists()

        # Verify content
        with open(profile_file) as f:
            saved_data = yaml.safe_load(f)
        assert saved_data == custom_profile

    def test_validate_all_configurations(self, primary_config_file, temp_config_dir):
        """Test validating all configurations."""
        config_manager = HardwareConfigManager(
            primary_config_file=primary_config_file,
            custom_config_dir=temp_config_dir,
            enable_auto_reload=False,
        )

        result = config_manager.validate_all_configurations()

        assert result.is_valid
        assert len(result.errors) == 0

    def test_list_custom_profiles(self, primary_config_file, temp_config_dir):
        """Test listing custom profiles."""
        config_manager = HardwareConfigManager(
            primary_config_file=primary_config_file,
            custom_config_dir=temp_config_dir,
            enable_auto_reload=False,
        )

        # Initially no custom profiles
        profiles = config_manager.list_custom_profiles()
        assert len(profiles) == 0

        # Save a custom profile
        custom_profile = {
            "gpus": {
                "test": {
                    "name": "Test",
                    "memory_size_gb": 8,
                    "memory_bandwidth_gbps": 500,
                }
            }
        }
        config_manager.save_custom_profile("test_profile", custom_profile)

        # Now should have one profile
        profiles = config_manager.list_custom_profiles()
        assert len(profiles) == 1
        assert profiles[0]["name"] == "test_profile"
        assert profiles[0]["format"] == "yaml"


class TestHardwareSpecEditor:
    """Test hardware specification editor."""

    @pytest.fixture
    def config_manager(self):
        """Mock configuration manager."""
        mock_manager = MagicMock()
        mock_manager.get_hardware_specs.return_value = None
        return mock_manager

    @pytest.fixture
    def editor(self, config_manager):
        """Hardware spec editor instance."""
        return HardwareSpecEditor(config_manager)

    def test_create_hardware_template_gpu(self, editor):
        """Test creating GPU hardware template."""
        template = editor.create_hardware_template(HardwareType.GPU)

        assert template["name"] == "New Hardware Device"
        assert "tensor_performance" in template
        assert "vector_performance" in template
        assert "streaming_multiprocessors" in template
        assert template["memory_size_gb"] == 0

    def test_create_hardware_template_npu(self, editor):
        """Test creating NPU hardware template."""
        template = editor.create_hardware_template(HardwareType.NPU)

        assert template["name"] == "New Hardware Device"
        assert "tensor_performance" in template
        assert "vector_performance" in template
        assert "streaming_multiprocessors" not in template
        assert template["memory_size_gb"] == 0

    def test_validate_hardware_spec(self, editor):
        """Test validating hardware specification."""
        valid_spec = {
            "name": "Test GPU",
            "memory_size_gb": 16,
            "memory_bandwidth_gbps": 1000,
            "tensor_performance": {"fp16_tensor": 200},
            "vector_performance": {"fp32": 100},
        }

        result = editor.validate_hardware_spec("test_gpu", valid_spec)
        assert result.is_valid

    def test_validate_invalid_hardware_spec(self, editor):
        """Test validating invalid hardware specification."""
        invalid_spec = {
            "name": "Test GPU",
            # Missing required fields
        }

        result = editor.validate_hardware_spec("test_gpu", invalid_spec)
        assert not result.is_valid
        assert len(result.errors) > 0

    def test_export_hardware_spec(self, editor):
        """Test exporting hardware specification."""
        # Mock a hardware spec
        from llm_modeling_metrics.hardware.models import HardwareSpec

        mock_spec = HardwareSpec(
            id="test_gpu",
            name="Test GPU",
            type=HardwareType.GPU,
            memory_size_gb=16,
            memory_bandwidth_gbps=1000,
            tensor_performance={"fp16_tensor": 200},
            vector_performance={"fp32": 100},
        )

        editor.config_manager.get_hardware_specs.return_value = mock_spec

        yaml_export = editor.export_hardware_spec("test_gpu", "yaml")
        assert yaml_export is not None
        assert "test_gpu:" in yaml_export
        assert "Test GPU" in yaml_export

        json_export = editor.export_hardware_spec("test_gpu", "json")
        assert json_export is not None
        assert "test_gpu" in json_export
        assert "Test GPU" in json_export


class TestHardwareSystemMonitor:
    """Test hardware system monitor."""

    @pytest.fixture
    def monitor(self):
        """Hardware system monitor instance."""
        return HardwareSystemMonitor(check_interval=1, max_error_history=10)

    def test_monitor_initialization(self, monitor):
        """Test monitor initialization."""
        assert monitor.check_interval == 1
        assert monitor.max_error_history == 10
        assert not monitor._monitoring_active
        assert len(monitor._health_check_functions) > 0  # Default health checks

    def test_record_error(self, monitor):
        """Test recording error events."""
        monitor.record_error(
            "test_component",
            ErrorSeverity.MEDIUM,
            "Test error message",
            context={"test": "data"},
        )

        assert len(monitor._error_events) == 1
        error = monitor._error_events[0]
        assert error.component == "test_component"
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.message == "Test error message"
        assert error.context["test"] == "data"
        assert not error.resolved

    def test_resolve_error(self, monitor):
        """Test resolving error events."""
        monitor.record_error("test", ErrorSeverity.LOW, "Test error")
        error_id = monitor._error_events[0].error_id

        monitor.resolve_error(error_id, "Fixed the issue")

        error = monitor._error_events[0]
        assert error.resolved
        assert error.resolution_time is not None
        assert error.context["resolution_notes"] == "Fixed the issue"

    def test_get_system_health(self, monitor):
        """Test getting system health status."""
        # Record some test errors
        monitor.record_error("test1", ErrorSeverity.LOW, "Low severity error")
        monitor.record_error("test2", ErrorSeverity.HIGH, "High severity error")

        health = monitor.get_system_health()

        assert "overall_status" in health
        assert "timestamp" in health
        assert "error_summary" in health
        assert "total_errors" in health
        assert health["total_errors"] == 2
        assert health["error_summary"]["low"] == 1
        assert health["error_summary"]["high"] == 1

    def test_register_custom_health_check(self, monitor):
        """Test registering custom health check."""

        def custom_check():
            from llm_modeling_metrics.hardware.monitoring import (
                HealthCheck,
                HealthStatus,
            )

            return HealthCheck(
                name="custom_check",
                status=HealthStatus.HEALTHY,
                message="Custom check passed",
            )

        monitor.register_health_check("custom_check", custom_check)

        assert "custom_check" in monitor._health_check_functions

    def test_fallback_configuration(self, monitor):
        """Test fallback configuration management."""
        fallback_data = {"test": "fallback_value"}

        monitor.set_fallback_config("test_component", fallback_data)

        retrieved = monitor.get_fallback_config("test_component")
        assert retrieved == fallback_data

        # Test non-existent component
        assert monitor.get_fallback_config("nonexistent") is None


class TestHardwareErrorHandler:
    """Test hardware error handler."""

    @pytest.fixture
    def monitor(self):
        """Mock system monitor."""
        return MagicMock()

    @pytest.fixture
    def error_handler(self, monitor):
        """Hardware error handler instance."""
        return HardwareErrorHandler(monitor)

    def test_handle_hardware_validation_error(self, error_handler):
        """Test handling hardware validation errors."""
        validation_result = ValidationResult(is_valid=False)
        validation_result.add_error("Test validation error")

        enhanced_result = error_handler.handle_hardware_validation_error(
            "test_hardware", validation_result
        )

        assert not enhanced_result.is_valid
        assert len(enhanced_result.errors) == 1
        assert len(enhanced_result.recommendations) > 0

        # Verify error was recorded with monitor
        error_handler.monitor.record_error.assert_called_once()

    def test_handle_config_loading_error(self, error_handler):
        """Test handling configuration loading errors."""
        test_error = Exception("Config file not found")

        fallback_config = error_handler.handle_config_loading_error(
            "/path/to/config.yaml", test_error
        )

        assert "gpus" in fallback_config
        assert "npus" in fallback_config
        assert len(fallback_config["gpus"]) > 0

        # Verify error was recorded
        error_handler.monitor.record_error.assert_called_once()

    def test_handle_timing_calculation_error(self, error_handler):
        """Test handling timing calculation errors."""
        test_error = Exception("Timing calculation failed")

        fallback_timing = error_handler.handle_timing_calculation_error(
            "test_operator", "test_hardware", test_error
        )

        assert "compute_time_ms" in fallback_timing
        assert "memory_time_ms" in fallback_timing
        assert "execution_time_ms" in fallback_timing
        assert fallback_timing["fallback_used"] is True
        assert fallback_timing["error_message"] == str(test_error)

        # Verify error was recorded
        error_handler.monitor.record_error.assert_called_once()

    def test_log_performance_warning(self, error_handler):
        """Test logging performance warnings."""
        # Should not log warning for fast operation
        error_handler.log_performance_warning("fast_op", 500.0, 1000.0)
        error_handler.monitor.record_error.assert_not_called()

        # Should log warning for slow operation
        error_handler.log_performance_warning("slow_op", 2000.0, 1000.0)
        error_handler.monitor.record_error.assert_called_once()

    def test_create_debug_context(self, error_handler):
        """Test creating debug context."""
        context = error_handler.create_debug_context(
            "test_operation", param1="value1", param2=42
        )

        assert context["operation"] == "test_operation"
        assert context["param1"] == "value1"
        assert context["param2"] == 42
        assert "timestamp" in context
        assert "thread_id" in context


if __name__ == "__main__":
    pytest.main([__file__])

"""
Unit tests for ParallelStrategyCalculator functionality.
"""

from unittest.mock import Mock, patch

import pytest

from llm_modeling_metrics.core.base_model import BaseModel, ParallelConfig
from llm_modeling_metrics.core.parallel_strategies import ParallelStrategyCalculator
from tests.conftest import (
    MockConfig,
    assert_parallel_config_valid,
    create_mock_model_config,
)


class MockModel(BaseModel):
    """Mock model for testing parallel strategies."""

    def __init__(self, model_name: str, config=None):
        super().__init__(model_name, config)
        if config:
            self._parse_config()

    def _parse_config(self):
        self._parsed_config = {
            "hidden_size": getattr(self.config, "hidden_size", 4096),
            "num_attention_heads": getattr(self.config, "num_attention_heads", 32),
            "num_key_value_heads": getattr(self.config, "num_key_value_heads", 32),
            "intermediate_size": getattr(self.config, "intermediate_size", 11008),
            "num_hidden_layers": getattr(self.config, "num_hidden_layers", 32),
            "vocab_size": getattr(self.config, "vocab_size", 32000),
        }

    def compute_attention_params(self) -> int:
        hidden_size = self._parsed_config["hidden_size"]
        return hidden_size * hidden_size * 4

    def compute_mlp_params(self) -> int:
        hidden_size = self._parsed_config["hidden_size"]
        intermediate_size = self._parsed_config["intermediate_size"]
        return hidden_size * intermediate_size * 3

    def compute_embedding_params(self) -> int:
        hidden_size = self._parsed_config["hidden_size"]
        vocab_size = self._parsed_config["vocab_size"]
        return vocab_size * hidden_size * 2

    def compute_flops(self, sequence_length: int = 2048, batch_size: int = 1) -> dict:
        hidden_size = self._parsed_config["hidden_size"]
        base_flops = batch_size * sequence_length * hidden_size * 1000
        return {
            "attention": base_flops * 2,
            "mlp": base_flops * 3,
            "total": base_flops * 5,
        }

    def compute_memory_requirements(
        self, sequence_length: int = 2048, batch_size: int = 1
    ) -> dict:
        params = self.get_total_params()
        activations = (
            batch_size * sequence_length * self._parsed_config["hidden_size"] * 4
        )
        return {
            "parameters": params * 4,
            "activations": activations * 4,
            "total": (params + activations) * 4,
        }

    def get_matrix_shapes(self, parallel_config=None) -> dict:
        hidden_size = self._parsed_config["hidden_size"]
        intermediate_size = self._parsed_config["intermediate_size"]

        return {
            "attention": {
                "q_proj": (hidden_size, hidden_size),
                "k_proj": (hidden_size, hidden_size),
                "v_proj": (hidden_size, hidden_size),
                "o_proj": (hidden_size, hidden_size),
            },
            "mlp": {
                "gate_proj": (hidden_size, intermediate_size),
                "up_proj": (hidden_size, intermediate_size),
                "down_proj": (intermediate_size, hidden_size),
            },
        }


class TestTensorParallelShapes:
    """Test tensor parallel shape computation."""

    def test_basic_tensor_parallel_shapes(self):
        """Test basic tensor parallel shape computation."""
        base_shapes = {
            "q_proj": (4096, 4096),
            "k_proj": (4096, 1024),
            "v_proj": (4096, 1024),
            "o_proj": (4096, 4096),
            "gate_proj": (4096, 11008),
            "up_proj": (4096, 11008),
            "down_proj": (11008, 4096),
            "embeddings": (32000, 4096),
        }

        tp_size = 4
        parallel_shapes = ParallelStrategyCalculator.compute_tensor_parallel_shapes(
            base_shapes, tp_size
        )

        # Column-parallel operations (split output dimension)
        assert parallel_shapes["q_proj"] == (4096, 1024)
        assert parallel_shapes["k_proj"] == (4096, 256)
        assert parallel_shapes["v_proj"] == (4096, 256)
        assert parallel_shapes["gate_proj"] == (4096, 2752)
        assert parallel_shapes["up_proj"] == (4096, 2752)

        # Row-parallel operations (split input dimension)
        assert parallel_shapes["o_proj"] == (1024, 4096)
        assert parallel_shapes["down_proj"] == (2752, 4096)

        # Replicated operations (no change)
        assert parallel_shapes["embeddings"] == (32000, 4096)

    def test_tensor_parallel_shapes_edge_cases(self):
        """Test tensor parallel shapes with edge cases."""
        # Test with tp_size = 1 (no parallelism)
        base_shapes = {"test_matrix": (1024, 2048)}
        parallel_shapes = ParallelStrategyCalculator.compute_tensor_parallel_shapes(
            base_shapes, 1
        )
        assert parallel_shapes["test_matrix"] == (1024, 2048)

        # Test with empty shapes
        empty_shapes = {}
        parallel_shapes = ParallelStrategyCalculator.compute_tensor_parallel_shapes(
            empty_shapes, 4
        )
        assert parallel_shapes == {}

        # Test with non-divisible dimensions
        non_divisible_shapes = {"matrix": (1000, 1001)}
        parallel_shapes = ParallelStrategyCalculator.compute_tensor_parallel_shapes(
            non_divisible_shapes, 3
        )
        # Should handle non-divisible dimensions gracefully
        assert parallel_shapes["matrix"][0] == 1000  # Input dim unchanged
        assert (
            parallel_shapes["matrix"][1] == 1001 // 3
        )  # Output dim divided (with truncation)

    def test_different_tensor_parallel_sizes(self):
        """Test tensor parallel shapes with different TP sizes."""
        base_shapes = {"matrix": (4096, 4096)}

        for tp_size in [1, 2, 4, 8, 16]:
            parallel_shapes = ParallelStrategyCalculator.compute_tensor_parallel_shapes(
                base_shapes, tp_size
            )

            if tp_size == 1:
                assert parallel_shapes["matrix"] == (4096, 4096)
            else:
                expected_output_dim = 4096 // tp_size
                assert parallel_shapes["matrix"] == (4096, expected_output_dim)


class TestParallelConfigValidation:
    """Test parallel configuration validation."""

    def test_valid_parallel_configs(self):
        """Test validation of valid parallel configurations."""
        model_config = create_mock_model_config("llama")

        valid_configs = [
            ParallelConfig(tensor_parallel_size=1),
            ParallelConfig(tensor_parallel_size=2),
            ParallelConfig(tensor_parallel_size=4),
            ParallelConfig(tensor_parallel_size=8),
            ParallelConfig(pipeline_parallel_size=2),
            ParallelConfig(data_parallel_size=4),
            ParallelConfig(tensor_parallel_size=2, pipeline_parallel_size=2),
        ]

        for config in valid_configs:
            assert ParallelStrategyCalculator.validate_parallel_config(
                config, model_config
            )

    def test_invalid_parallel_configs(self):
        """Test validation of invalid parallel configurations."""
        model_config = create_mock_model_config("llama")

        invalid_configs = [
            ParallelConfig(tensor_parallel_size=5),  # 32 heads not divisible by 5
            ParallelConfig(tensor_parallel_size=7),  # 32 heads not divisible by 7
            ParallelConfig(tensor_parallel_size=33),  # Larger than number of heads
        ]

        for config in invalid_configs:
            assert not ParallelStrategyCalculator.validate_parallel_config(
                config, model_config
            )

    def test_validation_errors(self):
        """Test getting detailed validation errors."""
        model_config = create_mock_model_config("llama")
        invalid_config = ParallelConfig(tensor_parallel_size=5)

        errors = ParallelStrategyCalculator.get_validation_errors(
            invalid_config, model_config
        )

        assert len(errors) > 0
        assert any("num_attention_heads" in error for error in errors)
        assert any("32" in error and "5" in error for error in errors)

    def test_validation_with_different_model_configs(self):
        """Test validation with different model configurations."""
        # Test with GQA model (different num_key_value_heads)
        gqa_config = create_mock_model_config("llama", num_key_value_heads=8)

        # Should validate based on num_attention_heads, not num_key_value_heads
        valid_config = ParallelConfig(tensor_parallel_size=4)  # 32 heads / 4 = 8
        assert ParallelStrategyCalculator.validate_parallel_config(
            valid_config, gqa_config
        )

        # Test with MoE model
        moe_config = create_mock_model_config("deepseek_v3")
        moe_parallel_config = ParallelConfig(
            tensor_parallel_size=2, expert_parallel_size=4
        )
        # Should be valid for MoE models
        assert ParallelStrategyCalculator.validate_parallel_config(
            moe_parallel_config, moe_config
        )

    def test_validation_edge_cases(self):
        """Test validation edge cases."""
        # Test with minimal model config
        minimal_config = {
            "hidden_size": 1,
            "num_attention_heads": 1,
            "intermediate_size": 1,
        }

        config = ParallelConfig(tensor_parallel_size=1)
        assert ParallelStrategyCalculator.validate_parallel_config(
            config, minimal_config
        )

        config = ParallelConfig(tensor_parallel_size=2)
        assert not ParallelStrategyCalculator.validate_parallel_config(
            config, minimal_config
        )


class TestCommunicationVolume:
    """Test communication volume computation."""

    def test_tensor_parallel_communication(self):
        """Test communication volume for tensor parallelism."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        tp_config = ParallelConfig(tensor_parallel_size=4)

        comm_volume = ParallelStrategyCalculator.compute_communication_volume(
            model, tp_config
        )

        assert "tensor_parallel" in comm_volume
        assert "pipeline_parallel" in comm_volume
        assert "data_parallel" in comm_volume
        assert "total" in comm_volume

        assert comm_volume["tensor_parallel"] > 0
        assert comm_volume["pipeline_parallel"] == 0
        assert comm_volume["data_parallel"] == 0
        assert comm_volume["total"] == comm_volume["tensor_parallel"]

    def test_pipeline_parallel_communication(self):
        """Test communication volume for pipeline parallelism."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        pp_config = ParallelConfig(pipeline_parallel_size=4)

        comm_volume = ParallelStrategyCalculator.compute_communication_volume(
            model, pp_config
        )

        assert comm_volume["pipeline_parallel"] > 0
        assert comm_volume["tensor_parallel"] == 0
        assert comm_volume["data_parallel"] == 0

    def test_data_parallel_communication(self):
        """Test communication volume for data parallelism."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        dp_config = ParallelConfig(data_parallel_size=4)

        comm_volume = ParallelStrategyCalculator.compute_communication_volume(
            model, dp_config
        )

        assert comm_volume["data_parallel"] > 0
        assert comm_volume["tensor_parallel"] == 0
        assert comm_volume["pipeline_parallel"] == 0

    def test_mixed_parallel_communication(self):
        """Test communication volume for mixed parallelism."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        mixed_config = ParallelConfig(
            tensor_parallel_size=2, pipeline_parallel_size=2, data_parallel_size=2
        )

        comm_volume = ParallelStrategyCalculator.compute_communication_volume(
            model, mixed_config
        )

        assert comm_volume["tensor_parallel"] > 0
        assert comm_volume["pipeline_parallel"] > 0
        assert comm_volume["data_parallel"] > 0
        assert comm_volume["total"] > 0

        # Total should be sum of all components
        expected_total = (
            comm_volume["tensor_parallel"]
            + comm_volume["pipeline_parallel"]
            + comm_volume["data_parallel"]
        )
        assert comm_volume["total"] == expected_total

    def test_communication_volume_scaling(self):
        """Test that communication volume scales with parallel size."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        # Test tensor parallel scaling
        tp_sizes = [1, 2, 4, 8]
        previous_volume = 0

        for tp_size in tp_sizes:
            if tp_size == 1:
                continue  # Skip no parallelism case

            config = ParallelConfig(tensor_parallel_size=tp_size)
            comm_volume = ParallelStrategyCalculator.compute_communication_volume(
                model, config
            )

            if previous_volume > 0:
                # Communication volume should generally increase with more parallelism
                # (though this depends on the specific implementation)
                assert comm_volume["tensor_parallel"] >= 0

            previous_volume = comm_volume["tensor_parallel"]


class TestCommunicationOverhead:
    """Test communication overhead estimation."""

    def test_basic_communication_overhead(self):
        """Test basic communication overhead estimation."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        parallel_config = ParallelConfig(tensor_parallel_size=4)

        comm_time = ParallelStrategyCalculator.estimate_communication_overhead(
            model, parallel_config
        )

        assert "tensor_parallel" in comm_time
        assert "pipeline_parallel" in comm_time
        assert "data_parallel" in comm_time
        assert "total" in comm_time

        assert comm_time["tensor_parallel"] > 0
        assert comm_time["total"] > 0

    def test_communication_overhead_with_network_params(self):
        """Test communication overhead with different network parameters."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        parallel_config = ParallelConfig(tensor_parallel_size=4)

        # Test with fast network
        fast_comm_time = ParallelStrategyCalculator.estimate_communication_overhead(
            model, parallel_config, bandwidth_gbps=100.0, latency_us=1.0
        )

        # Test with slow network
        slow_comm_time = ParallelStrategyCalculator.estimate_communication_overhead(
            model, parallel_config, bandwidth_gbps=10.0, latency_us=100.0
        )

        # Slow network should have higher communication time
        assert slow_comm_time["total"] > fast_comm_time["total"]

    def test_communication_overhead_different_parallel_types(self):
        """Test communication overhead for different parallel types."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        configs = [
            ParallelConfig(tensor_parallel_size=4),
            ParallelConfig(pipeline_parallel_size=4),
            ParallelConfig(data_parallel_size=4),
        ]

        for config in configs:
            comm_time = ParallelStrategyCalculator.estimate_communication_overhead(
                model, config
            )
            assert comm_time["total"] > 0

            # Check that only the relevant parallel type has overhead
            if config.tensor_parallel_size > 1:
                assert comm_time["tensor_parallel"] > 0
            else:
                assert comm_time["tensor_parallel"] == 0


class TestOptimalConfigSuggestions:
    """Test optimal parallel configuration suggestions."""

    def test_suggest_optimal_config_basic(self):
        """Test basic optimal configuration suggestions."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        suggestions = ParallelStrategyCalculator.suggest_optimal_parallel_config(
            model, num_devices=8, memory_per_device_gb=80.0
        )

        assert len(suggestions) > 0

        # Verify all suggestions are valid
        for suggestion in suggestions:
            assert_parallel_config_valid(suggestion)
            assert ParallelStrategyCalculator.validate_parallel_config(
                suggestion, model._parsed_config
            )

            # Verify device count constraint
            total_devices = (
                suggestion.tensor_parallel_size
                * suggestion.pipeline_parallel_size
                * suggestion.data_parallel_size
            )
            assert total_devices <= 8

    def test_suggest_optimal_config_constraints(self):
        """Test optimal configuration suggestions with different constraints."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        # Test with limited devices
        suggestions_limited = (
            ParallelStrategyCalculator.suggest_optimal_parallel_config(
                model, num_devices=2, memory_per_device_gb=80.0
            )
        )

        # Test with more devices
        suggestions_many = ParallelStrategyCalculator.suggest_optimal_parallel_config(
            model, num_devices=32, memory_per_device_gb=80.0
        )

        # Should have different suggestions for different constraints
        assert len(suggestions_limited) > 0
        assert len(suggestions_many) > 0

        # Limited devices should have smaller parallel sizes
        max_limited_devices = max(
            s.tensor_parallel_size * s.pipeline_parallel_size * s.data_parallel_size
            for s in suggestions_limited
        )
        assert max_limited_devices <= 2

    def test_suggest_optimal_config_memory_constraints(self):
        """Test optimal configuration suggestions with memory constraints."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        # Test with low memory
        suggestions_low_mem = (
            ParallelStrategyCalculator.suggest_optimal_parallel_config(
                model, num_devices=8, memory_per_device_gb=16.0
            )
        )

        # Test with high memory
        suggestions_high_mem = (
            ParallelStrategyCalculator.suggest_optimal_parallel_config(
                model, num_devices=8, memory_per_device_gb=160.0
            )
        )

        assert len(suggestions_low_mem) > 0
        assert len(suggestions_high_mem) > 0

        # Low memory should prefer more parallelism to reduce per-device memory
        # This is implementation-dependent, but we can at least check suggestions exist


class TestMemoryAnalysis:
    """Test memory requirements analysis."""

    def test_basic_memory_analysis(self):
        """Test basic memory requirements analysis."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        parallel_config = ParallelConfig(
            tensor_parallel_size=4, pipeline_parallel_size=2
        )

        memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
            model, parallel_config
        )

        required_keys = [
            "total_memory_gb",
            "memory_per_device_gb",
            "memory_breakdown",
            "memory_efficiency",
        ]
        for key in required_keys:
            assert key in memory_analysis

        assert memory_analysis["total_memory_gb"] > 0
        assert memory_analysis["memory_per_device_gb"] > 0
        assert 0 <= memory_analysis["memory_efficiency"] <= 1

    def test_memory_breakdown_structure(self):
        """Test memory breakdown structure."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        parallel_config = ParallelConfig(tensor_parallel_size=2)

        memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
            model, parallel_config
        )

        breakdown = memory_analysis["memory_breakdown"]
        assert "parameters" in breakdown
        assert "activations" in breakdown

        # Check parameter breakdown
        param_breakdown = breakdown["parameters"]
        assert "total_gb" in param_breakdown
        assert "per_device_gb" in param_breakdown
        assert "sharding_strategy" in param_breakdown

        # Check activation breakdown
        activation_breakdown = breakdown["activations"]
        assert "total_gb" in activation_breakdown
        assert "per_device_gb" in activation_breakdown
        assert "sharding_strategy" in activation_breakdown

    def test_memory_analysis_different_parallel_configs(self):
        """Test memory analysis with different parallel configurations."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        configs = [
            ParallelConfig(),  # No parallelism
            ParallelConfig(tensor_parallel_size=2),
            ParallelConfig(pipeline_parallel_size=2),
            ParallelConfig(tensor_parallel_size=2, pipeline_parallel_size=2),
        ]

        for config in configs:
            memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
                model, config
            )

            assert memory_analysis["total_memory_gb"] > 0
            assert memory_analysis["memory_per_device_gb"] > 0

            # Per-device memory should be less than or equal to total memory
            total_devices = (
                config.tensor_parallel_size
                * config.pipeline_parallel_size
                * config.data_parallel_size
            )
            expected_per_device = memory_analysis["total_memory_gb"] / total_devices

            # Allow for some overhead in the calculation
            assert memory_analysis["memory_per_device_gb"] <= expected_per_device * 1.1


class TestPerformanceAnalysis:
    """Test performance characteristics analysis."""

    def test_basic_performance_analysis(self):
        """Test basic performance characteristics analysis."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        parallel_config = ParallelConfig(tensor_parallel_size=4)

        perf_analysis = ParallelStrategyCalculator.analyze_performance_characteristics(
            model, parallel_config
        )

        required_keys = [
            "compute_time_ms",
            "communication_time_ms",
            "total_time_ms",
            "compute_efficiency",
            "communication_overhead",
            "model_flops_utilization",
            "tokens_per_second",
        ]

        for key in required_keys:
            assert key in perf_analysis

        # Check value ranges
        assert perf_analysis["compute_time_ms"] > 0
        assert perf_analysis["total_time_ms"] > 0
        assert 0 <= perf_analysis["compute_efficiency"] <= 1
        assert perf_analysis["tokens_per_second"] > 0

    def test_performance_analysis_scaling(self):
        """Test performance analysis with different parallel sizes."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        # Test different tensor parallel sizes
        tp_sizes = [1, 2, 4, 8]

        for tp_size in tp_sizes:
            if tp_size > 32:  # Skip invalid configs
                continue

            config = ParallelConfig(tensor_parallel_size=tp_size)
            perf_analysis = (
                ParallelStrategyCalculator.analyze_performance_characteristics(
                    model, config
                )
            )

            assert perf_analysis["compute_time_ms"] > 0
            assert perf_analysis["total_time_ms"] >= perf_analysis["compute_time_ms"]

            # Communication overhead should increase with more parallelism
            if tp_size > 1:
                assert perf_analysis["communication_time_ms"] > 0
            else:
                assert perf_analysis["communication_time_ms"] == 0


class TestParallelStrategyEdgeCases:
    """Test edge cases and error conditions for parallel strategies."""

    def test_invalid_tensor_parallel_sizes(self):
        """Test validation with invalid tensor parallel sizes."""
        model_config = create_mock_model_config("llama")

        # Test with TP size larger than number of heads
        invalid_config = ParallelConfig(tensor_parallel_size=64)  # 32 heads < 64
        assert not ParallelStrategyCalculator.validate_parallel_config(
            invalid_config, model_config
        )

        # Test with TP size that doesn't divide evenly
        invalid_config = ParallelConfig(tensor_parallel_size=7)  # 32 % 7 != 0
        assert not ParallelStrategyCalculator.validate_parallel_config(
            invalid_config, model_config
        )

    def test_zero_dimension_handling(self):
        """Test handling of zero dimensions in parallel calculations."""
        zero_shapes = {"matrix": (0, 1024)}

        # Should handle zero dimensions gracefully
        parallel_shapes = ParallelStrategyCalculator.compute_tensor_parallel_shapes(
            zero_shapes, 4
        )
        assert parallel_shapes["matrix"][0] == 0
        assert parallel_shapes["matrix"][1] == 1024 // 4

    def test_single_device_optimization(self):
        """Test optimization suggestions for single device."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        suggestions = ParallelStrategyCalculator.suggest_optimal_parallel_config(
            model, num_devices=1, memory_per_device_gb=80.0
        )

        # All suggestions should use only 1 device
        for suggestion in suggestions:
            total_devices = (
                suggestion.tensor_parallel_size
                * suggestion.pipeline_parallel_size
                * suggestion.data_parallel_size
            )
            assert total_devices == 1

    def test_memory_constrained_optimization(self):
        """Test optimization with severe memory constraints."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        # Very low memory constraint
        suggestions = ParallelStrategyCalculator.suggest_optimal_parallel_config(
            model, num_devices=8, memory_per_device_gb=1.0  # Very low
        )

        # Should still provide suggestions, even if they don't meet constraints
        assert len(suggestions) > 0

        # Analyze memory for the suggestions
        for suggestion in suggestions:
            memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
                model, suggestion
            )
            # Memory analysis should complete without errors
            assert "total_memory_gb" in memory_analysis

    def test_extreme_parallel_configurations(self):
        """Test with extreme parallel configurations."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        # Test with maximum valid tensor parallelism
        max_tp_config = ParallelConfig(tensor_parallel_size=32)  # Equal to num_heads

        # Should be valid
        assert ParallelStrategyCalculator.validate_parallel_config(
            max_tp_config, model._parsed_config
        )

        # Communication volume should be computed
        comm_volume = ParallelStrategyCalculator.compute_communication_volume(
            model, max_tp_config
        )
        assert comm_volume["tensor_parallel"] > 0

    def test_mixed_parallel_edge_cases(self):
        """Test mixed parallelism with edge cases."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        # Test with all parallel dimensions at maximum
        mixed_config = ParallelConfig(
            tensor_parallel_size=4, pipeline_parallel_size=8, data_parallel_size=2
        )  # Total: 64 devices

        # Should handle large device counts
        memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
            model, mixed_config
        )
        assert memory_analysis["memory_per_device_gb"] > 0

        perf_analysis = ParallelStrategyCalculator.analyze_performance_characteristics(
            model, mixed_config
        )
        assert perf_analysis["tokens_per_second"] > 0


class TestOptimizationSuggestions:
    """Test optimization suggestions generation."""

    def test_basic_optimization_suggestions(self):
        """Test basic optimization suggestions generation."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        current_config = ParallelConfig(tensor_parallel_size=2, data_parallel_size=2)
        constraints = {
            "num_devices": 8,
            "memory_per_device_gb": 80.0,
            "bandwidth_gbps": 100.0,
            "latency_us": 10.0,
        }

        suggestions = ParallelStrategyCalculator.generate_optimization_suggestions(
            model, current_config, constraints
        )

        required_keys = [
            "configuration_alternatives",
            "general_suggestions",
            "current_performance",
            "current_memory",
        ]

        for key in required_keys:
            assert key in suggestions

        # Check configuration alternatives structure
        alternatives = suggestions["configuration_alternatives"]
        assert isinstance(alternatives, list)

        for alt in alternatives:
            assert "config" in alt
            assert "performance_impact" in alt
            assert "meets_constraints" in alt
            assert "recommendation_reasons" in alt

            assert isinstance(alt["config"], ParallelConfig)
            assert isinstance(alt["meets_constraints"], bool)
            assert isinstance(alt["recommendation_reasons"], list)

    def test_optimization_suggestions_different_constraints(self):
        """Test optimization suggestions with different constraints."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))
        current_config = ParallelConfig(tensor_parallel_size=1)

        # Test with tight memory constraints
        tight_constraints = {
            "num_devices": 4,
            "memory_per_device_gb": 16.0,
            "bandwidth_gbps": 50.0,
            "latency_us": 50.0,
        }

        suggestions_tight = (
            ParallelStrategyCalculator.generate_optimization_suggestions(
                model, current_config, tight_constraints
            )
        )

        # Test with loose constraints
        loose_constraints = {
            "num_devices": 16,
            "memory_per_device_gb": 160.0,
            "bandwidth_gbps": 200.0,
            "latency_us": 5.0,
        }

        suggestions_loose = (
            ParallelStrategyCalculator.generate_optimization_suggestions(
                model, current_config, loose_constraints
            )
        )

        # Both should have suggestions
        assert len(suggestions_tight["configuration_alternatives"]) > 0
        assert len(suggestions_loose["configuration_alternatives"]) > 0

        # Suggestions should be different for different constraints
        tight_configs = [
            alt["config"] for alt in suggestions_tight["configuration_alternatives"]
        ]
        loose_configs = [
            alt["config"] for alt in suggestions_loose["configuration_alternatives"]
        ]

        # At least some configurations should be different
        # (This is implementation-dependent, but generally expected)
        assert tight_configs != loose_configs or len(tight_configs) != len(
            loose_configs
        )


class TestParallelStrategiesIntegration:
    """Integration tests for parallel strategies."""

    def test_end_to_end_parallel_analysis(self):
        """Test complete parallel strategy analysis workflow."""
        model = MockModel("test-model", MockConfig(create_mock_model_config("llama")))

        # Step 1: Get optimal configuration suggestions
        suggestions = ParallelStrategyCalculator.suggest_optimal_parallel_config(
            model, num_devices=8, memory_per_device_gb=80.0
        )

        assert len(suggestions) > 0
        best_config = suggestions[0]  # Take first suggestion

        # Step 2: Validate the configuration
        assert ParallelStrategyCalculator.validate_parallel_config(
            best_config, model._parsed_config
        )

        # Step 3: Analyze memory requirements
        memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
            model, best_config
        )
        assert memory_analysis["memory_per_device_gb"] <= 80.0  # Should meet constraint

        # Step 4: Analyze performance characteristics
        perf_analysis = ParallelStrategyCalculator.analyze_performance_characteristics(
            model, best_config
        )
        assert perf_analysis["tokens_per_second"] > 0

        # Step 5: Get optimization suggestions
        constraints = {
            "num_devices": 8,
            "memory_per_device_gb": 80.0,
            "bandwidth_gbps": 100.0,
            "latency_us": 10.0,
        }

        optimization_suggestions = (
            ParallelStrategyCalculator.generate_optimization_suggestions(
                model, best_config, constraints
            )
        )

        assert "configuration_alternatives" in optimization_suggestions
        assert "current_performance" in optimization_suggestions

    def test_parallel_strategies_with_different_models(self):
        """Test parallel strategies with different model configurations."""
        model_configs = [
            create_mock_model_config("llama", hidden_size=2048, num_attention_heads=16),
            create_mock_model_config("llama", hidden_size=4096, num_attention_heads=32),
            create_mock_model_config("llama", hidden_size=8192, num_attention_heads=64),
        ]

        for config_dict in model_configs:
            model = MockModel(
                f"test-model-{config_dict['hidden_size']}", MockConfig(config_dict)
            )

            # Test that all operations work with different model sizes
            suggestions = ParallelStrategyCalculator.suggest_optimal_parallel_config(
                model, num_devices=8, memory_per_device_gb=80.0
            )
            assert len(suggestions) > 0

            # Test validation
            for suggestion in suggestions[:3]:  # Test first few suggestions
                assert ParallelStrategyCalculator.validate_parallel_config(
                    suggestion, model._parsed_config
                )

            # Test memory analysis
            memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
                model, suggestions[0]
            )
            assert memory_analysis["total_memory_gb"] > 0

"""
Backward compatibility tests for mixed precision support.

These tests ensure that existing code continues to work unchanged
when mixed precision features are added to the system.
"""

from unittest.mock import Mock, patch

import pytest

from llm_modeling_metrics.core.base_model import ParallelConfig
from llm_modeling_metrics.core.operators import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>NL<PERSON>er,
    <PERSON><PERSON>ul<PERSON>perator,
    MoELayer,
)
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel
from tests.conftest import MockConfig, create_mock_model_config


class TestOperatorBackwardCompatibility:
    """Test that operators maintain backward compatibility."""

    def test_matmul_operator_legacy_interface(self):
        """Test MatMulOperator with legacy interface."""
        # Legacy initialization (should still work)
        op = MatMulOperator(M=1024, N=2048, K=1024)

        # Should use default precision
        assert hasattr(op, "input_precision")
        assert hasattr(op, "weight_precision")
        assert hasattr(op, "output_precision")

        # Legacy methods should work
        flops = op.compute_flops(batch_size=1, sequence_length=2048)
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=2048)
        movement = op.compute_memory_movement_bytes(batch_size=1, sequence_length=2048)
        params = op.compute_params()

        assert flops > 0
        assert memory > 0
        assert movement > 0
        assert params > 0

    def test_matmul_operator_precision_parameter(self):
        """Test MatMulOperator with legacy precision parameter."""
        # Legacy precision parameter should still work
        op = MatMulOperator(M=1024, N=2048, K=1024, precision="fp16")

        # Should apply to all precisions
        assert op.input_precision == "fp16"
        assert op.weight_precision == "fp16"
        assert op.output_precision == "fp16"

        # Calculations should work
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=2048)
        assert memory > 0

    def test_attention_operator_legacy_interface(self):
        """Test AttentionOperator with legacy interface."""
        # Legacy initialization
        op = AttentionOperator(
            hidden_size=4096, num_heads=32, batch_size=1, sequence_length=2048
        )

        # Should have default precisions
        assert hasattr(op, "activation_precision")
        assert hasattr(op, "kv_cache_precision")

        # Legacy methods should work
        flops = op.compute_flops(batch_size=1, sequence_length=2048)
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=2048)

        assert flops > 0
        assert memory > 0

    def test_mlp_operator_legacy_interface(self):
        """Test FFNLayer with legacy interface."""
        # Legacy initialization
        op = FFNLayer(hidden_size=4096, intermediate_size=11008)

        # Should have default precisions
        assert hasattr(op, "weight_precision")
        assert hasattr(op, "activation_precision")

        # Legacy methods should work
        flops = op.compute_flops(batch_size=1, sequence_length=2048)
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=2048)

        assert flops > 0
        assert memory > 0

    def test_moe_operator_legacy_interface(self):
        """Test MoELayer with legacy interface."""
        # Legacy initialization
        op = MoELayer(
            hidden_size=4096,
            intermediate_size=1407,
            num_experts=64,
            experts_per_token=6,
        )

        # Should have default expert precision
        assert hasattr(op, "expert_parameter_precision")

        # Legacy methods should work
        flops = op.compute_flops(batch_size=1, sequence_length=2048)
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=2048)
        params = op.compute_params()

        assert flops > 0
        assert memory > 0
        assert params > 0

    def test_operator_bytes_per_element_legacy(self):
        """Test that get_bytes_per_element works with legacy interface."""
        op = MatMulOperator(M=10, N=20, K=30, precision="bf16")

        # Should work without precision parameter (use default)
        bytes_per_elem = op.get_bytes_per_element()
        assert bytes_per_elem == 2  # bf16 = 2 bytes

        # Should work with explicit precision
        bytes_fp32 = op.get_bytes_per_element("fp32")
        assert bytes_fp32 == 4


class TestDenseModelBackwardCompatibility:
    """Test that DenseModel maintains backward compatibility."""

    def test_dense_model_legacy_initialization(self):
        """Test DenseModel with legacy initialization."""
        config = MockConfig(create_mock_model_config("llama"))

        # Legacy initialization should work
        model = DenseModel("meta-llama/Llama-2-7b-hf", config)

        # All legacy methods should work
        attention_params = model.compute_attention_params()
        mlp_params = model.compute_mlp_params()
        embedding_params = model.compute_embedding_params()
        total_params = model.get_total_params()

        assert attention_params > 0
        assert mlp_params > 0
        assert embedding_params > 0
        assert total_params == attention_params + mlp_params + embedding_params

    def test_dense_model_legacy_memory_computation(self):
        """Test DenseModel memory computation with legacy interface."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Legacy memory computation should work
        memory = model.compute_memory_requirements(
            sequence_length=2048, batch_size=1, dtype="bf16", training=True
        )

        # Should have expected keys
        expected_keys = [
            "parameters",
            "activations",
            "gradients",
            "optimizer_states",
            "total",
        ]
        for key in expected_keys:
            assert key in memory
            assert memory[key] >= 0

        # Total should be sum of components
        component_sum = sum(memory[k] for k in expected_keys if k != "total")
        assert abs(memory["total"] - component_sum) <= 1

    def test_dense_model_legacy_flops_computation(self):
        """Test DenseModel FLOP computation with legacy interface."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Legacy FLOP computation should work
        flops = model.compute_flops(sequence_length=2048, batch_size=1)

        # Should have expected components
        expected_components = [
            "attention",
            "mlp",
            "layernorm",
            "final_layernorm",
            "embeddings",
        ]
        for component in expected_components:
            if component in flops:
                assert flops[component] >= 0

        # Should have positive total FLOPs
        total_flops = sum(flops.values())
        assert total_flops > 0

    def test_dense_model_legacy_matrix_shapes(self):
        """Test DenseModel matrix shapes with legacy interface."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Legacy matrix shapes should work
        shapes = model.get_matrix_shapes()

        # Should have expected structure
        assert "attention" in shapes
        assert "mlp" in shapes

        # Attention shapes
        attention_shapes = shapes["attention"]
        expected_attention = ["q_proj", "k_proj", "v_proj", "o_proj"]
        for proj in expected_attention:
            assert proj in attention_shapes
            assert isinstance(attention_shapes[proj], tuple)
            assert len(attention_shapes[proj]) == 2

        # MLP shapes
        mlp_shapes = shapes["mlp"]
        expected_mlp = ["gate_proj", "up_proj", "down_proj"]
        for proj in expected_mlp:
            assert proj in mlp_shapes
            assert isinstance(mlp_shapes[proj], tuple)
            assert len(mlp_shapes[proj]) == 2

    def test_dense_model_legacy_metrics(self):
        """Test DenseModel metrics generation with legacy interface."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("meta-llama/Llama-2-7b-hf", config)

        # Legacy metrics generation should work
        metrics = model.get_metrics()

        # Should have all expected attributes
        expected_attrs = [
            "model_name",
            "architecture",
            "total_params",
            "attention_params",
            "mlp_params",
            "embedding_params",
            "flops_forward",
            "flops_per_token",
            "memory_params",
            "memory_activations",
            "memory_total",
            "attention_shapes",
            "mlp_shapes",
            "sequence_length",
            "batch_size",
        ]

        for attr in expected_attrs:
            assert hasattr(metrics, attr), f"Missing attribute: {attr}"

        # Values should be reasonable
        assert metrics.model_name == "meta-llama/Llama-2-7b-hf"
        assert metrics.total_params > 0
        assert metrics.flops_forward > 0
        assert metrics.memory_total > 0

    def test_dense_model_legacy_parallel_config(self):
        """Test DenseModel with legacy parallel configuration."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Legacy parallel config should work
        parallel_config = ParallelConfig(tensor_parallel_size=2)

        # Should validate correctly
        is_valid = model.validate_parallel_config(parallel_config)
        assert isinstance(is_valid, bool)

        # Matrix shapes with parallel config should work
        shapes = model.get_matrix_shapes(parallel_config)
        assert "attention" in shapes
        assert "mlp" in shapes

        # Metrics with parallel config should work
        metrics = model.get_metrics(parallel_config=parallel_config)
        assert metrics.parallel_config == parallel_config


class TestMoEModelBackwardCompatibility:
    """Test that MoEModel maintains backward compatibility."""

    def test_moe_model_legacy_initialization(self):
        """Test MoEModel with legacy initialization."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))

        # Legacy initialization should work
        model = MoEModel("deepseek-ai/DeepSeek-R1", config)

        # All legacy methods should work
        attention_params = model.compute_attention_params()
        mlp_params = model.compute_mlp_params()
        embedding_params = model.compute_embedding_params()
        total_params = model.get_total_params()
        active_params = model.compute_active_params_per_token()

        assert attention_params > 0
        assert mlp_params > 0
        assert embedding_params > 0
        assert total_params > 0
        assert active_params > 0
        assert active_params < total_params  # Should be sparse

    def test_moe_model_legacy_memory_computation(self):
        """Test MoEModel memory computation with legacy interface."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        # Legacy memory computation should work
        memory = model.compute_memory_requirements(
            sequence_length=2048, batch_size=1, dtype="bf16", training=True
        )

        # Should have MoE-specific keys
        expected_keys = [
            "parameters",
            "activations",
            "kv_cache",
            "expert_cache",
            "total",
        ]
        for key in expected_keys:
            assert key in memory
            assert memory[key] >= 0

    def test_moe_model_legacy_flops_computation(self):
        """Test MoEModel FLOP computation with legacy interface."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        # Legacy FLOP computation should work
        flops = model.compute_flops(sequence_length=2048, batch_size=1)
        flops_per_token = model.compute_flops_per_token()

        # Should have MoE-specific components
        expected_components = ["attention", "moe", "router", "total"]
        for component in expected_components:
            if component in flops:
                assert flops[component] >= 0

        assert flops_per_token > 0
        assert flops_per_token < flops.get("total", float("inf"))  # Should be sparse

    def test_moe_model_legacy_matrix_shapes(self):
        """Test MoEModel matrix shapes with legacy interface."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        # Legacy matrix shapes should work
        shapes = model.get_matrix_shapes()

        # Should have MoE-specific structure
        assert "attention" in shapes
        assert "moe" in shapes
        assert "router" in shapes

        # MoE shapes should have expert information
        moe_shapes = shapes["moe"]
        assert "routed_experts" in moe_shapes or "shared_experts" in moe_shapes

    def test_moe_model_legacy_metrics(self):
        """Test MoEModel metrics generation with legacy interface."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("deepseek-ai/DeepSeek-R1", config)

        # Legacy metrics generation should work
        metrics = model.get_metrics()

        # Should have MoE-specific attributes
        moe_attrs = ["experts_per_token", "active_params_per_token"]
        for attr in moe_attrs:
            if hasattr(metrics, attr):
                assert getattr(metrics, attr) > 0

        # Standard attributes should also be present
        assert metrics.model_name == "deepseek-ai/DeepSeek-R1"
        assert metrics.total_params > 0
        assert metrics.flops_forward > 0
        assert metrics.memory_total > 0


class TestBackwardCompatibilityWithMixedPrecision:
    """Test backward compatibility when mixed precision is available."""

    def test_legacy_code_with_mixed_precision_available(self):
        """Test that legacy code works when mixed precision features are available."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Legacy code should work exactly as before
        memory_legacy = model.compute_memory_requirements(
            sequence_length=2048, batch_size=1, dtype="bf16", training=True
        )

        # Should have same structure as before
        assert "parameters" in memory_legacy
        assert "activations" in memory_legacy
        assert "total" in memory_legacy

        # Values should be reasonable
        assert memory_legacy["total"] > 0
        assert memory_legacy["parameters"] > 0

    def test_mixed_precision_does_not_break_legacy_api(self):
        """Test that adding mixed precision doesn't break legacy API."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Legacy API calls should work
        params = model.get_total_params()
        flops = model.compute_flops(sequence_length=2048, batch_size=1)
        memory = model.compute_memory_requirements(sequence_length=2048, batch_size=1)
        shapes = model.get_matrix_shapes()
        metrics = model.get_metrics()

        # All should return valid results
        assert params > 0
        assert sum(flops.values()) > 0
        assert memory["total"] > 0
        assert len(shapes) > 0
        assert metrics.total_params == params

    def test_parameter_precedence_backward_compatibility(self):
        """Test parameter precedence maintains backward compatibility."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Legacy dtype parameter should still work
        memory_legacy = model.compute_memory_requirements(
            sequence_length=2048, batch_size=1, dtype="fp16", training=True
        )

        # Mixed precision with equivalent settings
        memory_mixed = model.compute_memory_requirements(
            sequence_length=2048,
            batch_size=1,
            weight_dtype="fp16",
            activation_dtype="fp16",
            kv_cache_dtype="fp16",
            grad_dtype="fp16",
            optimizer_dtype="fp16",
            training=True,
        )

        # Results should be very similar (allowing for small differences)
        for key in memory_legacy:
            if key in memory_mixed:
                diff_ratio = abs(memory_legacy[key] - memory_mixed[key]) / max(
                    memory_legacy[key], 1
                )
                assert diff_ratio < 0.01, f"Backward compatibility broken for {key}"

    def test_default_precision_behavior(self):
        """Test that default precision behavior is maintained."""
        # Operators should use reasonable defaults
        op = MatMulOperator(M=10, N=20, K=30)

        # Should have default precisions set
        assert op.input_precision in ["bf16", "fp16", "fp32"]
        assert op.weight_precision in ["bf16", "fp16", "fp32"]
        assert op.output_precision in ["bf16", "fp16", "fp32"]

        # Should work without any precision specification
        memory = op.compute_memory_capacity_bytes(batch_size=1, sequence_length=1)
        assert memory > 0

    def test_legacy_error_handling(self):
        """Test that legacy error handling still works."""
        # Invalid model config should still raise appropriate errors
        invalid_config = MockConfig(
            {
                "hidden_size": 0,  # Invalid
                "num_hidden_layers": 32,
                "num_attention_heads": 32,
                "intermediate_size": 11008,
                "vocab_size": 32000,
                "model_type": "llama",
            }
        )

        with pytest.raises((ValueError, ZeroDivisionError)):
            DenseModel("invalid-model", invalid_config)

        # Invalid parallel config should still be caught
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        invalid_parallel = ParallelConfig(
            tensor_parallel_size=3
        )  # 32 not divisible by 3
        is_valid = model.validate_parallel_config(invalid_parallel)
        assert not is_valid

    def test_legacy_caching_behavior(self):
        """Test that legacy caching behavior is maintained."""
        config = MockConfig(create_mock_model_config("llama"))
        model = DenseModel("test-model", config)

        # Multiple calls should return consistent results
        params1 = model.get_total_params()
        params2 = model.get_total_params()
        assert params1 == params2

        memory1 = model.compute_memory_requirements(sequence_length=2048, batch_size=1)
        memory2 = model.compute_memory_requirements(sequence_length=2048, batch_size=1)
        assert memory1 == memory2

    def test_legacy_model_factory_compatibility(self):
        """Test that model factory maintains backward compatibility."""
        from llm_modeling_metrics.core.model_factory import ModelFactory

        # Legacy model registration should work
        ModelFactory.register_model("test_arch", DenseModel)

        # Legacy model creation should work
        config = create_mock_model_config("llama")
        model = ModelFactory.create_model("test_arch", "test-model", config)

        assert isinstance(model, DenseModel)
        assert model.model_name == "test-model"

        # Cleanup
        ModelFactory.clear_registry()


class TestLegacyAPICompatibility:
    """Test that API maintains backward compatibility."""

    @pytest.fixture
    def mock_client(self):
        """Create mock API client."""
        from fastapi.testclient import TestClient

        from llm_modeling_metrics.web.app import app

        return TestClient(app)

    @pytest.fixture
    def mock_model_factory(self):
        """Mock model factory for API tests."""
        with patch("llm_modeling_metrics.web.app.ModelFactory") as mock_factory:
            # Mock config manager
            mock_config_manager = Mock()
            mock_config_manager.fetch_config.return_value = create_mock_model_config(
                "llama"
            )
            mock_factory.get_config_manager.return_value = mock_config_manager

            # Mock model creation
            mock_model = Mock()
            mock_model.get_metrics.return_value = Mock(
                model_name="test-model",
                total_params=7000000000,
                flops_forward=14000000000000,
                memory_total=28000000000,
            )
            mock_factory.create_model.return_value = mock_model

            yield mock_factory

    def test_legacy_api_analyze_endpoint(self, mock_client, mock_model_factory):
        """Test that legacy API analyze endpoint still works."""
        # Legacy request format
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "dtype": "bf16",  # Legacy parameter
        }

        response = mock_client.post("/api/analyze", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "results" in data
        assert "meta-llama/Llama-2-7b-hf" in data["results"]

    def test_legacy_api_compare_endpoint(self, mock_client, mock_model_factory):
        """Test that legacy API compare endpoint still works."""
        # Legacy request format
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf", "meta-llama/Llama-2-13b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "dtype": "bf16",  # Legacy parameter
        }

        response = mock_client.post("/api/compare", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "comparison" in data

    def test_legacy_api_without_dtype(self, mock_client, mock_model_factory):
        """Test that API works without any dtype specification."""
        # Request without any precision parameters
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        response = mock_client.post("/api/analyze", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "results" in data
        assert "meta-llama/Llama-2-7b-hf" in data["results"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

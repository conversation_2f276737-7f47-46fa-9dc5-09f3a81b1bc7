import sys

sys.path.append(".")
from llm_modeling_metrics.core.operators import (
    AttentionOperator,
    CommunicationOperator,
    FFNLayer,
    LayerNormOperator,
    MatMulOperator,
    MLAAttentionOperator,
    MoELayer,
)

print("=== Testing all operators without legacy precision ===")

# Test all operator types with mixed precision
operators = [
    MatMulOperator(
        M=1, N=1024, K=1024, weight_precision="fp8", activation_precision="bf16"
    ),
    AttentionOperator(
        hidden_size=1024,
        num_heads=16,
        weight_precision="fp8",
        activation_precision="bf16",
    ),
    FFNLayer(
        hidden_size=1024,
        intermediate_size=4096,
        weight_precision="fp8",
        activation_precision="bf16",
    ),
    MLAAttentionOperator(
        hidden_size=1024,
        num_heads=16,
        weight_precision="fp8",
        activation_precision="bf16",
    ),
    LayerNormOperator(
        hidden_size=1024, weight_precision="fp8", activation_precision="bf16"
    ),
    CommunicationOperator(
        "allreduce", 1024, 8, weight_precision="fp8", activation_precision="bf16"
    ),
]

for i, op in enumerate(operators):
    print(f"{i+1}. {op.__class__.__name__}:")
    print(f"   Weight precision: {op.weight_precision}")
    print(f"   Activation precision: {op.activation_precision}")
    print(f"   get_bytes_per_element(): {op.get_bytes_per_element()}")

    # Test compute methods
    try:
        flops = op.compute_flops()
        memory = op.compute_memory_capacity_bytes()
        movement = op.compute_memory_movement_bytes()
        print(f"   FLOPs: {flops}, Memory: {memory}, Movement: {movement}")
    except Exception as e:
        print(f"   Error in compute methods: {e}")
    print()

print("All operators tested successfully!")

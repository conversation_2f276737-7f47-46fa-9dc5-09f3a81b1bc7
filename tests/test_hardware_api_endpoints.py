"""Tests for hardware API endpoints."""

import tempfile
from datetime import datetime
from pathlib import Path

import pytest
import yaml


class TestHardwareAPIEndpoints:
    """Test hardware API endpoints functionality."""

    @pytest.fixture
    def mock_hardware_specs(self):
        """Create mock hardware specifications."""
        return {
            "gpus": {
                "test_gpu": {
                    "name": "Test GPU",
                    "architecture": "Test Arch",
                    "memory_size_gb": 32,
                    "memory_bandwidth_gbps": 1000,
                    "tensor_performance": {"fp16_tensor": 100, "bf16_tensor": 100},
                    "vector_performance": {"fp32": 50, "fp16": 100},
                    "tdp_watts": 300,
                    "manufacturing_process": "7nm",
                }
            },
            "npus": {
                "test_npu": {
                    "name": "Test NPU",
                    "architecture": "Test NPU Arch",
                    "memory_size_gb": 64,
                    "memory_bandwidth_gbps": 800,
                    "tensor_performance": {"fp16_tensor": 200, "int8_tensor": 400},
                    "vector_performance": {"fp32": 100},
                    "tdp_watts": 400,
                }
            },
        }

    @pytest.fixture
    def temp_specs_file(self, mock_hardware_specs):
        """Create temporary hardware specs file."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
            yaml.dump(mock_hardware_specs, f)
            temp_file = f.name

        yield temp_file

        # Cleanup
        Path(temp_file).unlink()

    def test_hardware_service_integration(self, temp_specs_file):
        """Test hardware service integration with mock data."""
        from llm_modeling_metrics.hardware.service import HardwareService

        # Create service with test file
        service = HardwareService(temp_specs_file)

        # Test get_available_hardware
        hardware_dict = service.get_available_hardware()

        assert "gpu" in hardware_dict
        assert "npu" in hardware_dict
        assert len(hardware_dict["gpu"]) == 1
        assert len(hardware_dict["npu"]) == 1

        # Check GPU data
        gpu = hardware_dict["gpu"][0]
        assert gpu.id == "test_gpu"
        assert gpu.name == "Test GPU"
        assert gpu.memory_size_gb == 32
        assert gpu.memory_bandwidth_gbps == 1000

        # Check NPU data
        npu = hardware_dict["npu"][0]
        assert npu.id == "test_npu"
        assert npu.name == "Test NPU"
        assert npu.memory_size_gb == 64
        assert npu.memory_bandwidth_gbps == 800

    def test_hardware_service_empty(self):
        """Test hardware service with empty data."""
        from llm_modeling_metrics.hardware.service import HardwareService

        # Create empty specs file
        empty_specs = {"gpus": {}, "npus": {}}

        with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
            yaml.dump(empty_specs, f)
            temp_file = f.name

        try:
            service = HardwareService(temp_file)
            hardware_dict = service.get_available_hardware()

            assert hardware_dict["gpu"] == []
            assert hardware_dict["npu"] == []
        finally:
            Path(temp_file).unlink()

    def test_get_hardware_specs_success(self, temp_specs_file):
        """Test successful hardware specs retrieval."""
        from llm_modeling_metrics.hardware.service import HardwareService

        service = HardwareService(temp_specs_file)
        spec = service.get_hardware_specs("test_gpu")

        assert spec is not None
        assert spec.id == "test_gpu"
        assert spec.name == "Test GPU"
        assert spec.architecture == "Test Arch"
        assert spec.memory_size_gb == 32
        assert spec.memory_bandwidth_gbps == 1000
        assert "fp16" in spec.supported_precisions
        assert "bf16" in spec.supported_precisions
        assert "fp32" in spec.supported_precisions

    def test_get_hardware_specs_not_found(self, temp_specs_file):
        """Test hardware specs retrieval for non-existent hardware."""
        from llm_modeling_metrics.hardware.service import HardwareService

        service = HardwareService(temp_specs_file)
        spec = service.get_hardware_specs("nonexistent_gpu")

        assert spec is None

    def test_validate_hardware_compatibility_success(self, temp_specs_file):
        """Test successful hardware compatibility validation."""
        from llm_modeling_metrics.hardware.service import HardwareService

        service = HardwareService(temp_specs_file)

        # Create mock operators
        class MockOperator:
            def __init__(self):
                self.operator_type = "attention"
                self.precision = "fp16"
                self.parameters = 1000000
                self.memory_usage_bytes = 4000000

        operators = [MockOperator()]
        result = service.validate_hardware_compatibility("test_gpu", operators)

        assert result.is_valid
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        assert isinstance(result.recommendations, list)

    def test_validate_hardware_compatibility_not_found(self, temp_specs_file):
        """Test hardware compatibility validation with non-existent hardware."""
        from llm_modeling_metrics.hardware.service import HardwareService

        service = HardwareService(temp_specs_file)
        result = service.validate_hardware_compatibility("nonexistent_gpu", [])

        assert not result.is_valid
        assert len(result.errors) > 0
        assert any("not found" in error for error in result.errors)

    def test_validate_hardware_compatibility_invalid_request(self, temp_specs_file):
        """Test hardware compatibility validation with invalid request."""
        from llm_modeling_metrics.hardware.service import HardwareService

        service = HardwareService(temp_specs_file)

        # Test with empty hardware ID - should handle gracefully
        result = service.validate_hardware_compatibility("", [])
        assert not result.is_valid

    def test_get_hardware_recommendations_success(self, temp_specs_file):
        """Test successful hardware recommendations."""
        from llm_modeling_metrics.hardware.models import WorkloadProfile
        from llm_modeling_metrics.hardware.service import HardwareService

        service = HardwareService(temp_specs_file)

        workload = WorkloadProfile(
            model_type="dense",
            batch_size=32,
            sequence_length=2048,
            precision_requirements=["fp16", "bf16"],
            memory_constraints=30,
        )

        recommendations = service.get_hardware_recommendations(workload)

        assert isinstance(recommendations, list)
        # Should have recommendations for both GPU and NPU
        assert len(recommendations) >= 1

        if recommendations:
            recommendation = recommendations[0]
            assert hasattr(recommendation, "hardware_id")
            assert hasattr(recommendation, "hardware_name")
            assert hasattr(recommendation, "score")
            assert hasattr(recommendation, "reasons")
            assert 0 <= recommendation.score <= 100

    def test_get_hardware_recommendations_invalid_workload(self, temp_specs_file):
        """Test hardware recommendations with invalid workload."""
        from llm_modeling_metrics.hardware.models import WorkloadProfile
        from llm_modeling_metrics.hardware.service import HardwareService

        service = HardwareService(temp_specs_file)

        # Test with invalid model type - should handle gracefully
        try:
            workload = WorkloadProfile(
                model_type="invalid_type",  # Invalid model type
                batch_size=32,
                sequence_length=2048,
                precision_requirements=["fp16"],
            )
            # If validation passes, the service should still handle it
            recommendations = service.get_hardware_recommendations(workload)
            assert isinstance(recommendations, list)
        except ValueError:
            # Expected if Pydantic validation catches the invalid model type
            pass

    def test_reload_hardware_specifications_success(self, temp_specs_file):
        """Test successful hardware specifications reload."""
        from llm_modeling_metrics.hardware.service import HardwareService

        service = HardwareService(temp_specs_file)

        # Test reload - should not raise exception
        service.reload_hardware_specifications()

        # Verify hardware is still available after reload
        hardware_dict = service.get_available_hardware()
        assert "gpu" in hardware_dict
        assert "npu" in hardware_dict

    def test_reload_hardware_specifications_error(self):
        """Test hardware specifications reload with error."""
        from llm_modeling_metrics.hardware.service import HardwareService

        # Test with non-existent file
        try:
            service = HardwareService("nonexistent_file.yaml")
            # Should raise an error during initialization
            assert False, "Expected RuntimeError"
        except RuntimeError as e:
            assert "Failed to load hardware specifications" in str(e)


if __name__ == "__main__":
    pytest.main([__file__])

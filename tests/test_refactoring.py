"""
Test script to verify that the refactored parameter and FLOPS calculations
produce the same results as the original implementations.
"""

import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import Mo<PERSON>ode<PERSON>


def test_dense_model():
    """Test dense model parameter and FLOPS calculations."""
    print("Testing Dense Model...")

    # Create a simple dense model configuration
    config = {
        "hidden_size": 4096,
        "num_hidden_layers": 32,
        "num_attention_heads": 32,
        "num_key_value_heads": 32,
        "intermediate_size": 11008,
        "vocab_size": 32000,
        "max_position_embeddings": 2048,
        "model_type": "llama",
        "tie_word_embeddings": False,
        "rms_norm_eps": 1e-6,
    }

    model = DenseModel("test-dense", config)

    # Test parameter calculations
    attention_params = model.compute_attention_params()
    mlp_params = model.compute_mlp_params()
    embedding_params = model.compute_embedding_params()
    total_params = model.get_total_params()

    print(f"  Attention params: {attention_params:,}")
    print(f"  MLP params: {mlp_params:,}")
    print(f"  Embedding params: {embedding_params:,}")
    print(f"  Total params: {total_params:,}")

    # Test FLOPS calculations
    flops = model.compute_flops(sequence_length=2048, batch_size=1)
    total_flops = sum(flops.values())

    print(f"  Total FLOPs: {total_flops:,}")
    print(f"  FLOPS breakdown: {flops}")

    # Basic sanity checks
    assert attention_params > 0, "Attention params should be positive"
    assert mlp_params > 0, "MLP params should be positive"
    assert embedding_params > 0, "Embedding params should be positive"
    assert total_params == attention_params + mlp_params + embedding_params
    assert total_flops > 0, "Total FLOPs should be positive"

    print("  ✓ Dense model tests passed!")
    return True


def test_moe_model():
    """Test MoE model parameter and FLOPS calculations."""
    print("Testing MoE Model...")

    # Create a simple MoE model configuration
    config = {
        "hidden_size": 4096,
        "num_hidden_layers": 32,
        "num_attention_heads": 32,
        "num_key_value_heads": 32,
        "intermediate_size": 11008,
        "vocab_size": 32000,
        "max_position_embeddings": 2048,
        "model_type": "deepseek",
        "tie_word_embeddings": False,
        "rms_norm_eps": 1e-6,
        # MoE-specific parameters
        "n_shared_experts": 2,
        "n_routed_experts": 8,
        "num_experts_per_tok": 2,
        "moe_intermediate_size": 1408,
        "moe_layer_freq": 1,
        "first_k_dense_replace": 0,
        "routed_scaling_factor": 1.0,
        "ep_size": 1,
        "topk_method": "greedy",
        "scoring_func": "softmax",
        "aux_loss_alpha": 0.001,
        # Standard attention (not MLA)
        "kv_lora_rank": None,
        "qk_rope_head_dim": 64,
        "v_head_dim": 128,
        "qk_nope_head_dim": 128,
    }

    model = MoEModel("test-moe", config)

    # Test parameter calculations
    attention_params = model.compute_attention_params()
    mlp_params = model.compute_mlp_params()
    embedding_params = model.compute_embedding_params()
    total_params = model.get_total_params()
    active_params = model.compute_active_params_per_token()

    print(f"  Attention params: {attention_params:,}")
    print(f"  MLP params: {mlp_params:,}")
    print(f"  Embedding params: {embedding_params:,}")
    print(f"  Total params: {total_params:,}")
    print(f"  Active params per token: {active_params:,}")

    # Test FLOPS calculations
    flops = model.compute_flops(sequence_length=2048, batch_size=1)
    total_flops = sum(flops.values())

    print(f"  Total FLOPs: {total_flops:,}")
    print(f"  FLOPS breakdown: {flops}")

    # Basic sanity checks
    assert attention_params > 0, "Attention params should be positive"
    assert mlp_params > 0, "MLP params should be positive"
    assert embedding_params > 0, "Embedding params should be positive"
    assert total_params == attention_params + mlp_params + embedding_params
    assert active_params < total_params, "Active params should be less than total"
    assert total_flops > 0, "Total FLOPs should be positive"

    print("  ✓ MoE model tests passed!")
    return True


def test_mla_attention():
    """Test MoE model with MLA attention."""
    print("Testing MoE Model with MLA Attention...")

    # Create MoE model configuration with MLA
    config = {
        "hidden_size": 4096,
        "num_hidden_layers": 32,
        "num_attention_heads": 32,
        "num_key_value_heads": 32,
        "intermediate_size": 11008,
        "vocab_size": 32000,
        "max_position_embeddings": 2048,
        "model_type": "deepseek",
        "tie_word_embeddings": False,
        "rms_norm_eps": 1e-6,
        # MoE-specific parameters
        "n_shared_experts": 2,
        "n_routed_experts": 8,
        "num_experts_per_tok": 2,
        "moe_intermediate_size": 1408,
        "moe_layer_freq": 1,
        "first_k_dense_replace": 0,
        # MLA-specific parameters
        "kv_lora_rank": 512,
        "q_lora_rank": 1536,
        "qk_rope_head_dim": 64,
        "qk_nope_head_dim": 128,
        "v_head_dim": 128,
    }

    model = MoEModel("test-moe-mla", config)

    # Test parameter calculations
    attention_params = model.compute_attention_params()
    mlp_params = model.compute_mlp_params()
    total_params = model.get_total_params()

    print(f"  MLA Attention params: {attention_params:,}")
    print(f"  MLP params: {mlp_params:,}")
    print(f"  Total params: {total_params:,}")

    # Test FLOPS calculations
    flops = model.compute_flops(sequence_length=2048, batch_size=1)
    total_flops = sum(flops.values())

    print(f"  Total FLOPs: {total_flops:,}")

    # Basic sanity checks
    assert attention_params > 0, "MLA attention params should be positive"
    assert total_flops > 0, "Total FLOPs should be positive"

    print("  ✓ MLA attention tests passed!")
    return True


def main():
    """Run all tests."""
    print("Running refactoring tests...\n")

    try:
        test_dense_model()
        print()
        test_moe_model()
        print()
        test_mla_attention()
        print()
        print("🎉 All tests passed! Refactoring is working correctly.")
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

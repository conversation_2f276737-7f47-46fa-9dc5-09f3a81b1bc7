"""
Tests for the operator-based modeling system.
"""

import os
import sys
import unittest

sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from llm_modeling_metrics.core.base_model import ParallelConfig
from llm_modeling_metrics.core.operators import (
    AttentionOperator,
    CommunicationOperator,
    FFNLayer,
    HardwareSpecs,
    LayerNormOperator,
    MatMulOperator,
    MoELayer,
    OperatorMetrics,
)
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel


class TestOperators(unittest.TestCase):
    """Test individual operators."""

    def setUp(self):
        """Set up test fixtures."""
        self.hardware = HardwareSpecs(
            name="Test GPU",
            peak_flops={"fp16": 100.0, "bf16": 100.0, "fp32": 50.0},
            memory_bandwidth_gbps=1000.0,
            memory_size_gb=40,
        )

        self.batch_size = 1
        self.sequence_length = 2048

    def test_attention_operator(self):
        """Test attention operator."""
        op = AttentionOperator(hidden_size=4096, num_heads=32, num_kv_heads=32)

        # Set shape before computing metrics
        op.set_shape(self.batch_size, self.sequence_length, kv_lens=2048)

        flops = op.compute_flops()
        memory = op.compute_memory_capacity_bytes()
        bandwidth = op.compute_memory_movement_bytes()

        self.assertGreater(flops, 0)
        self.assertGreater(memory, 0)
        self.assertGreater(bandwidth, 0)

        # Test metrics computation
        metrics = op.compute_metrics(self.hardware)
        self.assertIsInstance(metrics, OperatorMetrics)
        self.assertEqual(metrics.flops, flops)
        self.assertEqual(metrics.memory_capacity_bytes, memory)

    def test_mlp_operator(self):
        """Test MLP operator."""
        op = FFNLayer(hidden_size=4096, intermediate_size=11008)

        # Set shape before computing metrics
        op.set_shape(self.batch_size, self.sequence_length)

        flops = op.compute_flops()
        memory = op.compute_memory_capacity_bytes()

        self.assertGreater(flops, 0)
        self.assertGreater(memory, 0)

    def test_matmul_operator(self):
        """Test matrix multiplication operator."""
        op = MatMulOperator(input_dim=4096, output_dim=11008)

        # Set shape before computing metrics
        op.set_shape(self.batch_size, self.sequence_length)

        flops = op.compute_flops()
        expected_flops = self.batch_size * self.sequence_length * 4096 * 11008 * 2

        self.assertEqual(flops, expected_flops)

    def test_layernorm_operator(self):
        """Test layer normalization operator."""
        op = LayerNormOperator(hidden_size=4096)

        # Set shape before computing metrics
        op.set_shape(self.batch_size, self.sequence_length)

        flops = op.compute_flops()
        expected_flops = (
            self.batch_size * self.sequence_length * 4096 * 5
        )  # 5 FLOPs per element

        self.assertEqual(flops, expected_flops)

    def test_moe_operator(self):
        """Test MoE operator."""
        op = MoELayer(
            hidden_size=4096,
            intermediate_size=1407,
            num_experts=64,
            experts_per_token=6,
        )

        # Set shape before computing metrics
        op.set_shape(self.batch_size, self.sequence_length)

        flops = op.compute_flops()
        memory = op.compute_memory_capacity_bytes()

        self.assertGreater(flops, 0)
        self.assertGreater(memory, 0)

    def test_communication_operator(self):
        """Test communication operator."""
        data_size = 1024 * 1024  # 1MB
        op = CommunicationOperator(
            operation_type="AllReduce",
            data_size_bytes=data_size,
            num_devices=4,
            bandwidth_gbps=100.0,
        )

        flops = op.compute_flops()
        memory = op.compute_memory_capacity_bytes()
        comm_time = op.compute_communication_time_ms()

        self.assertEqual(flops, 0)  # Communication has no FLOPs
        self.assertGreater(memory, 0)
        self.assertGreater(comm_time, 0)


class TestModelOperatorIntegration(unittest.TestCase):
    """Test operator integration with models."""

    def setUp(self):
        """Set up test models."""
        self.dense_config = {
            "hidden_size": 1024,
            "num_hidden_layers": 12,
            "num_attention_heads": 16,
            "num_key_value_heads": 16,
            "intermediate_size": 4096,
            "vocab_size": 32000,
            "max_position_embeddings": 2048,
            "model_type": "llama",
            "tie_word_embeddings": False,
            "rms_norm_eps": 1e-6,
        }

        self.moe_config = {
            "hidden_size": 1024,
            "num_hidden_layers": 12,
            "num_attention_heads": 16,
            "num_key_value_heads": 16,
            "intermediate_size": 4096,
            "moe_intermediate_size": 1024,
            "vocab_size": 32000,
            "max_position_embeddings": 2048,
            "model_type": "deepseek",
            "tie_word_embeddings": False,
            "rms_norm_eps": 1e-6,
            "n_shared_experts": 2,
            "n_routed_experts": 8,
            "num_experts_per_tok": 2,
            "moe_layer_freq": 1,
            "first_k_dense_replace": 0,
        }

    def test_dense_model_operator_breakdown(self):
        """Test dense model operator breakdown."""
        model = DenseModel("test-dense", self.dense_config)

        # Test operator breakdown
        breakdown = model.get_operator_breakdown(batch_size=1, sequence_length=512)

        self.assertIn("operators", breakdown)
        self.assertIn("totals", breakdown)
        self.assertIn("hardware", breakdown)

        # Check that we have expected operators
        operators = breakdown["operators"]
        self.assertIn("attention", operators)
        self.assertIn("mlp", operators)
        self.assertIn("layernorm", operators)
        self.assertIn("embeddings", operators)

        # Check totals
        totals = breakdown["totals"]
        self.assertGreater(totals["flops"], 0)
        self.assertGreater(totals["memory_capacity_bytes"], 0)
        self.assertGreater(totals["execution_time_ms"], 0)

    def test_dense_model_roofline_analysis(self):
        """Test dense model roofline analysis."""
        model = DenseModel("test-dense", self.dense_config)

        roofline = model.get_roofline_analysis(batch_size=1, sequence_length=512)

        self.assertIn("operators", roofline)
        self.assertIn("model_point", roofline)
        self.assertIn("hardware", roofline)

        # Check model point
        model_point = roofline["model_point"]
        self.assertIn("arithmetic_intensity", model_point)
        self.assertIn("achieved_gflops", model_point)
        self.assertIn("bottleneck", model_point)

    def test_moe_model_operator_breakdown(self):
        """Test MoE model operator breakdown."""
        model = MoEModel("test-moe", self.moe_config)

        # Test without parallel config
        breakdown = model.get_operator_breakdown(batch_size=1, sequence_length=512)

        self.assertIn("operators", breakdown)
        operators = breakdown["operators"]
        self.assertIn("attention", operators)
        self.assertIn("moe", operators)

        # Check MoE details
        if "moe" in operators:
            moe_data = operators["moe"]
            self.assertIn("moe_details", moe_data)
            moe_details = moe_data["moe_details"]
            self.assertIn("expert_utilization_rate", moe_details)

    def test_moe_model_with_parallel_config(self):
        """Test MoE model with parallel configuration."""
        model = MoEModel("test-moe", self.moe_config)

        parallel_config = ParallelConfig(tensor_parallel_size=2, expert_parallel_size=2)

        breakdown = model.get_operator_breakdown(
            batch_size=1, sequence_length=512, parallel_config=parallel_config
        )

        self.assertIn("operators", breakdown)

        # Should have communication operators
        operators = breakdown["operators"]
        comm_ops = [op for op in operators.keys() if "comm" in op.lower()]
        self.assertGreater(len(comm_ops), 0)


class TestHardwareSpecs(unittest.TestCase):
    """Test hardware specifications."""

    def test_hardware_specs_creation(self):
        """Test hardware specs creation."""
        specs = HardwareSpecs(
            name="Test GPU",
            peak_flops={"fp16": 100.0},
            memory_bandwidth_gbps=1000.0,
            memory_size_gb=40,
        )

        self.assertEqual(specs.name, "Test GPU")
        self.assertEqual(specs.peak_flops["fp16"], 100.0)
        self.assertEqual(specs.memory_bandwidth_gbps, 1000.0)

    def test_hardware_specs_from_file(self):
        """Test loading hardware specs from file."""
        # This test will use fallback if file not found
        try:
            specs = HardwareSpecs.from_gpu_spec("nvidia_h100_sxm5")
            self.assertIn("H100", specs.name)
            self.assertGreater(specs.memory_bandwidth_gbps, 1000)
        except FileNotFoundError:
            # Expected if GPU specs file not available
            self.skipTest("GPU specs file not available")


if __name__ == "__main__":
    unittest.main()

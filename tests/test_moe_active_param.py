"""
Test suite for MoE (Mixture of Experts) model active parameter calculations.

This module tests the active parameter computation for various MoE models
to ensure they return the expected values.
"""

import pytest

from llm_modeling_metrics import DenseModel, ModelFactory, MoEModel


class TestMoEActiveParams:
    """Test class for MoE model active parameter calculations."""

    def setup_method(self):
        """Set up each test method by ensuring models are registered.

        Note: This explicit registration is needed because when running tests,
        the automatic model registration in __init__.py might not be triggered
        if ModelFactory is imported directly from its module.
        """
        ModelFactory.register_model("dense", DenseModel)
        ModelFactory.register_model("moe", MoEModel)

    def test_deepseek_v3_active_params(self):
        """Test DeepSeek-V3 active parameters per token."""
        model = ModelFactory.create_model("deepseek-ai/DeepSeek-V3")
        active_params = model.compute_active_params_per_token()

        expected_params_b = 37  # Expected in billions
        actual_params_b = active_params // 1e9

        assert (
            actual_params_b == expected_params_b
        ), f"DeepSeek-V3 active params per token should be {expected_params_b}B but got {actual_params_b:.1f}B"

    def test_kimi_k2_active_params(self):
        """Test Kimi K2 Instruct active parameters per token."""
        model = ModelFactory.create_model("moonshotai/Kimi-K2-Instruct")
        active_params = model.compute_active_params_per_token()

        expected_params_b = 32  # Expected in billions
        actual_params_b = active_params // 1e9

        assert (
            actual_params_b == expected_params_b
        ), f"K2 active params per token should be {expected_params_b}B but got {actual_params_b:.1f}B"

    def test_qwen3_active_params(self):
        """Test Qwen3-235B-A22B active parameters per token."""
        model = ModelFactory.create_model("Qwen/Qwen3-235B-A22B")
        active_params = model.compute_active_params_per_token()

        expected_params_b = 22  # Expected in billions
        actual_params_b = active_params / 1e9

        assert (
            actual_params_b // 1 == expected_params_b
        ), f"Qwen active params per token should be {expected_params_b}B but got {actual_params_b:.1f}B"

    # def test_step3_active_params(self):
    #     """Test Step3 active parameters per token (including vision part)."""
    #     model = ModelFactory.create_model("stepfun-ai/step3")
    #     active_params = model.compute_active_params_per_token()

    #     expected_params_b = 38  # Expected in billions (should include vision part)
    #     actual_params_b = active_params / 1e9

    #     assert actual_params_b // 1 == expected_params_b, \
    #         f"Step3 active params per token should be {expected_params_b}B but got {actual_params_b:.1f}B"


if __name__ == "__main__":
    pytest.main([__file__])

"""
Unit tests for ModelFactory functionality.
"""

from unittest.mock import Mock, patch

import pytest

from llm_modeling_metrics.core.base_model import BaseModel
from llm_modeling_metrics.core.config_manager import ConfigManager
from llm_modeling_metrics.core.model_factory import (
    ConfigurationError,
    ModelFactory,
    ModelNotSupportedError,
)
from tests.conftest import MockConfig, create_mock_model_config


class MockModel(BaseModel):
    """Mock model for testing ModelFactory."""

    def _parse_config(self):
        self._parsed_config = {"test": True}

    def compute_attention_params(self) -> int:
        return 1000000

    def compute_mlp_params(self) -> int:
        return 2000000

    def compute_embedding_params(self) -> int:
        return 500000

    def compute_flops(self, sequence_length: int = 2048, batch_size: int = 1) -> dict:
        return {"total": 1000000000}

    def compute_memory_requirements(
        self, sequence_length: int = 2048, batch_size: int = 1
    ) -> dict:
        return {"parameters": 14000000, "activations": 1000000, "total": 15000000}

    def get_matrix_shapes(self, parallel_config=None) -> dict:
        return {"attention": {"q_proj": (1024, 1024)}, "mlp": {"up_proj": (1024, 2048)}}


class InvalidModel:
    """Invalid model class that doesn't inherit from BaseModel."""

    pass


class TestModelFactory:
    """Test ModelFactory functionality."""

    def test_register_model_valid(self):
        """Test registering a valid model class."""
        ModelFactory.register_model("test", MockModel)

        assert ModelFactory.is_architecture_supported("test")
        assert ModelFactory.get_model_class("test") == MockModel
        assert "test" in ModelFactory.get_supported_architectures()

    def test_register_model_invalid(self):
        """Test registering an invalid model class."""
        with pytest.raises(TypeError, match="must be a subclass of BaseModel"):
            ModelFactory.register_model("invalid", InvalidModel)

    def test_register_model_case_insensitive(self):
        """Test that model registration is case insensitive."""
        ModelFactory.register_model("TEST", MockModel)

        assert ModelFactory.is_architecture_supported("test")
        assert ModelFactory.is_architecture_supported("TEST")
        assert ModelFactory.is_architecture_supported("Test")

    def test_unregister_model(self):
        """Test unregistering a model architecture."""
        ModelFactory.register_model("test", MockModel)
        assert ModelFactory.is_architecture_supported("test")

        result = ModelFactory.unregister_model("test")
        assert result is True
        assert not ModelFactory.is_architecture_supported("test")

        # Try to unregister non-existent model
        result = ModelFactory.unregister_model("nonexistent")
        assert result is False

    def test_clear_registry(self):
        """Test clearing the model registry."""
        ModelFactory.register_model("test1", MockModel)
        ModelFactory.register_model("test2", MockModel)

        assert len(ModelFactory.get_supported_architectures()) == 2

        ModelFactory.clear_registry()
        assert len(ModelFactory.get_supported_architectures()) == 0

    def test_get_supported_architectures(self):
        """Test getting list of supported architectures."""
        ModelFactory.register_model("llama", MockModel)
        ModelFactory.register_model("deepseek", MockModel)

        architectures = ModelFactory.get_supported_architectures()
        assert "llama" in architectures
        assert "deepseek" in architectures
        assert len(architectures) == 2

    def test_get_model_class(self):
        """Test getting model class for architecture."""
        ModelFactory.register_model("test", MockModel)

        model_class = ModelFactory.get_model_class("test")
        assert model_class == MockModel

        # Test non-existent architecture
        model_class = ModelFactory.get_model_class("nonexistent")
        assert model_class is None

    @patch("llm_modeling_metrics.core.model_factory.ConfigManager")
    def test_create_model_with_config(self, mock_config_manager_class):
        """Test creating model with provided config."""
        ModelFactory.register_model("test", MockModel)

        config = MockConfig({"model_type": "test"})
        model = ModelFactory.create_model("test-model", config)

        assert isinstance(model, MockModel)
        assert model.model_name == "test-model"
        assert model.config == config

        # ConfigManager should not be called when config is provided
        mock_config_manager_class.assert_not_called()

    @patch("llm_modeling_metrics.core.model_factory.ConfigManager")
    def test_create_model_without_config(self, mock_config_manager_class):
        """Test creating model without config (should fetch it)."""
        ModelFactory.register_model("test", MockModel)

        # Setup mock config manager
        mock_config_manager = Mock()
        mock_config_manager.fetch_config.return_value = {"model_type": "test"}
        mock_config_manager_class.return_value = mock_config_manager

        model = ModelFactory.create_model("test-model")

        assert isinstance(model, MockModel)
        assert model.model_name == "test-model"
        mock_config_manager.fetch_config.assert_called_once_with("test-model")

    def test_create_model_unsupported_architecture(self):
        """Test creating model with unsupported architecture."""
        config = MockConfig({"model_type": "unsupported"})

        with pytest.raises(
            ModelNotSupportedError, match="Architecture 'unsupported' is not supported"
        ):
            ModelFactory.create_model("test-model", config)

    def test_set_config_manager(self):
        """Test setting custom config manager."""
        mock_config_manager = Mock(spec=ConfigManager)
        ModelFactory.set_config_manager(mock_config_manager)

        assert ModelFactory._config_manager == mock_config_manager


class TestArchitectureDetection:
    """Test architecture detection logic."""

    def test_detect_architecture_from_model_type(self):
        """Test architecture detection from model_type field."""
        test_cases = [
            ({"model_type": "llama"}, "llama"),
            ({"model_type": "LlamaForCausalLM"}, "llama"),
            ({"model_type": "deepseek"}, "deepseek"),
            ({"model_type": "deepseek_v2"}, "deepseek"),
            ({"model_type": "deepseek_v3"}, "deepseek"),
            ({"model_type": "qwen"}, "qwen"),
            ({"model_type": "qwen2"}, "qwen"),
            ({"model_type": "mistral"}, "mistral"),
            ({"model_type": "mixtral"}, "mixtral"),
        ]

        for config_dict, expected_arch in test_cases:
            arch = ModelFactory._detect_architecture("test-model", config_dict)
            assert arch == expected_arch

    def test_detect_architecture_from_architectures_field(self):
        """Test architecture detection from architectures field."""
        test_cases = [
            ({"architectures": ["LlamaForCausalLM"]}, "llama"),
            ({"architectures": ["DeepseekV3ForCausalLM"]}, "deepseek"),
            ({"architectures": ["QwenForCausalLM"]}, "qwen"),
            ({"architectures": ["MistralForCausalLM"]}, "mistral"),
        ]

        for config_dict, expected_arch in test_cases:
            arch = ModelFactory._detect_architecture("test-model", config_dict)
            assert arch == expected_arch

    def test_detect_architecture_from_model_name(self):
        """Test architecture detection from model name."""
        test_cases = [
            ("meta-llama/Llama-2-7b-hf", {}, "llama"),
            ("deepseek-ai/DeepSeek-R1", {}, "deepseek"),
            ("Qwen/Qwen2-7B", {}, "qwen"),
            ("mistralai/Mistral-7B-v0.1", {}, "mistral"),
            ("mistralai/Mixtral-8x7B-v0.1", {}, "mixtral"),
        ]

        for model_name, config_dict, expected_arch in test_cases:
            arch = ModelFactory._detect_architecture(model_name, config_dict)
            assert arch == expected_arch

    def test_detect_architecture_moe_fallback(self):
        """Test MoE architecture detection fallback."""
        moe_configs = [
            {"num_experts": 8},
            {"n_routed_experts": 64},
            {"experts_per_token": 2},
        ]

        for config_dict in moe_configs:
            arch = ModelFactory._detect_architecture("unknown-model", config_dict)
            assert arch == "moe"

    def test_detect_architecture_unknown(self):
        """Test architecture detection failure."""
        with pytest.raises(
            ModelNotSupportedError, match="Cannot determine architecture"
        ):
            ModelFactory._detect_architecture(
                "unknown-model", {"unknown_field": "value"}
            )

    def test_detect_architecture_priority(self):
        """Test that model_type takes priority over other fields."""
        config = {
            "model_type": "llama",
            "architectures": ["DeepseekV3ForCausalLM"],  # Should be ignored
        }

        arch = ModelFactory._detect_architecture("deepseek-model", config)
        assert arch == "llama"  # model_type should take priority


class TestModelFactoryIntegration:
    """Integration tests for ModelFactory."""

    def test_full_workflow_with_real_models(self):
        """Test complete workflow with realistic model configurations."""
        # Register mock models for different architectures
        ModelFactory.register_model("llama", MockModel)
        ModelFactory.register_model("deepseek", MockModel)

        # Test Llama model creation
        llama_config = create_mock_model_config("llama")
        llama_model = ModelFactory.create_model(
            "meta-llama/Llama-2-7b-hf", llama_config
        )

        assert isinstance(llama_model, MockModel)
        assert llama_model.model_name == "meta-llama/Llama-2-7b-hf"

        # Test DeepSeek model creation
        deepseek_config = create_mock_model_config("deepseek_v3")
        deepseek_model = ModelFactory.create_model(
            "deepseek-ai/DeepSeek-R1", deepseek_config
        )

        assert isinstance(deepseek_model, MockModel)
        assert deepseek_model.model_name == "deepseek-ai/DeepSeek-R1"

    def test_error_handling_chain(self):
        """Test error handling throughout the creation chain."""
        # Test with no registered models
        config = create_mock_model_config("llama")

        with pytest.raises(ModelNotSupportedError):
            ModelFactory.create_model("test-model", config)

        # Register model and test with invalid config
        ModelFactory.register_model("llama", MockModel)

        invalid_config = {"invalid": "config"}
        with pytest.raises(ModelNotSupportedError):
            ModelFactory.create_model("unknown-model", invalid_config)

    @patch("llm_modeling_metrics.core.model_factory.ConfigManager")
    def test_config_manager_integration(self, mock_config_manager_class):
        """Test integration with ConfigManager."""
        ModelFactory.register_model("llama", MockModel)

        # Setup mock config manager
        mock_config_manager = Mock()
        mock_config_manager.fetch_config.return_value = create_mock_model_config(
            "llama"
        )
        mock_config_manager_class.return_value = mock_config_manager

        # Test without setting config manager (should create default)
        model = ModelFactory.create_model("meta-llama/Llama-2-7b-hf")
        assert isinstance(model, MockModel)
        mock_config_manager_class.assert_called_once()

        # Test with custom config manager
        custom_config_manager = Mock()
        custom_config_manager.fetch_config.return_value = create_mock_model_config(
            "llama"
        )
        ModelFactory.set_config_manager(custom_config_manager)

        model2 = ModelFactory.create_model("meta-llama/Llama-2-13b-hf")
        assert isinstance(model2, MockModel)
        custom_config_manager.fetch_config.assert_called_once_with(
            "meta-llama/Llama-2-13b-hf"
        )

    def test_case_sensitivity_throughout(self):
        """Test case sensitivity handling throughout the factory."""
        ModelFactory.register_model("LLAMA", MockModel)

        # All these should work
        assert ModelFactory.is_architecture_supported("llama")
        assert ModelFactory.is_architecture_supported("LLAMA")
        assert ModelFactory.is_architecture_supported("Llama")

        # Model creation should work with different cases
        config = create_mock_model_config("llama")
        config["model_type"] = "LLAMA"

        model = ModelFactory.create_model("test-model", config)
        assert isinstance(model, MockModel)

    def test_multiple_registrations_same_architecture(self):
        """Test that later registrations override earlier ones."""

        class MockModel1(MockModel):
            pass

        class MockModel2(MockModel):
            pass

        ModelFactory.register_model("test", MockModel1)
        assert ModelFactory.get_model_class("test") == MockModel1

        ModelFactory.register_model("test", MockModel2)
        assert ModelFactory.get_model_class("test") == MockModel2

    def test_registry_isolation(self):
        """Test that registry changes don't affect existing instances."""
        ModelFactory.register_model("test", MockModel)

        # Create model
        config = MockConfig({"model_type": "test"})
        model = ModelFactory.create_model("test-model", config)

        # Clear registry
        ModelFactory.clear_registry()

        # Existing model should still work
        assert isinstance(model, MockModel)
        assert model.model_name == "test-model"

        # But new models can't be created
        with pytest.raises(ModelNotSupportedError):
            ModelFactory.create_model("test-model2", config)

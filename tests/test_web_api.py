"""
Tests for web API endpoints and functionality.
"""

import json
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from fastapi.testclient import TestClient

from llm_modeling_metrics.core.base_model import ParallelConfig
from llm_modeling_metrics.core.model_factory import ModelFactory
from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel
from llm_modeling_metrics.web.app import app
from tests.conftest import MockConfig, create_mock_model_config


class TestWebAPIEndpoints:
    """Test web API endpoints."""

    def setup_method(self):
        """Set up test environment."""
        self.client = TestClient(app)

        # Register models
        ModelFactory.register_model("llama", DenseModel)
        ModelFactory.register_model("deepseek", MoEModel)

        # Mock config manager
        self.mock_config_manager = Mock()
        ModelFactory.set_config_manager(self.mock_config_manager)

    def test_health_check_endpoint(self):
        """Test health check endpoint."""
        response = self.client.get("/health")
        assert response.status_code == 200

        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data

    def test_supported_models_endpoint(self):
        """Test supported models endpoint."""
        response = self.client.get("/api/models/supported")
        assert response.status_code == 200

        data = response.json()
        assert "architectures" in data
        assert isinstance(data["architectures"], list)
        assert "llama" in data["architectures"]
        assert "deepseek" in data["architectures"]

    def test_analyze_single_model_endpoint(self):
        """Test single model analysis endpoint."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Request payload
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "results" in data
        assert "execution_time" in data
        assert "timestamp" in data

        # Check results structure
        results = data["results"]
        assert len(results) == 1
        assert "meta-llama/Llama-2-7b-hf" in results

        model_result = results["meta-llama/Llama-2-7b-hf"]
        assert "model_name" in model_result
        assert "architecture" in model_result
        assert "total_params" in model_result
        assert "flops_forward" in model_result
        assert "memory_total" in model_result

    def test_analyze_multiple_models_endpoint(self):
        """Test multiple model analysis endpoint."""

        # Mock configs
        def mock_fetch_config(model_name):
            if "llama" in model_name.lower():
                return create_mock_model_config("llama")
            elif "deepseek" in model_name.lower():
                return create_mock_model_config("deepseek_v3")
            else:
                raise ValueError(f"Unknown model: {model_name}")

        self.mock_config_manager.fetch_config.side_effect = mock_fetch_config

        # Request payload
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf", "deepseek-ai/DeepSeek-R1"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "results" in data
        assert "comparison" in data

        # Check results
        results = data["results"]
        assert len(results) == 2
        assert "meta-llama/Llama-2-7b-hf" in results
        assert "deepseek-ai/DeepSeek-R1" in results

        # Check comparison
        comparison = data["comparison"]
        assert "models" in comparison
        assert "metrics" in comparison
        assert len(comparison["models"]) == 2

    def test_analyze_with_parallel_config_endpoint(self):
        """Test analysis with parallel configuration."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Request payload with parallel config
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "parallel_config": {
                "tensor_parallel_size": 2,
                "pipeline_parallel_size": 1,
                "data_parallel_size": 1,
            },
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 200

        data = response.json()
        results = data["results"]
        model_result = results["meta-llama/Llama-2-7b-hf"]

        # Should have parallel config in result
        assert "parallel_config" in model_result
        parallel_config = model_result["parallel_config"]
        assert parallel_config["tensor_parallel_size"] == 2

    def test_analyze_endpoint_validation_errors(self):
        """Test validation errors in analyze endpoint."""
        # Empty model names
        payload = {"model_names": [], "sequence_length": 2048, "batch_size": 1}

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 422  # Validation error

        # Invalid sequence length
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 0,
            "batch_size": 1,
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 422

        # Invalid batch size
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 0,
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 422

    def test_analyze_endpoint_model_not_found(self):
        """Test analyze endpoint with non-existent model."""
        # Mock config manager to raise error
        self.mock_config_manager.fetch_config.side_effect = Exception("Model not found")

        payload = {
            "model_names": ["non-existent/model"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 400  # Bad request

        data = response.json()
        assert "error" in data
        assert "Model not found" in data["error"]

    def test_compare_models_endpoint(self):
        """Test model comparison endpoint."""

        # Mock configs
        def mock_fetch_config(model_name):
            if "llama-7b" in model_name.lower():
                return create_mock_model_config("llama", hidden_size=4096)
            elif "llama-13b" in model_name.lower():
                return create_mock_model_config("llama", hidden_size=5120)
            else:
                raise ValueError(f"Unknown model: {model_name}")

        self.mock_config_manager.fetch_config.side_effect = mock_fetch_config

        # Request payload
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf", "meta-llama/Llama-2-13b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "json",
        }

        response = self.client.post("/api/compare", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "comparison" in data
        assert "export_data" in data

        # Check comparison structure
        comparison = data["comparison"]
        assert "models" in comparison
        assert "metrics" in comparison
        assert len(comparison["models"]) == 2

        # Check that 13B model has more parameters
        metrics = comparison["metrics"]
        params = metrics["total_params"]
        assert params[1] > params[0]  # 13B should have more params than 7B

    def test_export_comparison_endpoint(self):
        """Test comparison export endpoint."""
        # Mock configs
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Request payload
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "csv",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 200

        # Should return CSV content
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
        assert "attachment" in response.headers["content-disposition"]

        # Check CSV content
        csv_content = response.content.decode()
        assert "model_name" in csv_content
        assert "total_params" in csv_content
        assert "meta-llama/Llama-2-7b-hf" in csv_content

    def test_export_excel_format(self):
        """Test Excel export format."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Request payload
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "excel",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 200

        # Should return Excel content
        assert (
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            in response.headers["content-type"]
        )
        assert "attachment" in response.headers["content-disposition"]
        assert ".xlsx" in response.headers["content-disposition"]

    def test_parallel_suggestions_endpoint(self):
        """Test parallel configuration suggestions endpoint."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Request payload
        payload = {
            "model_name": "meta-llama/Llama-2-7b-hf",
            "num_devices": 8,
            "memory_per_device_gb": 80.0,
            "bandwidth_gbps": 100.0,
            "latency_us": 10.0,
        }

        response = self.client.post("/api/parallel/suggestions", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "suggestions" in data
        assert "model_info" in data

        # Check suggestions structure
        suggestions = data["suggestions"]
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0

        for suggestion in suggestions:
            assert "config" in suggestion
            assert "memory_analysis" in suggestion
            assert "performance_analysis" in suggestion
            assert "meets_constraints" in suggestion

    def test_parallel_optimization_endpoint(self):
        """Test parallel configuration optimization endpoint."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Request payload
        payload = {
            "model_name": "meta-llama/Llama-2-7b-hf",
            "current_config": {
                "tensor_parallel_size": 2,
                "pipeline_parallel_size": 1,
                "data_parallel_size": 2,
            },
            "constraints": {
                "num_devices": 8,
                "memory_per_device_gb": 80.0,
                "bandwidth_gbps": 100.0,
                "latency_us": 10.0,
            },
        }

        response = self.client.post("/api/parallel/optimize", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "optimization_suggestions" in data
        assert "current_analysis" in data

        # Check optimization suggestions
        suggestions = data["optimization_suggestions"]
        assert "configuration_alternatives" in suggestions
        assert "general_suggestions" in suggestions
        assert "current_performance" in suggestions

    def test_websocket_analysis_progress(self):
        """Test WebSocket for analysis progress tracking."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Test WebSocket connection
        with self.client.websocket_connect("/ws/analysis") as websocket:
            # Send analysis request
            request_data = {
                "model_names": ["meta-llama/Llama-2-7b-hf"],
                "sequence_length": 2048,
                "batch_size": 1,
            }

            websocket.send_json(request_data)

            # Receive progress updates
            messages = []
            try:
                while True:
                    message = websocket.receive_json()
                    messages.append(message)

                    # Break when analysis is complete
                    if message.get("status") == "completed":
                        break
            except:
                pass  # WebSocket closed

            # Should have received progress messages
            assert len(messages) > 0

            # Check final message
            final_message = messages[-1]
            assert final_message["status"] == "completed"
            assert "results" in final_message

    def test_api_rate_limiting(self):
        """Test API rate limiting."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        # Make multiple rapid requests
        responses = []
        for i in range(10):  # Adjust based on rate limit settings
            response = self.client.post("/api/analyze", json=payload)
            responses.append(response)

        # Most requests should succeed
        success_count = sum(1 for r in responses if r.status_code == 200)
        rate_limited_count = sum(1 for r in responses if r.status_code == 429)

        # Should have some successful requests
        assert success_count > 0

        # May have some rate limited requests (depending on implementation)
        # This is optional and depends on rate limiting configuration

    def test_api_error_handling(self):
        """Test API error handling."""
        # Test with invalid JSON
        response = self.client.post("/api/analyze", data="invalid json")
        assert response.status_code == 422

        # Test with missing required fields
        response = self.client.post("/api/analyze", json={})
        assert response.status_code == 422

        # Test with invalid model name format
        payload = {"model_names": [""], "sequence_length": 2048, "batch_size": 1}
        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 422

    def test_api_cors_headers(self):
        """Test CORS headers in API responses."""
        response = self.client.options("/api/analyze")

        # Should have CORS headers
        assert "access-control-allow-origin" in response.headers
        assert "access-control-allow-methods" in response.headers
        assert "access-control-allow-headers" in response.headers

    def test_api_documentation_endpoint(self):
        """Test API documentation endpoint."""
        response = self.client.get("/docs")
        assert response.status_code == 200

        # Should return HTML content
        assert "text/html" in response.headers["content-type"]

        # Should contain OpenAPI documentation
        content = response.content.decode()
        assert "swagger" in content.lower() or "openapi" in content.lower()

    def test_openapi_schema_endpoint(self):
        """Test OpenAPI schema endpoint."""
        response = self.client.get("/openapi.json")
        assert response.status_code == 200

        # Should return JSON
        assert response.headers["content-type"] == "application/json"

        # Should be valid OpenAPI schema
        schema = response.json()
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema

        # Check that our endpoints are documented
        paths = schema["paths"]
        assert "/api/analyze" in paths
        assert "/api/models/supported" in paths
        assert "/api/compare" in paths


class TestWebAPIIntegration:
    """Test web API integration scenarios."""

    def setup_method(self):
        """Set up test environment."""
        self.client = TestClient(app)

        # Register models
        ModelFactory.register_model("llama", DenseModel)
        ModelFactory.register_model("deepseek", MoEModel)

        # Mock config manager
        self.mock_config_manager = Mock()
        ModelFactory.set_config_manager(self.mock_config_manager)

    def test_full_analysis_workflow_via_api(self):
        """Test complete analysis workflow through API."""

        # Mock configs
        def mock_fetch_config(model_name):
            if "llama" in model_name.lower():
                return create_mock_model_config("llama")
            elif "deepseek" in model_name.lower():
                return create_mock_model_config("deepseek_v3")
            else:
                raise ValueError(f"Unknown model: {model_name}")

        self.mock_config_manager.fetch_config.side_effect = mock_fetch_config

        # Step 1: Get supported models
        response = self.client.get("/api/models/supported")
        assert response.status_code == 200
        supported = response.json()
        assert "llama" in supported["architectures"]

        # Step 2: Analyze single model
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 200
        single_result = response.json()

        # Step 3: Compare multiple models
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf", "deepseek-ai/DeepSeek-R1"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        response = self.client.post("/api/compare", json=payload)
        assert response.status_code == 200
        comparison_result = response.json()

        # Step 4: Get parallel suggestions
        payload = {
            "model_name": "meta-llama/Llama-2-7b-hf",
            "num_devices": 8,
            "memory_per_device_gb": 80.0,
        }

        response = self.client.post("/api/parallel/suggestions", json=payload)
        assert response.status_code == 200
        suggestions_result = response.json()

        # Step 5: Export results
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf", "deepseek-ai/DeepSeek-R1"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "json",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 200

        # Verify workflow consistency
        # Single model result should match first model in comparison
        single_model_metrics = single_result["results"]["meta-llama/Llama-2-7b-hf"]
        comparison_metrics = comparison_result["comparison"]["metrics"]

        # Parameters should match
        assert (
            single_model_metrics["total_params"]
            == comparison_metrics["total_params"][0]
        )

        # Suggestions should be valid
        assert len(suggestions_result["suggestions"]) > 0
        for suggestion in suggestions_result["suggestions"]:
            assert suggestion["meets_constraints"] in [True, False]

    def test_concurrent_api_requests(self):
        """Test handling of concurrent API requests."""
        import threading
        import time

        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        # Function to make API request
        def make_request(results, index):
            response = self.client.post("/api/analyze", json=payload)
            results[index] = response

        # Make concurrent requests
        num_requests = 5
        results = [None] * num_requests
        threads = []

        for i in range(num_requests):
            thread = threading.Thread(target=make_request, args=(results, i))
            threads.append(thread)
            thread.start()

        # Wait for all requests to complete
        for thread in threads:
            thread.join()

        # All requests should succeed
        for i, response in enumerate(results):
            assert response is not None, f"Request {i} failed"
            assert (
                response.status_code == 200
            ), f"Request {i} returned {response.status_code}"

            # Results should be consistent
            data = response.json()
            assert "results" in data
            assert "meta-llama/Llama-2-7b-hf" in data["results"]

    def test_api_performance_monitoring(self):
        """Test API performance monitoring."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        # Measure response time
        import time

        start_time = time.time()

        response = self.client.post("/api/analyze", json=payload)

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200

        # Response should include execution time
        data = response.json()
        assert "execution_time" in data
        assert data["execution_time"] > 0

        # API response time should be reasonable (adjust threshold as needed)
        assert response_time < 30.0  # Should complete within 30 seconds

    def test_api_memory_usage(self):
        """Test API memory usage patterns."""
        import os

        import psutil

        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss

        # Make multiple requests
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        for i in range(5):
            response = self.client.post("/api/analyze", json=payload)
            assert response.status_code == 200

        # Check memory usage after requests
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory

        # Memory increase should be reasonable (adjust threshold as needed)
        # This is a rough check - actual values depend on system and implementation
        assert memory_increase < 500 * 1024 * 1024  # Less than 500MB increase

    def test_api_error_recovery(self):
        """Test API error recovery and resilience."""
        # Test recovery from config fetch errors
        self.mock_config_manager.fetch_config.side_effect = Exception("Temporary error")

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        # First request should fail
        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 400

        # Fix the error
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.side_effect = None
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Subsequent request should succeed
        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 200

        # API should be fully functional again
        data = response.json()
        assert "results" in data
        assert "meta-llama/Llama-2-7b-hf" in data["results"]

    def test_api_data_validation_edge_cases(self):
        """Test API data validation with edge cases."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Test with very large sequence length
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 100000,  # Very large
            "batch_size": 1,
        }

        response = self.client.post("/api/analyze", json=payload)
        # Should either succeed or return validation error
        assert response.status_code in [200, 422]

        # Test with very large batch size
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1000,  # Very large
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code in [200, 422]

        # Test with many models
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"] * 10,  # Many models
            "sequence_length": 2048,
            "batch_size": 1,
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code in [200, 422]

        # Test with minimal valid values
        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 1,
            "batch_size": 1,
        }

        response = self.client.post("/api/analyze", json=payload)
        assert response.status_code == 200


class TestWebAPIExportFunctionality:
    """Test web API export functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.client = TestClient(app)

        # Register models
        ModelFactory.register_model("llama", DenseModel)
        ModelFactory.register_model("deepseek", MoEModel)

        # Mock config manager
        self.mock_config_manager = Mock()
        ModelFactory.set_config_manager(self.mock_config_manager)

    def test_export_json_format(self):
        """Test JSON export format."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "json",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

        # Should be valid JSON
        data = response.json()
        assert "models" in data
        assert "metrics" in data
        assert "timestamp" in data

    def test_export_csv_format(self):
        """Test CSV export format."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "csv",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 200
        assert "text/csv" in response.headers["content-type"]

        # Should be valid CSV
        csv_content = response.content.decode()
        lines = csv_content.strip().split("\n")
        assert len(lines) >= 2  # Header + at least one data row

        # Check header
        header = lines[0]
        assert "model_name" in header
        assert "total_params" in header
        assert "flops_forward" in header

    def test_export_excel_format(self):
        """Test Excel export format."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "excel",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 200

        # Should be Excel format
        content_type = response.headers["content-type"]
        assert "spreadsheet" in content_type or "excel" in content_type

        # Should have proper filename
        content_disposition = response.headers["content-disposition"]
        assert "attachment" in content_disposition
        assert ".xlsx" in content_disposition

    def test_export_multiple_models(self):
        """Test export with multiple models."""

        # Mock configs
        def mock_fetch_config(model_name):
            if "llama" in model_name.lower():
                return create_mock_model_config("llama")
            elif "deepseek" in model_name.lower():
                return create_mock_model_config("deepseek_v3")
            else:
                raise ValueError(f"Unknown model: {model_name}")

        self.mock_config_manager.fetch_config.side_effect = mock_fetch_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf", "deepseek-ai/DeepSeek-R1"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "csv",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 200

        # Should have data for both models
        csv_content = response.content.decode()
        assert "meta-llama/Llama-2-7b-hf" in csv_content
        assert "deepseek-ai/DeepSeek-R1" in csv_content

        # Should have at least 3 lines (header + 2 data rows)
        lines = csv_content.strip().split("\n")
        assert len(lines) >= 3

    def test_export_with_parallel_config(self):
        """Test export with parallel configuration."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "parallel_config": {
                "tensor_parallel_size": 2,
                "pipeline_parallel_size": 1,
                "data_parallel_size": 1,
            },
            "export_format": "json",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 200

        data = response.json()

        # Should include parallel config information
        assert "parallel_configs" in data
        parallel_configs = data["parallel_configs"]
        assert len(parallel_configs) == 1
        assert parallel_configs[0]["tensor_parallel_size"] == 2

    def test_export_invalid_format(self):
        """Test export with invalid format."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
            "export_format": "invalid_format",
        }

        response = self.client.post("/api/export", json=payload)
        assert response.status_code == 422  # Validation error

    def test_export_data_integrity(self):
        """Test data integrity in exports."""
        # Mock config
        llama_config = create_mock_model_config("llama")
        self.mock_config_manager.fetch_config.return_value = llama_config

        # Get analysis results directly
        analyze_payload = {
            "model_names": ["meta-llama/Llama-2-7b-hf"],
            "sequence_length": 2048,
            "batch_size": 1,
        }

        analyze_response = self.client.post("/api/analyze", json=analyze_payload)
        assert analyze_response.status_code == 200
        analyze_data = analyze_response.json()

        # Export the same data
        export_payload = {**analyze_payload, "export_format": "json"}

        export_response = self.client.post("/api/export", json=export_payload)
        assert export_response.status_code == 200
        export_data = export_response.json()

        # Data should be consistent
        analyze_result = analyze_data["results"]["meta-llama/Llama-2-7b-hf"]
        export_metrics = export_data["metrics"]

        # Key metrics should match
        assert analyze_result["total_params"] == export_metrics["total_params"][0]
        assert analyze_result["flops_forward"] == export_metrics["flops_forward"][0]
        assert analyze_result["memory_total"] == export_metrics["memory_total"][0]

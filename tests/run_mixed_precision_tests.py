"""
Test runner for mixed precision test suite.

This script runs all mixed precision tests and provides a summary.
"""

import subprocess
import sys
import time
from pathlib import Path


def run_test_file(test_file: str, description: str) -> tuple[bool, float, str]:
    """Run a test file and return success status, duration, and output."""
    print(f"\n{'='*60}")
    print(f"Running {description}")
    print(f"File: {test_file}")
    print(f"{'='*60}")

    start_time = time.time()

    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", test_file, "-v"],
            capture_output=True,
            text=True,
            timeout=300,  # 5 minute timeout
        )

        duration = time.time() - start_time

        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)

        success = result.returncode == 0
        return success, duration, result.stdout

    except subprocess.TimeoutExpired:
        duration = time.time() - start_time
        print(f"TEST TIMEOUT after {duration:.1f}s")
        return False, duration, "Test timed out"

    except Exception as e:
        duration = time.time() - start_time
        print(f"TEST ERROR: {e}")
        return False, duration, str(e)


def main():
    """Run all mixed precision tests."""
    print("Mixed Precision Test Suite Runner")
    print("=" * 60)

    # Define test files and descriptions
    test_files = [
        (
            "tests/test_mixed_precision_comprehensive.py",
            "Comprehensive Mixed Precision Tests",
        ),
        (
            "tests/test_mixed_precision_backward_compatibility.py",
            "Backward Compatibility Tests",
        ),
        ("tests/test_mixed_precision_api.py", "API Mixed Precision Tests"),
        ("tests/test_mixed_precision_performance.py", "Performance Tests"),
    ]

    # Check that test files exist
    missing_files = []
    for test_file, _ in test_files:
        if not Path(test_file).exists():
            missing_files.append(test_file)

    if missing_files:
        print(f"ERROR: Missing test files: {missing_files}")
        return 1

    # Run tests
    results = []
    total_start_time = time.time()

    for test_file, description in test_files:
        success, duration, output = run_test_file(test_file, description)
        results.append((test_file, description, success, duration, output))

    total_duration = time.time() - total_start_time

    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")

    passed = 0
    failed = 0

    for test_file, description, success, duration, output in results:
        status = "PASS" if success else "FAIL"
        print(f"{status:<6} {description:<40} ({duration:.1f}s)")

        if success:
            passed += 1
        else:
            failed += 1

    print(f"\nTotal: {passed + failed} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total time: {total_duration:.1f}s")

    if failed > 0:
        print(f"\nFAILED TESTS:")
        for test_file, description, success, duration, output in results:
            if not success:
                print(f"  - {description} ({test_file})")
        return 1
    else:
        print(f"\nAll tests passed! 🎉")
        return 0


if __name__ == "__main__":
    sys.exit(main())

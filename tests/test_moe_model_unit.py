"""
Unit tests for MoEModel implementation.
"""

from unittest.mock import Mock, patch

import pytest

from llm_modeling_metrics.core.base_model import ModelMetrics, ParallelConfig
from llm_modeling_metrics.models.moe_model import MoEModel
from tests.conftest import (
    MockConfig,
    assert_metrics_valid,
    assert_parallel_config_valid,
    assert_shapes_valid,
    create_mock_model_config,
)


class TestMoEModelInitialization:
    """Test MoEModel initialization and configuration parsing."""

    def test_initialization_with_valid_config(self):
        """Test MoEModel initialization with valid configuration."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("deepseek-ai/DeepSeek-R1", config)

        assert model.model_name == "deepseek-ai/DeepSeek-R1"
        assert model.config == config
        assert model._parsed_config["hidden_size"] == 7168
        assert model._parsed_config["n_routed_experts"] == 256
        assert model._parsed_config["num_experts_per_tok"] == 8
        assert model._parsed_config["n_shared_experts"] == 2

    def test_initialization_without_config(self):
        """Test MoEModel initialization without config raises error."""
        with pytest.raises(ValueError, match="Model configuration is required"):
            MoEModel("test-model", None)

    def test_config_parsing_deepseek_v2(self):
        """Test configuration parsing for DeepSeek V2."""
        config_dict = {
            "model_type": "deepseek_v2",
            "hidden_size": 5120,
            "num_hidden_layers": 27,
            "num_attention_heads": 128,
            "num_key_value_heads": 16,  # GQA
            "intermediate_size": 12288,
            "vocab_size": 102400,
            "n_routed_experts": 160,
            "num_experts_per_tok": 6,
            "n_shared_experts": 2,
            "qk_nope_head_dim": 128,
            "qk_rope_head_dim": 64,
            "v_head_dim": 128,
        }
        config = MockConfig(config_dict)

        model = MoEModel("deepseek-ai/DeepSeek-V2", config)

        assert model._parsed_config["model_type"] == "deepseek_v2"
        assert model._parsed_config["num_key_value_heads"] == 16
        assert model._parsed_config["n_routed_experts"] == 160
        assert model._parsed_config["num_experts_per_tok"] == 6

    def test_config_parsing_deepseek_v3(self):
        """Test configuration parsing for DeepSeek V3."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("deepseek-ai/DeepSeek-V3", config)

        assert model._parsed_config["model_type"] == "deepseek_v3"
        assert model._parsed_config["n_routed_experts"] == 256
        assert model._parsed_config["num_experts_per_tok"] == 8
        assert model._parsed_config["n_shared_experts"] == 2

    def test_config_validation_invalid_expert_config(self):
        """Test configuration validation with invalid expert configuration."""
        # Test with zero routed experts
        config_dict = create_mock_model_config("deepseek_v3")
        config_dict["n_routed_experts"] = 0
        config = MockConfig(config_dict)

        with pytest.raises(ValueError, match="n_routed_experts must be positive"):
            MoEModel("test-model", config)

        # Test with experts_per_token > routed_experts
        config_dict = create_mock_model_config("deepseek_v3")
        config_dict["num_experts_per_tok"] = 300  # > 256 routed experts
        config = MockConfig(config_dict)

        with pytest.raises(
            ValueError, match="num_experts_per_tok cannot exceed n_routed_experts"
        ):
            MoEModel("test-model", config)

    def test_config_validation_missing_moe_params(self):
        """Test configuration validation with missing MoE parameters."""
        config_dict = create_mock_model_config("llama")  # Non-MoE config
        config_dict["model_type"] = "deepseek_v3"  # But claim to be MoE
        # Missing n_routed_experts, num_experts_per_tok
        config = MockConfig(config_dict)

        with pytest.raises(ValueError, match="MoE configuration is incomplete"):
            MoEModel("test-model", config)


class TestMoEModelParameterComputation:
    """Test parameter computation methods for MoEModel."""

    def test_attention_params_standard(self):
        """Test attention parameter computation for standard attention."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        attention_params = model.compute_attention_params()

        # Expected: 4 projections (Q, K, V, O) * hidden_size * hidden_size
        expected = 4 * 7168 * 7168
        assert attention_params == expected

    def test_attention_params_with_gqa(self):
        """Test attention parameter computation with Grouped Query Attention."""
        config_dict = create_mock_model_config("deepseek_v3")
        config_dict["num_key_value_heads"] = 16  # GQA
        config = MockConfig(config_dict)
        model = MoEModel("test-model", config)

        attention_params = model.compute_attention_params()

        # Expected: Q proj (full) + K,V proj (reduced) + O proj (full)
        hidden_size = 7168
        num_heads = 128
        num_kv_heads = 16
        head_dim = hidden_size // num_heads

        q_params = hidden_size * hidden_size
        k_params = hidden_size * (num_kv_heads * head_dim)
        v_params = hidden_size * (num_kv_heads * head_dim)
        o_params = hidden_size * hidden_size
        expected = q_params + k_params + v_params + o_params

        assert attention_params == expected

    def test_mlp_params_with_shared_experts(self):
        """Test MLP parameter computation with shared experts."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        mlp_params = model.compute_mlp_params()

        hidden_size = 7168
        intermediate_size = 18432
        n_routed_experts = 256
        n_shared_experts = 2

        # Expected: routed experts + shared experts
        routed_expert_params = n_routed_experts * 3 * hidden_size * intermediate_size
        shared_expert_params = n_shared_experts * 3 * hidden_size * intermediate_size
        router_params = hidden_size * n_routed_experts

        expected = routed_expert_params + shared_expert_params + router_params
        assert mlp_params == expected

    def test_mlp_params_without_shared_experts(self):
        """Test MLP parameter computation without shared experts."""
        config_dict = create_mock_model_config("deepseek_v3")
        config_dict["n_shared_experts"] = 0
        config = MockConfig(config_dict)
        model = MoEModel("test-model", config)

        mlp_params = model.compute_mlp_params()

        hidden_size = 7168
        intermediate_size = 18432
        n_routed_experts = 256

        # Expected: only routed experts + router
        routed_expert_params = n_routed_experts * 3 * hidden_size * intermediate_size
        router_params = hidden_size * n_routed_experts

        expected = routed_expert_params + router_params
        assert mlp_params == expected

    def test_embedding_params_tied(self):
        """Test embedding parameter computation with tied embeddings."""
        config_dict = create_mock_model_config("deepseek_v3")
        config_dict["tie_word_embeddings"] = True
        config = MockConfig(config_dict)
        model = MoEModel("test-model", config)

        embedding_params = model.compute_embedding_params()

        # Expected: only input embeddings (output embeddings are tied)
        expected = 129280 * 7168
        assert embedding_params == expected

    def test_total_params_consistency(self):
        """Test that total parameters equals sum of components."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        attention_params = model.compute_attention_params()
        mlp_params = model.compute_mlp_params()
        embedding_params = model.compute_embedding_params()
        total_params = model.get_total_params()

        expected_total = attention_params + mlp_params + embedding_params
        assert total_params == expected_total

    def test_active_params_per_token(self):
        """Test active parameters per token computation."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        active_params = model.compute_active_params_per_token()

        # Should be less than total params due to expert sparsity
        total_params = model.get_total_params()
        assert active_params < total_params
        assert active_params > 0

        # Should account for attention + activated experts + embeddings
        attention_params = model.compute_attention_params()
        embedding_params = model.compute_embedding_params()
        assert active_params > attention_params + embedding_params


class TestMoEModelFLOPsComputation:
    """Test FLOP computation methods for MoEModel."""

    def test_flops_computation_basic(self):
        """Test basic FLOP computation."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        flops = model.compute_flops(sequence_length=2048, batch_size=1)

        assert "attention" in flops
        assert "moe" in flops
        assert "router" in flops
        assert "total" in flops
        assert flops["total"] > 0
        assert flops["attention"] > 0
        assert flops["moe"] > 0
        assert flops["router"] > 0

    def test_flops_per_token_vs_total(self):
        """Test that per-token FLOPs are less than total model FLOPs."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        flops_total = model.compute_flops(sequence_length=2048, batch_size=1)
        flops_per_token = model.compute_flops_per_token()

        # Per-token FLOPs should be much less due to expert sparsity
        expected_per_token = flops_total["total"] / 2048
        assert flops_per_token < expected_per_token
        assert flops_per_token > 0

    def test_flops_scaling_with_sequence_length(self):
        """Test FLOP scaling with sequence length."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        flops_1k = model.compute_flops(sequence_length=1024, batch_size=1)
        flops_2k = model.compute_flops(sequence_length=2048, batch_size=1)

        # FLOPs should scale with sequence length
        assert flops_2k["total"] > flops_1k["total"]

        # Attention should scale quadratically, MoE linearly
        attention_ratio = flops_2k["attention"] / flops_1k["attention"]
        moe_ratio = flops_2k["moe"] / flops_1k["moe"]
        assert attention_ratio > moe_ratio

    def test_flops_with_different_expert_configs(self):
        """Test FLOP computation with different expert configurations."""
        # More experts per token
        config_more_experts = MockConfig(create_mock_model_config("deepseek_v3"))
        config_more_experts.num_experts_per_tok = 16  # Double the experts
        model_more = MoEModel("test-model-more", config_more_experts)

        # Fewer experts per token
        config_fewer_experts = MockConfig(create_mock_model_config("deepseek_v3"))
        config_fewer_experts.num_experts_per_tok = 4  # Half the experts
        model_fewer = MoEModel("test-model-fewer", config_fewer_experts)

        flops_more = model_more.compute_flops(sequence_length=2048, batch_size=1)
        flops_fewer = model_fewer.compute_flops(sequence_length=2048, batch_size=1)

        # More experts per token should have more FLOPs
        assert flops_more["moe"] > flops_fewer["moe"]
        assert flops_more["total"] > flops_fewer["total"]


class TestMoEModelMemoryComputation:
    """Test memory computation methods for MoEModel."""

    def test_memory_requirements_basic(self):
        """Test basic memory requirements computation."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        memory = model.compute_memory_requirements(sequence_length=2048, batch_size=1)

        assert "parameters" in memory
        assert "activations" in memory
        assert "kv_cache" in memory
        assert "expert_cache" in memory
        assert "total" in memory
        assert memory["total"] > 0
        assert memory["parameters"] > 0
        assert memory["activations"] > 0
        assert memory["kv_cache"] > 0
        assert memory["expert_cache"] > 0

    def test_memory_with_expert_parallelism(self):
        """Test memory computation with expert parallelism."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        parallel_config = ParallelConfig(expert_parallel_size=8)
        memory = model.compute_memory_requirements(
            sequence_length=2048, batch_size=1, parallel_config=parallel_config
        )

        # Should have expert-specific memory calculations
        assert "expert_cache" in memory
        assert memory["expert_cache"] > 0

    def test_memory_scaling_with_experts(self):
        """Test memory scaling with different numbers of experts."""
        # More experts
        config_more = MockConfig(create_mock_model_config("deepseek_v3"))
        config_more.n_routed_experts = 512  # Double the experts
        model_more = MoEModel("test-model-more", config_more)

        # Fewer experts
        config_fewer = MockConfig(create_mock_model_config("deepseek_v3"))
        config_fewer.n_routed_experts = 128  # Half the experts
        model_fewer = MoEModel("test-model-fewer", config_fewer)

        memory_more = model_more.compute_memory_requirements(
            sequence_length=2048, batch_size=1
        )
        memory_fewer = model_fewer.compute_memory_requirements(
            sequence_length=2048, batch_size=1
        )

        # More experts should require more parameter memory
        assert memory_more["parameters"] > memory_fewer["parameters"]
        assert memory_more["total"] > memory_fewer["total"]


class TestMoEModelMatrixShapes:
    """Test matrix shape computation for MoEModel."""

    def test_matrix_shapes_basic(self):
        """Test basic matrix shape computation."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        shapes = model.get_matrix_shapes()

        assert_shapes_valid(shapes)
        assert "attention" in shapes
        assert "moe" in shapes
        assert "router" in shapes

        # Check attention shapes
        attention_shapes = shapes["attention"]
        assert attention_shapes["q_proj"] == (7168, 7168)
        assert attention_shapes["k_proj"] == (7168, 7168)
        assert attention_shapes["v_proj"] == (7168, 7168)
        assert attention_shapes["o_proj"] == (7168, 7168)

        # Check MoE shapes
        moe_shapes = shapes["moe"]
        assert "routed_experts" in moe_shapes
        assert "shared_experts" in moe_shapes

        # Check router shapes
        router_shapes = shapes["router"]
        assert router_shapes["gate"] == (7168, 256)  # hidden_size x n_routed_experts

    def test_matrix_shapes_with_expert_parallel(self):
        """Test matrix shapes with expert parallelism."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        parallel_config = ParallelConfig(expert_parallel_size=8)
        shapes = model.get_matrix_shapes(parallel_config)

        # Expert shapes should be modified for parallelism
        moe_shapes = shapes["moe"]
        assert "routed_experts" in moe_shapes

        # Each device should handle fewer experts
        expected_experts_per_device = 256 // 8  # 32 experts per device
        # Shape should reflect this partitioning
        assert moe_shapes["routed_experts"]["gate_proj"][0] == 7168

    def test_matrix_shapes_with_tensor_parallel(self):
        """Test matrix shapes with tensor parallelism."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        parallel_config = ParallelConfig(tensor_parallel_size=4)
        shapes = model.get_matrix_shapes(parallel_config)

        # Attention shapes should be modified for tensor parallelism
        attention_shapes = shapes["attention"]
        assert attention_shapes["q_proj"] == (7168, 1792)  # 7168 / 4
        assert attention_shapes["k_proj"] == (7168, 1792)
        assert attention_shapes["v_proj"] == (7168, 1792)
        assert attention_shapes["o_proj"] == (1792, 7168)  # Row-parallel

        # MoE expert shapes should also be modified
        moe_shapes = shapes["moe"]
        expert_shapes = moe_shapes["routed_experts"]
        assert expert_shapes["gate_proj"] == (7168, 4608)  # 18432 / 4
        assert expert_shapes["up_proj"] == (7168, 4608)
        assert expert_shapes["down_proj"] == (4608, 7168)


class TestMoEModelValidation:
    """Test validation methods for MoEModel."""

    def test_parallel_config_validation_tensor_parallel(self):
        """Test validation of tensor parallel configurations."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        valid_configs = [
            ParallelConfig(tensor_parallel_size=1),
            ParallelConfig(tensor_parallel_size=2),
            ParallelConfig(tensor_parallel_size=4),
            ParallelConfig(tensor_parallel_size=8),
            ParallelConfig(tensor_parallel_size=16),
            ParallelConfig(tensor_parallel_size=32),
            ParallelConfig(tensor_parallel_size=64),
            ParallelConfig(tensor_parallel_size=128),  # Equal to num_heads
        ]

        for parallel_config in valid_configs:
            assert model.validate_parallel_config(parallel_config)

    def test_parallel_config_validation_expert_parallel(self):
        """Test validation of expert parallel configurations."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        valid_configs = [
            ParallelConfig(expert_parallel_size=1),
            ParallelConfig(expert_parallel_size=2),
            ParallelConfig(expert_parallel_size=4),
            ParallelConfig(expert_parallel_size=8),
            ParallelConfig(expert_parallel_size=16),
            ParallelConfig(expert_parallel_size=32),
            ParallelConfig(expert_parallel_size=64),
            ParallelConfig(expert_parallel_size=128),
            ParallelConfig(expert_parallel_size=256),  # Equal to n_routed_experts
        ]

        for parallel_config in valid_configs:
            assert model.validate_parallel_config(parallel_config)

    def test_parallel_config_validation_invalid(self):
        """Test validation of invalid parallel configurations."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        invalid_configs = [
            ParallelConfig(tensor_parallel_size=129),  # > num_heads
            ParallelConfig(expert_parallel_size=257),  # > n_routed_experts
            ParallelConfig(tensor_parallel_size=3),  # 128 not divisible by 3
            ParallelConfig(expert_parallel_size=7),  # 256 not divisible by 7
        ]

        for parallel_config in invalid_configs:
            assert not model.validate_parallel_config(parallel_config)

    def test_parallel_config_validation_mixed(self):
        """Test validation of mixed parallel configurations."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        valid_mixed_config = ParallelConfig(
            tensor_parallel_size=4,
            expert_parallel_size=8,
            pipeline_parallel_size=2,
            data_parallel_size=2,
        )
        assert model.validate_parallel_config(valid_mixed_config)

        invalid_mixed_config = ParallelConfig(
            tensor_parallel_size=3,  # Invalid
            expert_parallel_size=7,  # Invalid
        )
        assert not model.validate_parallel_config(invalid_mixed_config)


class TestMoEModelMetrics:
    """Test comprehensive metrics generation for MoEModel."""

    def test_get_metrics_basic(self):
        """Test basic metrics generation."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("deepseek-ai/DeepSeek-R1", config)

        metrics = model.get_metrics()

        assert_metrics_valid(metrics)
        assert metrics.model_name == "deepseek-ai/DeepSeek-R1"
        assert metrics.architecture == "deepseek_v3"
        assert metrics.sequence_length == 2048  # default
        assert metrics.batch_size == 1  # default

        # MoE-specific metrics
        assert hasattr(metrics, "experts_per_token")
        assert hasattr(metrics, "active_params_per_token")
        assert metrics.experts_per_token == 8
        assert metrics.active_params_per_token > 0
        assert metrics.active_params_per_token < metrics.total_params

    def test_get_metrics_with_expert_parallel(self):
        """Test metrics generation with expert parallelism."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        parallel_config = ParallelConfig(tensor_parallel_size=2, expert_parallel_size=8)
        metrics = model.get_metrics(parallel_config=parallel_config)

        assert_metrics_valid(metrics)
        assert metrics.parallel_config == parallel_config

        # Should have expert-specific information
        assert metrics.experts_per_token == 8
        assert metrics.active_params_per_token > 0

    def test_get_metrics_efficiency_analysis(self):
        """Test efficiency analysis in metrics."""
        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        metrics = model.get_metrics()

        # Calculate efficiency metrics
        param_efficiency = metrics.active_params_per_token / metrics.total_params
        flop_efficiency = metrics.flops_per_token / metrics.flops_forward

        # MoE should be efficient (low ratios due to sparsity)
        assert param_efficiency < 0.5  # Less than 50% of params active
        assert flop_efficiency < 0.1  # Much fewer FLOPs per token

        # But still substantial absolute values
        assert metrics.active_params_per_token > 1_000_000_000  # > 1B active params
        assert metrics.flops_per_token > 1_000_000_000  # > 1G FLOPs per token


class TestMoEModelEdgeCases:
    """Test edge cases and error conditions for MoEModel."""

    def test_minimal_moe_config(self):
        """Test with minimal valid MoE configuration."""
        minimal_config = MockConfig(
            {
                "hidden_size": 512,
                "num_hidden_layers": 2,
                "num_attention_heads": 8,
                "intermediate_size": 1024,
                "vocab_size": 1000,
                "n_routed_experts": 4,
                "num_experts_per_tok": 2,
                "n_shared_experts": 1,
                "model_type": "deepseek_v3",
            }
        )

        model = MoEModel("minimal-moe", minimal_config)
        metrics = model.get_metrics(sequence_length=1, batch_size=1)

        assert_metrics_valid(metrics)
        assert metrics.total_params > 0
        assert metrics.active_params_per_token > 0
        assert metrics.active_params_per_token < metrics.total_params

    def test_single_expert_per_token(self):
        """Test with single expert per token."""
        config_dict = create_mock_model_config("deepseek_v3")
        config_dict["num_experts_per_tok"] = 1
        config = MockConfig(config_dict)

        model = MoEModel("single-expert", config)
        metrics = model.get_metrics()

        assert_metrics_valid(metrics)
        assert metrics.experts_per_token == 1
        # Should have minimal expert activation
        assert metrics.active_params_per_token < metrics.total_params * 0.1

    def test_no_shared_experts(self):
        """Test with no shared experts."""
        config_dict = create_mock_model_config("deepseek_v3")
        config_dict["n_shared_experts"] = 0
        config = MockConfig(config_dict)

        model = MoEModel("no-shared", config)
        metrics = model.get_metrics()

        assert_metrics_valid(metrics)
        # Should still work without shared experts
        assert metrics.total_params > 0
        assert metrics.active_params_per_token > 0

    def test_large_moe_model(self):
        """Test with very large MoE model."""
        large_config = MockConfig(
            {
                "hidden_size": 16384,
                "num_hidden_layers": 128,
                "num_attention_heads": 128,
                "intermediate_size": 65536,
                "vocab_size": 200000,
                "n_routed_experts": 1024,
                "num_experts_per_tok": 16,
                "n_shared_experts": 8,
                "model_type": "deepseek_v3",
            }
        )

        model = MoEModel("large-moe", large_config)
        metrics = model.get_metrics()

        assert_metrics_valid(metrics)
        assert metrics.total_params > 1_000_000_000_000  # > 1T params
        assert metrics.active_params_per_token > 100_000_000_000  # > 100B active

    def test_architecture_inference(self):
        """Test architecture inference for different MoE models."""
        test_cases = [
            ("deepseek-ai/DeepSeek-V2", {"model_type": "deepseek_v2"}, "deepseek_v2"),
            ("deepseek-ai/DeepSeek-V3", {"model_type": "deepseek_v3"}, "deepseek_v3"),
            ("custom/moe-model", {"model_type": "custom_moe"}, "custom_moe"),
        ]

        for model_name, config_dict, expected_arch in test_cases:
            full_config = {**create_mock_model_config("deepseek_v3"), **config_dict}
            config = MockConfig(full_config)
            model = MoEModel(model_name, config)
            metrics = model.get_metrics()
            assert metrics.architecture == expected_arch


class TestMoEModelCaching:
    """Test caching functionality for MoEModel."""

    @patch("llm_modeling_metrics.utils.caching.get_cache_manager")
    def test_parameter_caching(self, mock_cache_manager):
        """Test that parameter computations are cached."""
        mock_cache = Mock()
        mock_cache_manager.return_value.computation_cache = mock_cache

        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        # Call cached methods multiple times
        params1 = model._get_cached_mlp_params()
        params2 = model._get_cached_mlp_params()

        # Should return same result
        assert params1 == params2

    @patch("llm_modeling_metrics.utils.caching.get_cache_manager")
    def test_expert_computation_caching(self, mock_cache_manager):
        """Test that expert-specific computations are cached."""
        mock_cache = Mock()
        mock_cache_manager.return_value.computation_cache = mock_cache

        config = MockConfig(create_mock_model_config("deepseek_v3"))
        model = MoEModel("test-model", config)

        # Call expert-specific cached methods
        active_params1 = model._get_cached_active_params_per_token()
        active_params2 = model._get_cached_active_params_per_token()

        assert active_params1 == active_params2


class TestMoEModelComparison:
    """Test comparison between MoE and dense models."""

    def test_moe_vs_dense_efficiency(self):
        """Test efficiency comparison between MoE and equivalent dense model."""
        # Create MoE model
        moe_config = MockConfig(create_mock_model_config("deepseek_v3"))
        moe_model = MoEModel("moe-model", moe_config)
        moe_metrics = moe_model.get_metrics()

        # Create equivalent dense model (hypothetical)
        dense_config = MockConfig(
            {
                "hidden_size": 7168,
                "num_hidden_layers": 61,
                "num_attention_heads": 128,
                "intermediate_size": 18432,
                "vocab_size": 129280,
                "model_type": "llama",
            }
        )

        # MoE should have more total params but fewer active params
        assert moe_metrics.total_params > 100_000_000_000  # > 100B
        assert (
            moe_metrics.active_params_per_token < moe_metrics.total_params * 0.2
        )  # < 20% active

        # MoE should be FLOP-efficient per token
        flop_efficiency = moe_metrics.flops_per_token / moe_metrics.flops_forward
        assert flop_efficiency < 0.1  # < 10% of total FLOPs per token

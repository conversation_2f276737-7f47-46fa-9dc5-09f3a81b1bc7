"""
Tests for roofline and timing API endpoints.
"""

from unittest.mock import Mock, patch

import numpy as np
import pytest
from fastapi.testclient import TestClient

from llm_modeling_metrics.hardware.models import HardwareSpec, HardwareType
from llm_modeling_metrics.hardware.roofline_service import (
    ComparisonPlotData,
    KneePoint,
    OperatorPoint,
    RooflineData,
    RooflinePlotData,
)
from llm_modeling_metrics.hardware.timing_service import (
    BottleneckAnalysis,
    OperatorTiming,
    TimingComparison,
)
from llm_modeling_metrics.web.app import app


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_hardware_spec():
    """Create mock hardware specification."""
    return HardwareSpec(
        id="test_gpu",
        name="Test GPU",
        type=HardwareType.GPU,
        architecture="test_arch",
        memory_size_gb=80,
        memory_bandwidth_gbps=2000,
        peak_flops={"fp32": 100.0, "fp16": 200.0, "bf16": 200.0},
        supported_precisions=["fp32", "fp16", "bf16"],
    )


@pytest.fixture
def mock_roofline_data():
    """Create mock roofline data."""
    oi_range = np.logspace(-1, 3, 100)
    return RooflineData(
        operational_intensity_range=oi_range,
        performance_curves={
            "test_gpu": {"fp16": np.minimum(200.0, 2000 * oi_range / 1e12)}
        },
        knee_points={
            "test_gpu": {
                "fp16": KneePoint(
                    operational_intensity=100.0,
                    performance_tflops=200.0,
                    precision="fp16",
                    hardware_id="test_gpu",
                )
            }
        },
        operator_points=[
            OperatorPoint(
                operator_name="test_op",
                operational_intensity=50.0,
                achieved_performance_tflops=150.0,
                utilization_percent=75.0,
                is_compute_bound=True,
                precision="fp16",
                hardware_id="test_gpu",
            )
        ],
        hardware_specs={},
    )


@pytest.fixture
def mock_operator_timing():
    """Create mock operator timing."""
    return OperatorTiming(
        operator_name="test_op",
        hardware_id="test_gpu",
        compute_time_ms=10.0,
        memory_time_ms=5.0,
        execution_time_ms=10.0,
        bottleneck_type="compute",
        utilization_percent=75.0,
        operational_intensity=50.0,
        flops=1000000,
        memory_movement_bytes=20000,
        precision_overhead_factor=1.0,
        tensor_core_utilization=True,
        optimization_opportunities=["Use tensor cores"],
    )


class TestRooflineAPI:
    """Test roofline API endpoints."""

    @patch("llm_modeling_metrics.web.app.hardware_service")
    @patch("llm_modeling_metrics.web.app.roofline_service")
    def test_generate_roofline_data_success(
        self,
        mock_roofline_service,
        mock_hardware_service,
        client,
        mock_hardware_spec,
        mock_roofline_data,
    ):
        """Test successful roofline data generation."""
        # Setup mocks
        mock_hardware_service.get_hardware_specs.return_value = mock_hardware_spec
        mock_roofline_service.generate_roofline_data.return_value = mock_roofline_data

        # Make request
        response = client.post(
            "/api/roofline/generate",
            json={"hardware_ids": ["test_gpu"], "precisions": ["fp16"]},
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert "operational_intensity_range" in data
        assert "performance_curves" in data
        assert "knee_points" in data
        assert "operator_points" in data
        assert "hardware_specs" in data

        # Verify service calls
        mock_hardware_service.get_hardware_specs.assert_called_with("test_gpu")
        mock_roofline_service.generate_roofline_data.assert_called_once()

    @patch("llm_modeling_metrics.web.app.hardware_service")
    def test_generate_roofline_data_hardware_not_found(
        self, mock_hardware_service, client
    ):
        """Test roofline generation with non-existent hardware."""
        # Setup mock
        mock_hardware_service.get_hardware_specs.return_value = None

        # Make request
        response = client.post(
            "/api/roofline/generate", json={"hardware_ids": ["nonexistent_gpu"]}
        )

        # Verify error response
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]

    def test_generate_roofline_data_invalid_request(self, client):
        """Test roofline generation with invalid request."""
        # Make request with empty hardware IDs
        response = client.post("/api/roofline/generate", json={"hardware_ids": []})

        # Verify validation error
        assert response.status_code == 422

    @patch("llm_modeling_metrics.web.app.hardware_service")
    @patch("llm_modeling_metrics.web.app.roofline_service")
    def test_plot_operators_on_roofline_success(
        self, mock_roofline_service, mock_hardware_service, client, mock_hardware_spec
    ):
        """Test successful operator plotting on roofline."""
        # Setup mocks
        mock_hardware_service.get_hardware_specs.return_value = mock_hardware_spec
        mock_plot_data = RooflinePlotData(
            hardware_id="test_gpu",
            hardware_name="Test GPU",
            operational_intensity_range=np.logspace(-1, 3, 100),
            roofline_curves={"fp16": np.ones(100) * 200.0},
            knee_points={"fp16": KneePoint(100.0, 200.0, "fp16", "test_gpu")},
            operator_points=[],
        )
        mock_roofline_service.plot_operators_on_roofline.return_value = mock_plot_data

        # Make request
        response = client.post(
            "/api/roofline/plot-operators",
            json={
                "operators": [
                    {
                        "name": "test_op",
                        "type": "attention",
                        "parameters": {"seq_len": 2048, "batch_size": 1},
                    }
                ],
                "hardware_id": "test_gpu",
            },
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["hardware_id"] == "test_gpu"
        assert data["hardware_name"] == "Test GPU"
        assert "operational_intensity_range" in data
        assert "roofline_curves" in data
        assert "knee_points" in data
        assert "operator_points" in data

    @patch("llm_modeling_metrics.web.app.hardware_service")
    @patch("llm_modeling_metrics.web.app.roofline_service")
    def test_compare_hardware_rooflines_success(
        self, mock_roofline_service, mock_hardware_service, client, mock_hardware_spec
    ):
        """Test successful hardware roofline comparison."""
        # Setup mocks
        mock_hardware_service.get_hardware_specs.return_value = mock_hardware_spec
        mock_comparison_data = ComparisonPlotData(
            hardware_platforms=["test_gpu1", "test_gpu2"],
            operational_intensity_range=np.logspace(-1, 3, 100),
            roofline_data={},
            performance_rankings={},
            recommendations=["Test recommendation"],
        )
        mock_roofline_service.compare_hardware_rooflines.return_value = (
            mock_comparison_data
        )

        # Make request
        response = client.post(
            "/api/roofline/compare", json={"hardware_ids": ["test_gpu1", "test_gpu2"]}
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert "hardware_platforms" in data
        assert "operational_intensity_range" in data
        assert "roofline_data" in data
        assert "performance_rankings" in data
        assert "recommendations" in data

    def test_compare_hardware_rooflines_insufficient_hardware(self, client):
        """Test hardware comparison with insufficient hardware count."""
        # Make request with only one hardware ID
        response = client.post(
            "/api/roofline/compare", json={"hardware_ids": ["test_gpu1"]}
        )

        # Verify validation error
        assert response.status_code == 422


class TestTimingAPI:
    """Test timing API endpoints."""

    @patch("llm_modeling_metrics.web.app.hardware_service")
    @patch("llm_modeling_metrics.web.app.timing_service")
    def test_analyze_operator_timing_success(
        self,
        mock_timing_service,
        mock_hardware_service,
        client,
        mock_hardware_spec,
        mock_operator_timing,
    ):
        """Test successful operator timing analysis."""
        # Setup mocks
        mock_hardware_service.get_hardware_specs.return_value = mock_hardware_spec
        mock_timing_service.compute_operator_timing.return_value = mock_operator_timing

        # Make request
        response = client.post(
            "/api/timing/analyze",
            json={
                "operators": [
                    {
                        "name": "test_op",
                        "type": "attention",
                        "parameters": {"seq_len": 2048, "batch_size": 1},
                    }
                ],
                "hardware_id": "test_gpu",
            },
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        timing_data = data[0]
        assert timing_data["operator_name"] == "test_op"
        assert timing_data["hardware_id"] == "test_gpu"
        assert timing_data["compute_time_ms"] == 10.0
        assert timing_data["memory_time_ms"] == 5.0
        assert timing_data["execution_time_ms"] == 10.0
        assert timing_data["bottleneck_type"] == "compute"
        assert timing_data["utilization_percent"] == 75.0

    @patch("llm_modeling_metrics.web.app.hardware_service")
    @patch("llm_modeling_metrics.web.app.timing_service")
    def test_analyze_bottlenecks_success(
        self, mock_timing_service, mock_hardware_service, client, mock_hardware_spec
    ):
        """Test successful bottleneck analysis."""
        # Setup mocks
        mock_hardware_service.get_hardware_specs.return_value = mock_hardware_spec
        mock_bottleneck_analysis = BottleneckAnalysis(
            compute_bound_operators=["op1"],
            memory_bound_operators=["op2"],
            compute_utilization_avg=75.0,
            memory_utilization_avg=60.0,
            overall_bottleneck="compute",
            recommendations=["Optimize compute operations"],
        )
        mock_timing_service.analyze_bottlenecks.return_value = mock_bottleneck_analysis

        # Make request
        response = client.post(
            "/api/timing/bottlenecks",
            json={
                "operators": [
                    {
                        "name": "op1",
                        "type": "attention",
                        "parameters": {"seq_len": 2048},
                    },
                    {"name": "op2", "type": "mlp", "parameters": {"hidden_size": 4096}},
                ],
                "hardware_id": "test_gpu",
            },
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["compute_bound_operators"] == ["op1"]
        assert data["memory_bound_operators"] == ["op2"]
        assert data["compute_utilization_avg"] == 75.0
        assert data["memory_utilization_avg"] == 60.0
        assert data["overall_bottleneck"] == "compute"
        assert len(data["recommendations"]) > 0

    @patch("llm_modeling_metrics.web.app.hardware_service")
    @patch("llm_modeling_metrics.web.app.timing_service")
    def test_compare_timing_across_hardware_success(
        self,
        mock_timing_service,
        mock_hardware_service,
        client,
        mock_hardware_spec,
        mock_operator_timing,
    ):
        """Test successful cross-hardware timing comparison."""
        # Setup mocks
        mock_hardware_service.get_hardware_specs.return_value = mock_hardware_spec
        mock_timing_comparison = TimingComparison(
            operators=["test_op"],
            hardware_platforms=["gpu1", "gpu2"],
            timing_matrix={
                "test_op": {"gpu1": mock_operator_timing, "gpu2": mock_operator_timing}
            },
            performance_rankings={"test_op": ["gpu1", "gpu2"]},
            recommendations=["GPU1 is faster for this workload"],
        )
        mock_timing_service.compare_across_hardware.return_value = (
            mock_timing_comparison
        )

        # Make request
        response = client.post(
            "/api/timing/compare-hardware",
            json={
                "operators": [
                    {
                        "name": "test_op",
                        "type": "attention",
                        "parameters": {"seq_len": 2048},
                    }
                ],
                "hardware_ids": ["gpu1", "gpu2"],
            },
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()
        assert data["operators"] == ["test_op"]
        assert data["hardware_platforms"] == ["gpu1", "gpu2"]
        assert "timing_matrix" in data
        assert "performance_rankings" in data
        assert "recommendations" in data

    @patch("llm_modeling_metrics.web.app.hardware_service")
    def test_analyze_timing_hardware_not_found(self, mock_hardware_service, client):
        """Test timing analysis with non-existent hardware."""
        # Setup mock
        mock_hardware_service.get_hardware_specs.return_value = None

        # Make request
        response = client.post(
            "/api/timing/analyze",
            json={
                "operators": [
                    {"name": "test_op", "type": "attention", "parameters": {}}
                ],
                "hardware_id": "nonexistent_gpu",
            },
        )

        # Verify error response
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]

    def test_analyze_timing_invalid_request(self, client):
        """Test timing analysis with invalid request."""
        # Make request with empty operators
        response = client.post(
            "/api/timing/analyze", json={"operators": [], "hardware_id": "test_gpu"}
        )

        # Verify validation error
        assert response.status_code == 422

    def test_compare_timing_insufficient_hardware(self, client):
        """Test timing comparison with insufficient hardware count."""
        # Make request with only one hardware ID
        response = client.post(
            "/api/timing/compare-hardware",
            json={
                "operators": [
                    {"name": "test_op", "type": "attention", "parameters": {}}
                ],
                "hardware_ids": ["test_gpu1"],
            },
        )

        # Verify validation error
        assert response.status_code == 422


class TestAPIIntegration:
    """Test API integration scenarios."""

    @patch("llm_modeling_metrics.web.app.hardware_service")
    @patch("llm_modeling_metrics.web.app.roofline_service")
    @patch("llm_modeling_metrics.web.app.timing_service")
    def test_roofline_timing_workflow(
        self,
        mock_timing_service,
        mock_roofline_service,
        mock_hardware_service,
        client,
        mock_hardware_spec,
        mock_roofline_data,
        mock_operator_timing,
    ):
        """Test complete workflow from roofline generation to timing analysis."""
        # Setup mocks
        mock_hardware_service.get_hardware_specs.return_value = mock_hardware_spec
        mock_roofline_service.generate_roofline_data.return_value = mock_roofline_data
        mock_timing_service.compute_operator_timing.return_value = mock_operator_timing

        # Step 1: Generate roofline data
        roofline_response = client.post(
            "/api/roofline/generate",
            json={"hardware_ids": ["test_gpu"], "precisions": ["fp16"]},
        )
        assert roofline_response.status_code == 200

        # Step 2: Analyze operator timing
        timing_response = client.post(
            "/api/timing/analyze",
            json={
                "operators": [
                    {
                        "name": "test_op",
                        "type": "attention",
                        "parameters": {"seq_len": 2048},
                    }
                ],
                "hardware_id": "test_gpu",
            },
        )
        assert timing_response.status_code == 200

        # Verify both responses contain consistent data
        roofline_data = roofline_response.json()
        timing_data = timing_response.json()

        assert len(roofline_data["operator_points"]) > 0
        assert len(timing_data) > 0
        assert timing_data[0]["hardware_id"] == "test_gpu"

    def test_error_handling_consistency(self, client):
        """Test that error handling is consistent across endpoints."""
        # Test all endpoints with invalid hardware ID
        endpoints_and_payloads = [
            ("/api/roofline/generate", {"hardware_ids": ["invalid_gpu"]}),
            (
                "/api/roofline/plot-operators",
                {
                    "operators": [
                        {"name": "test", "type": "attention", "parameters": {}}
                    ],
                    "hardware_id": "invalid_gpu",
                },
            ),
            (
                "/api/timing/analyze",
                {
                    "operators": [
                        {"name": "test", "type": "attention", "parameters": {}}
                    ],
                    "hardware_id": "invalid_gpu",
                },
            ),
            (
                "/api/timing/bottlenecks",
                {
                    "operators": [
                        {"name": "test", "type": "attention", "parameters": {}}
                    ],
                    "hardware_id": "invalid_gpu",
                },
            ),
        ]

        for endpoint, payload in endpoints_and_payloads:
            with patch("llm_modeling_metrics.web.app.hardware_service") as mock_service:
                mock_service.get_hardware_specs.return_value = None
                response = client.post(endpoint, json=payload)
                assert response.status_code == 404
                assert "not found" in response.json()["detail"]


if __name__ == "__main__":
    pytest.main([__file__])

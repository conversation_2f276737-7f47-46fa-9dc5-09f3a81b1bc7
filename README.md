# LLM Modeling Metrics

A comprehensive Python library for analyzing computational requirements and performance characteristics of Large Language Models (LLMs), including both dense models and Mixture of Experts (MoE) architectures.

## 🚀 Features

- **🤖 Automatic Model Detection**: Supports dense and MoE architectures with automatic classification
- **📡 Real Config Fetching**: Automatic model configuration from HuggingFace Hub with caching
- **🧮 FLOP Computation**: Accurate forward pass FLOP calculations for various architectures
- **💾 Mixed Precision Memory Analysis**: Comprehensive memory estimation with mixed precision support
- **🔍 Attention Operators**: Modular support for MHA, GQA, and MLA attention mechanisms
- **⚡ Hardware Integration**: Performance analysis on specific hardware with roofline modeling
- **🔄 Parallel Strategy Analysis**: Multi-GPU deployment optimization and validation
- **📊 Model Comparison**: Compare multiple models across comprehensive metrics
- **🌐 Web Interface**: Interactive dashboard with real-time analysis and visualization
- **📤 Export Capabilities**: Export results to JSON, CSV, and Excel formats

## Supported Models

All models are automatically classified into two types based on their architecture:

### Dense Models
Standard transformer models where all parameters are active for every token:
- **Llama family**: Llama 2, Llama 3, Code Llama, and variants
- **Qwen family**: <PERSON>wen, <PERSON>wen2, Qwen2.5, and variants
- **Mistral family**: Mistral 7B and other dense variants
- **Other dense models**: Any standard transformer architecture

### Mixture of Experts (MoE) Models
Sparse transformer models where only a subset of parameters are active per token:
- **DeepSeek family**: DeepSeek V2, DeepSeek V3, and variants
- **Mixtral family**: Mixtral 8x7B, Mixtral 8x22B, and variants
- **Qwen MoE**: Qwen2.5-MoE and other MoE variants
- **Other MoE models**: Any model with expert routing mechanisms

## Installation

```bash
pip install llm-modeling-metrics
```

For web interface support:
```bash
pip install llm-modeling-metrics[web]
```

For development:
```bash
pip install llm-modeling-metrics[dev]
```

## 🚀 Quick Start

### Basic Model Analysis

```python
from llm_modeling_metrics import ModelFactory

# Create model with automatic config fetching
model = ModelFactory.create_model("meta-llama/Llama-3.1-8B")

# Get comprehensive metrics
metrics = model.get_metrics(sequence_length=2048)
print(f"Parameters: {metrics.total_params / 1e9:.1f}B")
print(f"Memory: {metrics.memory_total / 1e9:.2f} GB")
print(f"Architecture: {metrics.architecture}")

# Mixed precision memory analysis
memory = model.compute_memory_requirements(
    sequence_length=4096,
    weight_dtype='bf16',
    kv_cache_dtype='fp8',
    include_kv_cache=True
)
print(f"Mixed precision memory: {memory['memory_total'] / 1e9:.2f} GB")
```

### Model Comparison

```python
from llm_modeling_metrics.comparison import Comparator

# Compare multiple models with mixed precision
comparator = Comparator()
results = comparator.compare_models([
    "meta-llama/Llama-3.1-8B",
    "deepseek-ai/DeepSeek-V3",
    "Qwen/Qwen2.5-7B"
],
sequence_length=4096,
weight_dtype='bf16',
kv_cache_dtype='fp8'
)

# Export results
results.export_excel("model_comparison.xlsx")
df = results.to_dataframe()
```

### Web Interface

```bash
# Start the web server
python run_api.py
```

Then visit `http://localhost:8000` for the interactive dashboard with:
- Real-time model analysis
- Mixed precision optimization
- Hardware performance analysis
- Interactive visualizations
- Export capabilities

## 📚 Documentation

- **[Complete Documentation](docs/README.md)** - Documentation index and overview
- **[API Reference](docs/api_reference.md)** - Complete API documentation with examples
- **[Real Config API](docs/real_config_api.md)** - Automatic model configuration fetching
- **[Mixed Precision Guide](docs/mixed_precision_guide.md)** - Comprehensive mixed precision support
- **[Hardware Integration](docs/hardware_integration.md)** - Hardware analysis and optimization
- **[Deployment Guide](docs/deployment.md)** - Production deployment strategies

## 📖 Examples

See the [examples](examples/) directory for comprehensive usage examples:

- **[Real Config Example](examples/real_config_example.py)** - Automatic configuration fetching
- **[Attention Operators](examples/attention_operators_example.py)** - Attention mechanism analysis
- **[Operator Analysis](examples/operator_analysis_example.py)** - Detailed operator breakdown
- **[Hardware Integration](examples/hardware_integration_demo.py)** - Hardware performance analysis
- **[DeepSeek V3 Analysis](tests/test_deepseek_v3.py)** - MoE model analysis example

## 🌐 Web API Endpoints

The package provides a comprehensive REST API:

- `POST /api/analyze` - Model analysis with mixed precision support
- `POST /api/memory/analyze` - Detailed memory analysis
- `POST /api/memory/kv-growth` - KV cache growth analysis
- `GET /api/models/supported` - Supported model architectures
- `POST /api/roofline/generate` - Hardware roofline analysis
- `POST /api/hardware/recommendations` - Hardware recommendations

Full API documentation available at `/docs` when running the server.

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## Citation

If you use this package in your research, please cite:

```bibtex
@software{llm_modeling_metrics,
  title={LLM Modeling Metrics: A Comprehensive Analysis Library for Large Language Models},
  author={LLM Modeling Metrics Team},
  year={2024},
  url={https://github.com/your-org/llm-modeling-metrics}
}
```

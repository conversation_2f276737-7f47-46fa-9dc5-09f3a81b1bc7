"""Hardware specification editor for advanced users."""

import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml

from .config_manager import HardwareConfigManager
from .models import HardwareSpec, HardwareType, ValidationResult
from .validation import HardwareConfigValidator


class HardwareSpecEditor:
    """Advanced editor for hardware specifications."""

    def __init__(self, config_manager: Optional[HardwareConfigManager] = None):
        """Initialize the hardware specification editor.

        Args:
            config_manager: Optional config manager instance
        """
        self.config_manager = config_manager or HardwareConfigManager()
        self.validator = HardwareConfigValidator()
        self._edit_history: List[Dict[str, Any]] = []

    def create_hardware_template(self, hardware_type: HardwareType) -> Dict[str, Any]:
        """Create a template for a new hardware specification.

        Args:
            hardware_type: Type of hardware to create template for

        Returns:
            Dictionary template for hardware specification
        """
        base_template = {
            "name": "New Hardware Device",
            "architecture": "Unknown",
            "form_factor": "Unknown",
            "year": datetime.now().year,
            "memory_size_gb": 0,
            "memory_type": "Unknown",
            "memory_bandwidth_gbps": 0,
            "l2_cache_mb": None,
            "tdp_watts": None,
            "manufacturing_process": "Unknown",
            "interconnect": "Unknown",
        }

        if hardware_type == HardwareType.GPU:
            base_template.update(
                {
                    "streaming_multiprocessors": None,
                    "tensor_cores_per_sm": None,
                    "tensor_cores_total": None,
                    "fp32_cores_per_sm": None,
                    "fp32_cores_total": None,
                    "boost_clock_tensor": None,
                    "shared_memory_per_sm_kb": None,
                    "register_file_per_sm_kb": None,
                    "tensor_performance": {
                        "fp64_tensor": None,
                        "fp32_tensor": None,
                        "fp16_tensor": None,
                        "bf16_tensor": None,
                        "tf32_tensor": None,
                        "fp8_tensor": None,
                        "fp6_tensor": None,
                        "fp4_tensor": None,
                        "int8_tensor": None,
                    },
                    "vector_performance": {
                        "fp64": None,
                        "fp32": None,
                        "fp16": None,
                        "bf16": None,
                        "int32": None,
                    },
                }
            )
        else:  # NPU
            base_template.update(
                {
                    "tensor_performance": {
                        "fp16_tensor": None,
                        "bf16_tensor": None,
                        "int8_tensor": None,
                    },
                    "vector_performance": {"fp32": None, "fp16": None},
                }
            )

        return base_template

    def clone_hardware_spec(
        self, source_hardware_id: str, new_hardware_id: str
    ) -> Optional[Dict[str, Any]]:
        """Clone an existing hardware specification.

        Args:
            source_hardware_id: ID of hardware to clone from
            new_hardware_id: ID for the new hardware

        Returns:
            Cloned hardware specification or None if source not found
        """
        source_spec = self.config_manager.get_hardware_specs(source_hardware_id)
        if not source_spec:
            return None

        # Convert HardwareSpec back to dictionary format
        cloned_spec = {
            "name": f"{source_spec.name} (Clone)",
            "architecture": source_spec.architecture,
            "form_factor": source_spec.form_factor,
            "year": source_spec.year,
            "memory_size_gb": source_spec.memory_size_gb,
            "memory_type": source_spec.memory_type,
            "memory_bandwidth_gbps": source_spec.memory_bandwidth_gbps,
            "l2_cache_mb": source_spec.l2_cache_mb,
            "tdp_watts": source_spec.tdp_watts,
            "manufacturing_process": source_spec.manufacturing_process,
            "interconnect": source_spec.interconnect,
            "tensor_performance": (
                source_spec.tensor_performance.copy()
                if source_spec.tensor_performance
                else {}
            ),
            "vector_performance": (
                source_spec.vector_performance.copy()
                if source_spec.vector_performance
                else {}
            ),
        }

        # Add GPU-specific fields if applicable
        if source_spec.type == HardwareType.GPU and hasattr(
            source_spec, "tensor_cores"
        ):
            cloned_spec["tensor_cores_total"] = source_spec.tensor_cores

        return cloned_spec

    def validate_hardware_spec(
        self, hardware_id: str, hardware_data: Dict[str, Any]
    ) -> ValidationResult:
        """Validate a hardware specification.

        Args:
            hardware_id: Hardware identifier
            hardware_data: Hardware specification data

        Returns:
            ValidationResult with validation details
        """
        # Create a temporary config structure for validation
        temp_config = {"gpus": {}, "npus": {}}

        # Determine hardware type based on data or assume GPU
        if self._is_npu_spec(hardware_data):
            temp_config["npus"][hardware_id] = hardware_data
        else:
            temp_config["gpus"][hardware_id] = hardware_data

        return self.validator.validate_hardware_config_data(temp_config)

    def _is_npu_spec(self, hardware_data: Dict[str, Any]) -> bool:
        """Determine if hardware specification is for an NPU."""
        # Simple heuristics to determine if it's an NPU
        name = hardware_data.get("name", "").lower()
        architecture = hardware_data.get("architecture", "").lower()

        npu_indicators = ["npu", "ascend", "kunlun", "cambricon", "graphcore"]
        return any(
            indicator in name or indicator in architecture
            for indicator in npu_indicators
        )

    def save_hardware_spec(
        self,
        hardware_id: str,
        hardware_data: Dict[str, Any],
        profile_name: Optional[str] = None,
    ) -> ValidationResult:
        """Save a hardware specification to a custom profile.

        Args:
            hardware_id: Hardware identifier
            hardware_data: Hardware specification data
            profile_name: Name of profile to save to (creates new if None)

        Returns:
            ValidationResult indicating success/failure
        """
        # Validate first
        validation_result = self.validate_hardware_spec(hardware_id, hardware_data)
        if not validation_result.is_valid:
            return validation_result

        # Record edit history
        self._record_edit(hardware_id, "save", hardware_data)

        # Determine profile name
        if not profile_name:
            profile_name = f"custom_hardware_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Create profile structure
        if self._is_npu_spec(hardware_data):
            profile_data = {"npus": {hardware_id: hardware_data}}
        else:
            profile_data = {"gpus": {hardware_id: hardware_data}}

        return self.config_manager.save_custom_profile(profile_name, profile_data)

    def delete_hardware_spec(self, hardware_id: str) -> ValidationResult:
        """Delete a hardware specification from custom profiles.

        Args:
            hardware_id: Hardware identifier to delete

        Returns:
            ValidationResult indicating success/failure
        """
        result = ValidationResult(is_valid=True)

        # Check if hardware exists in custom profiles
        custom_profiles = self.config_manager.list_custom_profiles()
        found_in_profiles = []

        for profile in custom_profiles:
            try:
                profile_data = self.config_manager._load_config_file(profile["path"])
                if ("gpus" in profile_data and hardware_id in profile_data["gpus"]) or (
                    "npus" in profile_data and hardware_id in profile_data["npus"]
                ):
                    found_in_profiles.append(profile["name"])
            except Exception:
                continue

        if not found_in_profiles:
            result.add_error(f"Hardware {hardware_id} not found in any custom profiles")
            return result

        # Record edit history
        self._record_edit(hardware_id, "delete", None)

        # Remove from profiles
        for profile_name in found_in_profiles:
            try:
                profile_path = (
                    self.config_manager.custom_config_dir / f"{profile_name}.yaml"
                )
                if not profile_path.exists():
                    profile_path = (
                        self.config_manager.custom_config_dir / f"{profile_name}.json"
                    )

                if profile_path.exists():
                    profile_data = self.config_manager._load_config_file(
                        str(profile_path)
                    )

                    # Remove hardware from profile
                    if "gpus" in profile_data and hardware_id in profile_data["gpus"]:
                        del profile_data["gpus"][hardware_id]
                    if "npus" in profile_data and hardware_id in profile_data["npus"]:
                        del profile_data["npus"][hardware_id]

                    # Save updated profile
                    format_type = "yaml" if profile_path.suffix == ".yaml" else "json"
                    save_result = self.config_manager.save_custom_profile(
                        profile_name, profile_data, format_type
                    )

                    if save_result.is_valid:
                        result.add_recommendation(
                            f"Removed {hardware_id} from profile {profile_name}"
                        )
                    else:
                        result.errors.extend(save_result.errors)

            except Exception as e:
                result.add_error(
                    f"Failed to remove {hardware_id} from profile {profile_name}: {e}"
                )

        return result

    def update_hardware_field(
        self,
        hardware_id: str,
        field_path: str,
        new_value: Any,
        profile_name: Optional[str] = None,
    ) -> ValidationResult:
        """Update a specific field in a hardware specification.

        Args:
            hardware_id: Hardware identifier
            field_path: Dot-separated path to field (e.g., "tensor_performance.fp16_tensor")
            new_value: New value for the field
            profile_name: Profile containing the hardware (auto-detect if None)

        Returns:
            ValidationResult indicating success/failure
        """
        result = ValidationResult(is_valid=True)

        # Get current hardware specification
        current_spec = self.config_manager.get_hardware_specs(hardware_id)
        if not current_spec:
            result.add_error(f"Hardware {hardware_id} not found")
            return result

        # Find the profile containing this hardware
        if not profile_name:
            custom_profiles = self.config_manager.list_custom_profiles()
            for profile in custom_profiles:
                try:
                    profile_data = self.config_manager._load_config_file(
                        profile["path"]
                    )
                    if (
                        "gpus" in profile_data and hardware_id in profile_data["gpus"]
                    ) or (
                        "npus" in profile_data and hardware_id in profile_data["npus"]
                    ):
                        profile_name = profile["name"]
                        break
                except Exception:
                    continue

        if not profile_name:
            result.add_error(
                f"Hardware {hardware_id} not found in any editable profile"
            )
            return result

        # Load profile data
        try:
            profile_path = (
                self.config_manager.custom_config_dir / f"{profile_name}.yaml"
            )
            if not profile_path.exists():
                profile_path = (
                    self.config_manager.custom_config_dir / f"{profile_name}.json"
                )

            profile_data = self.config_manager._load_config_file(str(profile_path))

            # Find hardware data
            hardware_data = None
            if "gpus" in profile_data and hardware_id in profile_data["gpus"]:
                hardware_data = profile_data["gpus"][hardware_id]
            elif "npus" in profile_data and hardware_id in profile_data["npus"]:
                hardware_data = profile_data["npus"][hardware_id]

            if not hardware_data:
                result.add_error(
                    f"Hardware {hardware_id} not found in profile {profile_name}"
                )
                return result

            # Update the field
            self._set_nested_field(hardware_data, field_path, new_value)

            # Validate updated specification
            validation_result = self.validate_hardware_spec(hardware_id, hardware_data)
            if not validation_result.is_valid:
                return validation_result

            # Record edit history
            self._record_edit(
                hardware_id, "update_field", {"field": field_path, "value": new_value}
            )

            # Save updated profile
            format_type = "yaml" if profile_path.suffix == ".yaml" else "json"
            return self.config_manager.save_custom_profile(
                profile_name, profile_data, format_type
            )

        except Exception as e:
            result.add_error(f"Failed to update field {field_path}: {e}")
            return result

    def _set_nested_field(self, data: Dict[str, Any], field_path: str, value: Any):
        """Set a nested field value using dot notation."""
        keys = field_path.split(".")
        current = data

        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]

        current[keys[-1]] = value

    def get_edit_history(
        self, hardware_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get edit history for hardware specifications.

        Args:
            hardware_id: Optional hardware ID to filter history

        Returns:
            List of edit history entries
        """
        if hardware_id:
            return [
                entry
                for entry in self._edit_history
                if entry["hardware_id"] == hardware_id
            ]
        return self._edit_history.copy()

    def _record_edit(self, hardware_id: str, action: str, data: Any):
        """Record an edit action in history."""
        self._edit_history.append(
            {
                "hardware_id": hardware_id,
                "action": action,
                "data": data,
                "timestamp": datetime.now().isoformat(),
                "user": "system",  # Could be enhanced to track actual users
            }
        )

        # Keep only last 100 entries
        if len(self._edit_history) > 100:
            self._edit_history = self._edit_history[-100:]

    def export_hardware_spec(
        self, hardware_id: str, format: str = "yaml"
    ) -> Optional[str]:
        """Export a hardware specification to string format.

        Args:
            hardware_id: Hardware identifier to export
            format: Export format ('yaml' or 'json')

        Returns:
            Formatted hardware specification string or None if not found
        """
        spec = self.config_manager.get_hardware_specs(hardware_id)
        if not spec:
            return None

        # Convert to dictionary format
        spec_dict = {
            "name": spec.name,
            "architecture": spec.architecture,
            "form_factor": spec.form_factor,
            "year": spec.year,
            "memory_size_gb": spec.memory_size_gb,
            "memory_type": spec.memory_type,
            "memory_bandwidth_gbps": spec.memory_bandwidth_gbps,
            "l2_cache_mb": spec.l2_cache_mb,
            "tdp_watts": spec.tdp_watts,
            "manufacturing_process": spec.manufacturing_process,
            "interconnect": spec.interconnect,
            "tensor_performance": spec.tensor_performance,
            "vector_performance": spec.vector_performance,
        }

        # Remove None values
        spec_dict = {k: v for k, v in spec_dict.items() if v is not None}

        if format == "yaml":
            return yaml.dump(
                {hardware_id: spec_dict}, default_flow_style=False, indent=2
            )
        else:
            return json.dumps({hardware_id: spec_dict}, indent=2)

    def import_hardware_spec(
        self, spec_string: str, format: str = "yaml"
    ) -> ValidationResult:
        """Import a hardware specification from string format.

        Args:
            spec_string: Hardware specification in string format
            format: Import format ('yaml' or 'json')

        Returns:
            ValidationResult indicating success/failure
        """
        result = ValidationResult(is_valid=True)

        try:
            if format == "yaml":
                spec_data = yaml.safe_load(spec_string)
            else:
                spec_data = json.loads(spec_string)

            if not isinstance(spec_data, dict):
                result.add_error("Invalid specification format")
                return result

            # Validate and save each hardware specification
            for hardware_id, hardware_data in spec_data.items():
                validation_result = self.validate_hardware_spec(
                    hardware_id, hardware_data
                )
                if not validation_result.is_valid:
                    result.errors.extend(
                        [
                            f"{hardware_id}: {error}"
                            for error in validation_result.errors
                        ]
                    )
                    result.warnings.extend(
                        [
                            f"{hardware_id}: {warning}"
                            for warning in validation_result.warnings
                        ]
                    )
                else:
                    save_result = self.save_hardware_spec(hardware_id, hardware_data)
                    if save_result.is_valid:
                        result.add_recommendation(
                            f"Successfully imported {hardware_id}"
                        )
                    else:
                        result.errors.extend(save_result.errors)

        except Exception as e:
            result.add_error(f"Failed to parse specification: {e}")

        return result

    def get_hardware_comparison(self, hardware_ids: List[str]) -> Dict[str, Any]:
        """Get a comparison of multiple hardware specifications.

        Args:
            hardware_ids: List of hardware IDs to compare

        Returns:
            Dictionary with comparison data
        """
        comparison = {"hardware": {}, "fields": {}, "summary": {}}

        specs = []
        for hardware_id in hardware_ids:
            spec = self.config_manager.get_hardware_specs(hardware_id)
            if spec:
                specs.append(spec)
                comparison["hardware"][hardware_id] = {
                    "name": spec.name,
                    "type": spec.type.value,
                    "memory_size_gb": spec.memory_size_gb,
                    "memory_bandwidth_gbps": spec.memory_bandwidth_gbps,
                    "peak_flops": spec.peak_flops,
                }

        if not specs:
            return comparison

        # Compare key fields
        fields_to_compare = [
            "memory_size_gb",
            "memory_bandwidth_gbps",
            "l2_cache_mb",
            "tdp_watts",
            "year",
        ]

        for field in fields_to_compare:
            values = []
            for spec in specs:
                value = getattr(spec, field, None)
                if value is not None:
                    values.append(value)

            if values:
                comparison["fields"][field] = {
                    "min": min(values),
                    "max": max(values),
                    "avg": sum(values) / len(values) if values else 0,
                }

        # Summary statistics
        comparison["summary"] = {
            "total_hardware": len(specs),
            "gpu_count": sum(1 for spec in specs if spec.type == HardwareType.GPU),
            "npu_count": sum(1 for spec in specs if spec.type == HardwareType.NPU),
            "total_memory_gb": sum(spec.memory_size_gb for spec in specs),
            "avg_memory_bandwidth": sum(spec.memory_bandwidth_gbps for spec in specs)
            / len(specs),
        }

        return comparison

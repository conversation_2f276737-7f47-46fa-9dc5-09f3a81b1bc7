"""Hardware configuration validation utilities."""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml

from .models import HardwareSpec, HardwareType, ValidationResult

logger = logging.getLogger(__name__)


class HardwareConfigValidator:
    """Validator for hardware configuration files and data."""

    # Required fields for hardware specifications
    REQUIRED_FIELDS = {
        "name": str,
        "memory_size_gb": (int, float),
        "memory_bandwidth_gbps": (int, float),
    }

    # Optional fields with expected types
    OPTIONAL_FIELDS = {
        "architecture": str,
        "form_factor": str,
        "year": int,
        "memory_type": str,
        "l2_cache_mb": (int, float),
        "tdp_watts": (int, float),
        "manufacturing_process": str,
        "interconnect": str,
        "tensor_cores_total": int,
        "tensor_performance": dict,
        "vector_performance": dict,
    }

    # Valid precision types
    VALID_PRECISIONS = {
        "fp64",
        "fp32",
        "fp16",
        "bf16",
        "tf32",
        "fp8",
        "fp6",
        "fp4",
        "int32",
        "int8",
    }

    def validate_hardware_config_file(
        self, config_file: Union[str, Path]
    ) -> ValidationResult:
        """Validate a hardware configuration YAML file.

        Args:
            config_file: Path to the YAML configuration file.

        Returns:
            ValidationResult with validation details.
        """
        result = ValidationResult(is_valid=True)

        try:
            with open(config_file, "r") as f:
                config_data = yaml.safe_load(f)
        except FileNotFoundError:
            result.add_error(f"Configuration file not found: {config_file}")
            return result
        except yaml.YAMLError as e:
            result.add_error(f"Invalid YAML format: {e}")
            return result
        except Exception as e:
            result.add_error(f"Failed to read configuration file: {e}")
            return result

        return self.validate_hardware_config_data(config_data)

    def validate_hardware_config_data(
        self, config_data: Dict[str, Any]
    ) -> ValidationResult:
        """Validate hardware configuration data structure.

        Args:
            config_data: Dictionary containing hardware configuration data.

        Returns:
            ValidationResult with validation details.
        """
        result = ValidationResult(is_valid=True)

        if not isinstance(config_data, dict):
            result.add_error("Configuration data must be a dictionary")
            return result

        # Validate top-level structure
        valid_sections = {"gpus", "npus"}
        found_sections = set(config_data.keys())

        if not found_sections.intersection(valid_sections):
            result.add_error("Configuration must contain 'gpus' or 'npus' section")
            return result

        invalid_sections = found_sections - valid_sections
        if invalid_sections:
            result.add_warning(f"Unknown sections found: {', '.join(invalid_sections)}")

        # Validate GPU section
        if "gpus" in config_data:
            gpu_result = self._validate_hardware_section(config_data["gpus"], "GPU")
            result.errors.extend(gpu_result.errors)
            result.warnings.extend(gpu_result.warnings)
            result.recommendations.extend(gpu_result.recommendations)
            if not gpu_result.is_valid:
                result.is_valid = False

        # Validate NPU section
        if "npus" in config_data:
            npu_result = self._validate_hardware_section(config_data["npus"], "NPU")
            result.errors.extend(npu_result.errors)
            result.warnings.extend(npu_result.warnings)
            result.recommendations.extend(npu_result.recommendations)
            if not npu_result.is_valid:
                result.is_valid = False

        return result

    def validate_hardware_spec(self, hardware_spec: HardwareSpec) -> ValidationResult:
        """Validate a HardwareSpec object.

        Args:
            hardware_spec: HardwareSpec object to validate.

        Returns:
            ValidationResult with validation details.
        """
        result = ValidationResult(is_valid=True)

        # Validate basic fields
        if not hardware_spec.id:
            result.add_error("Hardware ID cannot be empty")

        if not hardware_spec.name:
            result.add_error("Hardware name cannot be empty")

        if hardware_spec.memory_size_gb <= 0:
            result.add_error(f"Invalid memory size: {hardware_spec.memory_size_gb}GB")

        if hardware_spec.memory_bandwidth_gbps <= 0:
            result.add_error(
                f"Invalid memory bandwidth: {hardware_spec.memory_bandwidth_gbps}GB/s"
            )

        # Validate performance data
        if (
            not hardware_spec.peak_flops
            and not hardware_spec.tensor_performance
            and not hardware_spec.vector_performance
        ):
            result.add_error("Hardware must have performance specifications")

        # Validate precision support
        if not hardware_spec.supported_precisions:
            result.add_warning("No supported precisions detected")

        # Check for reasonable values
        if hardware_spec.memory_size_gb > 1000:
            result.add_warning(
                f"Very large memory size: {hardware_spec.memory_size_gb}GB"
            )

        if hardware_spec.memory_bandwidth_gbps > 10000:
            result.add_warning(
                f"Very high memory bandwidth: {hardware_spec.memory_bandwidth_gbps}GB/s"
            )

        # Recommendations
        if hardware_spec.is_tensor_core_capable():
            result.add_recommendation(
                "Hardware supports tensor cores - consider mixed precision training"
            )

        if (
            hardware_spec.type == HardwareType.GPU
            and hardware_spec.memory_size_gb >= 80
        ):
            result.add_recommendation(
                "High-memory GPU suitable for large model training"
            )

        return result

    def _validate_hardware_section(
        self, hardware_section: Dict[str, Any], section_type: str
    ) -> ValidationResult:
        """Validate a hardware section (GPUs or NPUs).

        Args:
            hardware_section: Dictionary containing hardware specifications.
            section_type: Type of hardware section ('GPU' or 'NPU').

        Returns:
            ValidationResult with validation details.
        """
        result = ValidationResult(is_valid=True)

        if not isinstance(hardware_section, dict):
            result.add_error(f"{section_type} section must be a dictionary")
            return result

        if not hardware_section:
            result.add_warning(f"{section_type} section is empty")
            return result

        for hardware_id, hardware_data in hardware_section.items():
            hardware_result = self._validate_single_hardware(
                hardware_id, hardware_data, section_type
            )
            result.errors.extend(hardware_result.errors)
            result.warnings.extend(hardware_result.warnings)
            result.recommendations.extend(hardware_result.recommendations)
            if not hardware_result.is_valid:
                result.is_valid = False

        return result

    def _validate_single_hardware(
        self, hardware_id: str, hardware_data: Dict[str, Any], section_type: str
    ) -> ValidationResult:
        """Validate a single hardware specification.

        Args:
            hardware_id: Hardware identifier.
            hardware_data: Hardware specification data.
            section_type: Type of hardware ('GPU' or 'NPU').

        Returns:
            ValidationResult with validation details.
        """
        result = ValidationResult(is_valid=True)

        if not isinstance(hardware_data, dict):
            result.add_error(
                f"{section_type} {hardware_id} specification must be a dictionary"
            )
            return result

        # Check required fields
        for field, expected_type in self.REQUIRED_FIELDS.items():
            if field not in hardware_data:
                result.add_error(
                    f"{section_type} {hardware_id} missing required field: {field}"
                )
            else:
                value = hardware_data[field]
                if not isinstance(value, expected_type):
                    result.add_error(
                        f"{section_type} {hardware_id} field '{field}' has wrong type: expected {expected_type}, got {type(value)}"
                    )
                elif isinstance(value, (int, float)) and value <= 0:
                    result.add_error(
                        f"{section_type} {hardware_id} field '{field}' must be positive: {value}"
                    )

        # Check optional fields
        for field, expected_type in self.OPTIONAL_FIELDS.items():
            if field in hardware_data:
                value = hardware_data[field]
                if value is not None and not isinstance(value, expected_type):
                    result.add_warning(
                        f"{section_type} {hardware_id} field '{field}' has unexpected type: expected {expected_type}, got {type(value)}"
                    )

        # Validate performance data
        if "tensor_performance" in hardware_data:
            perf_result = self._validate_performance_data(
                hardware_data["tensor_performance"],
                f"{section_type} {hardware_id} tensor_performance",
            )
            result.errors.extend(perf_result.errors)
            result.warnings.extend(perf_result.warnings)
            if not perf_result.is_valid:
                result.is_valid = False

        if "vector_performance" in hardware_data:
            perf_result = self._validate_performance_data(
                hardware_data["vector_performance"],
                f"{section_type} {hardware_id} vector_performance",
            )
            result.errors.extend(perf_result.errors)
            result.warnings.extend(perf_result.warnings)
            if not perf_result.is_valid:
                result.is_valid = False

        # Check for unknown fields
        known_fields = set(self.REQUIRED_FIELDS.keys()) | set(
            self.OPTIONAL_FIELDS.keys()
        )
        unknown_fields = set(hardware_data.keys()) - known_fields
        if unknown_fields:
            result.add_warning(
                f"{section_type} {hardware_id} has unknown fields: {', '.join(unknown_fields)}"
            )

        return result

    def _validate_performance_data(
        self, performance_data: Dict[str, Any], context: str
    ) -> ValidationResult:
        """Validate performance data dictionary.

        Args:
            performance_data: Performance data dictionary.
            context: Context string for error messages.

        Returns:
            ValidationResult with validation details.
        """
        result = ValidationResult(is_valid=True)

        if not isinstance(performance_data, dict):
            result.add_error(f"{context} must be a dictionary")
            return result

        for precision, value in performance_data.items():
            # Extract base precision name (remove _tensor suffix)
            base_precision = precision.replace("_tensor", "")

            if base_precision not in self.VALID_PRECISIONS:
                result.add_warning(f"{context} has unknown precision: {precision}")

            if value is not None:
                if not isinstance(value, (int, float)):
                    result.add_error(
                        f"{context} precision '{precision}' has non-numeric value: {value}"
                    )
                elif value < 0:
                    result.add_error(
                        f"{context} precision '{precision}' has negative value: {value}"
                    )
                elif value == 0:
                    result.add_warning(
                        f"{context} precision '{precision}' has zero performance"
                    )

        return result

    def generate_validation_report(self, validation_result: ValidationResult) -> str:
        """Generate a human-readable validation report.

        Args:
            validation_result: ValidationResult to generate report for.

        Returns:
            Formatted validation report string.
        """
        report_lines = []

        if validation_result.is_valid:
            report_lines.append("✅ Hardware configuration is valid")
        else:
            report_lines.append("❌ Hardware configuration has errors")

        if validation_result.errors:
            report_lines.append("\n🚨 Errors:")
            for error in validation_result.errors:
                report_lines.append(f"  • {error}")

        if validation_result.warnings:
            report_lines.append("\n⚠️  Warnings:")
            for warning in validation_result.warnings:
                report_lines.append(f"  • {warning}")

        if validation_result.recommendations:
            report_lines.append("\n💡 Recommendations:")
            for recommendation in validation_result.recommendations:
                report_lines.append(f"  • {recommendation}")

        return "\n".join(report_lines)


def validate_hardware_config_file(config_file: Union[str, Path]) -> ValidationResult:
    """Convenience function to validate a hardware configuration file.

    Args:
        config_file: Path to the YAML configuration file.

    Returns:
        ValidationResult with validation details.
    """
    validator = HardwareConfigValidator()
    return validator.validate_hardware_config_file(config_file)


def validate_hardware_spec(hardware_spec: HardwareSpec) -> ValidationResult:
    """Convenience function to validate a HardwareSpec object.

    Args:
        hardware_spec: HardwareSpec object to validate.

    Returns:
        ValidationResult with validation details.
    """
    validator = HardwareConfigValidator()
    return validator.validate_hardware_spec(hardware_spec)

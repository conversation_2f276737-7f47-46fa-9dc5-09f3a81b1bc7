"""Operator timing service for comprehensive hardware-aware timing analysis."""

import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple

from ..core.operators import BaseOperator
from .models import HardwareSpec

logger = logging.getLogger(__name__)


@dataclass
class OperatorTiming:
    """Comprehensive timing analysis for an operator on specific hardware."""

    operator_name: str
    hardware_id: str
    compute_time_ms: float
    memory_time_ms: float
    execution_time_ms: float  # max(compute_time, memory_time)
    bottleneck_type: str  # 'compute' or 'memory'
    utilization_percent: float
    operational_intensity: float  # FLOP/Byte ratio
    flops: int
    memory_movement_bytes: int
    precision_overhead_factor: float
    tensor_core_utilization: bool
    optimization_opportunities: List[str] = field(default_factory=list)


@dataclass
class BottleneckAnalysis:
    """Analysis of bottlenecks across multiple operators."""

    compute_bound_operators: List[str] = field(default_factory=list)
    memory_bound_operators: List[str] = field(default_factory=list)
    compute_utilization_avg: float = 0.0
    memory_utilization_avg: float = 0.0
    overall_bottleneck: str = "balanced"  # 'compute', 'memory', or 'balanced'
    recommendations: List[str] = field(default_factory=list)


@dataclass
class OptimizationSuggestion:
    """Hardware-specific optimization suggestion for an operator."""

    operator_name: str
    suggestion_type: str  # 'precision', 'tensor_core', 'memory_layout', 'batching'
    description: str
    expected_improvement_percent: float
    implementation_complexity: str  # 'low', 'medium', 'high'
    hardware_specific: bool = True


@dataclass
class TimingComparison:
    """Comparison of operator timing across multiple hardware platforms."""

    operators: List[str]
    hardware_platforms: List[str]
    timing_matrix: Dict[str, Dict[str, OperatorTiming]] = field(
        default_factory=dict
    )  # operator -> hardware -> timing
    performance_rankings: Dict[str, List[str]] = field(
        default_factory=dict
    )  # operator -> ranked hardware list
    recommendations: List[str] = field(default_factory=list)


class OperatorTimingService:
    """Service for computing hardware-aware operator timing and analysis."""

    def __init__(self):
        """Initialize the timing service."""
        self._timing_cache: Dict[str, OperatorTiming] = {}
        self._optimization_cache: Dict[str, List[OptimizationSuggestion]] = {}

    def compute_operator_timing(
        self, operator: BaseOperator, hardware: HardwareSpec, **kwargs
    ) -> OperatorTiming:
        """Compute comprehensive timing analysis for an operator on specific hardware.

        Args:
            operator: The operator to analyze
            hardware: Hardware specifications
            **kwargs: Additional parameters for operator computation

        Returns:
            OperatorTiming with detailed timing breakdown
        """
        # Generate cache key
        cache_key = self._generate_timing_cache_key(operator, hardware, kwargs)
        if cache_key in self._timing_cache:
            return self._timing_cache[cache_key]

        try:
            # Compute basic metrics
            flops = operator.compute_flops(**kwargs)
            memory_movement_bytes = operator.compute_memory_access_bytes(**kwargs)

            # Compute timing components
            compute_time_ms = operator._estimate_compute_time(flops, hardware)
            memory_time_ms = operator._estimate_memory_time(
                memory_movement_bytes, hardware
            )
            execution_time_ms = max(compute_time_ms, memory_time_ms)

            # Determine bottleneck
            bottleneck_type = (
                "compute" if compute_time_ms > memory_time_ms else "memory"
            )

            # Calculate utilization
            utilization_percent = operator._estimate_utilization(
                flops, memory_movement_bytes, hardware
            )

            # Calculate operational intensity
            operational_intensity = (
                flops / memory_movement_bytes
                if memory_movement_bytes > 0
                else float("inf")
            )

            # Detect tensor core utilization
            tensor_core_utilization = operator._detect_tensor_core_utilization(hardware)

            # Calculate precision overhead
            precision_overhead_factor = operator._calculate_mixed_precision_overhead(
                hardware
            )

            # Generate optimization opportunities
            optimization_opportunities = self._identify_optimization_opportunities(
                operator,
                hardware,
                bottleneck_type,
                tensor_core_utilization,
                operational_intensity,
            )

            timing = OperatorTiming(
                operator_name=operator.name,
                hardware_id=hardware.id,
                compute_time_ms=compute_time_ms,
                memory_time_ms=memory_time_ms,
                execution_time_ms=execution_time_ms,
                bottleneck_type=bottleneck_type,
                utilization_percent=utilization_percent,
                operational_intensity=operational_intensity,
                flops=flops,
                memory_movement_bytes=memory_movement_bytes,
                precision_overhead_factor=precision_overhead_factor,
                tensor_core_utilization=tensor_core_utilization,
                optimization_opportunities=optimization_opportunities,
            )

            # Cache the result
            self._timing_cache[cache_key] = timing
            return timing

        except Exception as e:
            logger.error(
                f"Failed to compute timing for {operator.name} on {hardware.id}: {e}"
            )
            # Return a default timing with error indication
            return OperatorTiming(
                operator_name=operator.name,
                hardware_id=hardware.id,
                compute_time_ms=0.0,
                memory_time_ms=0.0,
                execution_time_ms=0.0,
                bottleneck_type="unknown",
                utilization_percent=0.0,
                operational_intensity=0.0,
                flops=0,
                memory_movement_bytes=0,
                precision_overhead_factor=1.0,
                tensor_core_utilization=False,
                optimization_opportunities=[f"Error in timing calculation: {str(e)}"],
            )

    def analyze_bottlenecks(
        self, operators: List[BaseOperator], hardware: HardwareSpec, **kwargs
    ) -> BottleneckAnalysis:
        """Analyze bottlenecks across multiple operators.

        Args:
            operators: List of operators to analyze
            hardware: Hardware specifications
            **kwargs: Additional parameters for operator computation

        Returns:
            BottleneckAnalysis with comprehensive bottleneck information
        """
        compute_bound = []
        memory_bound = []
        compute_utilizations = []
        memory_utilizations = []

        for operator in operators:
            timing = self.compute_operator_timing(operator, hardware, **kwargs)

            if timing.bottleneck_type == "compute":
                compute_bound.append(timing.operator_name)
                compute_utilizations.append(timing.utilization_percent)
            else:
                memory_bound.append(timing.operator_name)
                memory_utilizations.append(timing.utilization_percent)

        # Calculate average utilizations
        compute_utilization_avg = (
            sum(compute_utilizations) / len(compute_utilizations)
            if compute_utilizations
            else 0.0
        )
        memory_utilization_avg = (
            sum(memory_utilizations) / len(memory_utilizations)
            if memory_utilizations
            else 0.0
        )

        # Determine overall bottleneck
        if len(compute_bound) > len(memory_bound) * 1.5:
            overall_bottleneck = "compute"
        elif len(memory_bound) > len(compute_bound) * 1.5:
            overall_bottleneck = "memory"
        else:
            overall_bottleneck = "balanced"

        # Generate recommendations
        recommendations = self._generate_bottleneck_recommendations(
            overall_bottleneck,
            compute_utilization_avg,
            memory_utilization_avg,
            hardware,
        )

        return BottleneckAnalysis(
            compute_bound_operators=compute_bound,
            memory_bound_operators=memory_bound,
            compute_utilization_avg=compute_utilization_avg,
            memory_utilization_avg=memory_utilization_avg,
            overall_bottleneck=overall_bottleneck,
            recommendations=recommendations,
        )

    def suggest_optimizations(
        self, timing_results: List[OperatorTiming], hardware: HardwareSpec
    ) -> List[OptimizationSuggestion]:
        """Generate hardware-specific optimization suggestions.

        Args:
            timing_results: List of timing results to analyze
            hardware: Hardware specifications

        Returns:
            List of optimization suggestions sorted by expected improvement
        """
        cache_key = f"{hardware.id}_{len(timing_results)}_{hash(tuple(t.operator_name for t in timing_results))}"
        if cache_key in self._optimization_cache:
            return self._optimization_cache[cache_key]

        suggestions = []

        for timing in timing_results:
            # Precision optimization suggestions
            if timing.precision_overhead_factor > 1.05:
                suggestions.append(
                    OptimizationSuggestion(
                        operator_name=timing.operator_name,
                        suggestion_type="precision",
                        description=f"Consider using native {hardware.supported_precisions[0] if hardware.supported_precisions else 'fp16'} precision to reduce conversion overhead",
                        expected_improvement_percent=(
                            timing.precision_overhead_factor - 1.0
                        )
                        * 100,
                        implementation_complexity="low",
                    )
                )

            # Tensor core optimization suggestions
            if not timing.tensor_core_utilization and hardware.is_tensor_core_capable():
                if timing.bottleneck_type == "compute":
                    suggestions.append(
                        OptimizationSuggestion(
                            operator_name=timing.operator_name,
                            suggestion_type="tensor_core",
                            description="Enable tensor core utilization by using supported precisions (fp16, bf16, tf32)",
                            expected_improvement_percent=30.0,
                            implementation_complexity="medium",
                        )
                    )

            # Memory optimization suggestions
            if (
                timing.bottleneck_type == "memory"
                and timing.operational_intensity < 1.0
            ):
                suggestions.append(
                    OptimizationSuggestion(
                        operator_name=timing.operator_name,
                        suggestion_type="memory_layout",
                        description="Optimize memory layout and access patterns to improve cache utilization",
                        expected_improvement_percent=15.0,
                        implementation_complexity="high",
                    )
                )

            # Batching optimization suggestions
            if timing.utilization_percent < 50.0:
                suggestions.append(
                    OptimizationSuggestion(
                        operator_name=timing.operator_name,
                        suggestion_type="batching",
                        description="Increase batch size to improve hardware utilization",
                        expected_improvement_percent=min(
                            50.0 - timing.utilization_percent, 25.0
                        ),
                        implementation_complexity="low",
                    )
                )

        # Sort by expected improvement
        suggestions.sort(key=lambda x: x.expected_improvement_percent, reverse=True)

        # Cache the results
        self._optimization_cache[cache_key] = suggestions
        return suggestions

    def compare_across_hardware(
        self, operators: List[BaseOperator], hardware_list: List[HardwareSpec], **kwargs
    ) -> TimingComparison:
        """Compare operator timing across multiple hardware platforms.

        Args:
            operators: List of operators to compare
            hardware_list: List of hardware specifications
            **kwargs: Additional parameters for operator computation

        Returns:
            TimingComparison with cross-hardware analysis
        """
        timing_matrix = {}
        performance_rankings = {}

        # Compute timing for each operator on each hardware
        for operator in operators:
            timing_matrix[operator.name] = {}
            hardware_timings = []

            for hardware in hardware_list:
                timing = self.compute_operator_timing(operator, hardware, **kwargs)
                timing_matrix[operator.name][hardware.id] = timing
                hardware_timings.append((hardware.id, timing.execution_time_ms))

            # Rank hardware by performance (lower execution time is better)
            hardware_timings.sort(key=lambda x: x[1])
            performance_rankings[operator.name] = [
                hw_id for hw_id, _ in hardware_timings
            ]

        # Generate cross-hardware recommendations
        recommendations = self._generate_cross_hardware_recommendations(
            timing_matrix, hardware_list
        )

        return TimingComparison(
            operators=[op.name for op in operators],
            hardware_platforms=[hw.id for hw in hardware_list],
            timing_matrix=timing_matrix,
            performance_rankings=performance_rankings,
            recommendations=recommendations,
        )

    def _generate_timing_cache_key(
        self, operator: BaseOperator, hardware: HardwareSpec, kwargs: Dict
    ) -> str:
        """Generate cache key for timing results."""
        # Create a deterministic key from operator, hardware, and parameters
        param_str = "_".join(f"{k}:{v}" for k, v in sorted(kwargs.items()))
        return (
            f"{operator.name}_{hardware.id}_{operator.activation_precision}_{param_str}"
        )

    def _identify_optimization_opportunities(
        self,
        operator: BaseOperator,
        hardware: HardwareSpec,
        bottleneck_type: str,
        tensor_core_utilization: bool,
        operational_intensity: float,
    ) -> List[str]:
        """Identify optimization opportunities for an operator."""
        opportunities = []

        # Tensor core opportunities
        if not tensor_core_utilization and hardware.is_tensor_core_capable():
            opportunities.append(
                "Enable tensor core utilization with supported precisions"
            )

        # Memory optimization opportunities
        if bottleneck_type == "memory":
            if operational_intensity < 0.5:
                opportunities.append(
                    "Improve operational intensity through operator fusion"
                )
            opportunities.append("Optimize memory access patterns and data layout")

        # Compute optimization opportunities
        if bottleneck_type == "compute":
            opportunities.append(
                "Consider lower precision computation if accuracy allows"
            )
            if not tensor_core_utilization:
                opportunities.append("Utilize tensor cores for matrix operations")

        # Precision-specific opportunities
        if (
            hasattr(operator, "precision_overhead_factor")
            and operator._calculate_mixed_precision_overhead(hardware) > 1.05
        ):
            opportunities.append("Reduce mixed precision conversion overhead")

        return opportunities

    def _generate_bottleneck_recommendations(
        self,
        overall_bottleneck: str,
        compute_util: float,
        memory_util: float,
        hardware: HardwareSpec,
    ) -> List[str]:
        """Generate recommendations based on bottleneck analysis."""
        recommendations = []

        if overall_bottleneck == "compute":
            recommendations.append(
                "Workload is compute-bound - consider optimizing compute operations"
            )
            if hardware.is_tensor_core_capable():
                recommendations.append("Utilize tensor cores for matrix operations")
            recommendations.append(
                "Consider lower precision computation where possible"
            )

        elif overall_bottleneck == "memory":
            recommendations.append(
                "Workload is memory-bound - focus on memory optimizations"
            )
            recommendations.append("Improve data locality and cache utilization")
            recommendations.append("Consider operator fusion to reduce memory traffic")

        else:
            recommendations.append(
                "Workload is balanced - optimize both compute and memory aspects"
            )

        # Utilization-based recommendations
        if compute_util < 70.0:
            recommendations.append(
                "Low compute utilization - consider increasing batch size or model parallelism"
            )

        if memory_util < 70.0:
            recommendations.append(
                "Low memory utilization - consider optimizing memory access patterns"
            )

        return recommendations

    def _generate_cross_hardware_recommendations(
        self,
        timing_matrix: Dict[str, Dict[str, OperatorTiming]],
        hardware_list: List[HardwareSpec],
    ) -> List[str]:
        """Generate recommendations based on cross-hardware comparison."""
        recommendations = []

        # Find consistently best performing hardware
        hardware_scores = {hw.id: 0 for hw in hardware_list}

        for operator_name, hardware_timings in timing_matrix.items():
            # Sort by execution time (lower is better)
            sorted_timings = sorted(
                hardware_timings.items(), key=lambda x: x[1].execution_time_ms
            )

            # Award points based on ranking
            for i, (hw_id, _) in enumerate(sorted_timings):
                hardware_scores[hw_id] += len(hardware_list) - i

        # Find best overall hardware
        best_hardware = max(hardware_scores.items(), key=lambda x: x[1])
        recommendations.append(f"Overall best performing hardware: {best_hardware[0]}")

        # Identify hardware-specific strengths
        for hw in hardware_list:
            compute_bound_ops = []
            memory_bound_ops = []

            for op_name, hw_timings in timing_matrix.items():
                timing = hw_timings[hw.id]
                if timing.bottleneck_type == "compute":
                    compute_bound_ops.append(op_name)
                else:
                    memory_bound_ops.append(op_name)

            if len(compute_bound_ops) > len(memory_bound_ops):
                recommendations.append(
                    f"{hw.id} is well-suited for compute-intensive workloads"
                )
            elif len(memory_bound_ops) > len(compute_bound_ops):
                recommendations.append(
                    f"{hw.id} is well-suited for memory-intensive workloads"
                )

        return recommendations

    def clear_cache(self):
        """Clear timing and optimization caches."""
        self._timing_cache.clear()
        self._optimization_cache.clear()

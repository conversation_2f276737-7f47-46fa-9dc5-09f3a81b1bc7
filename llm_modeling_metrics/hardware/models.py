"""Data models for hardware specifications and validation."""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional


class HardwareType(Enum):
    """Hardware accelerator types."""

    GPU = "gpu"
    NPU = "npu"


class PrecisionType(Enum):
    """Supported precision types."""

    FP64 = "fp64"
    FP32 = "fp32"
    FP16 = "fp16"
    BF16 = "bf16"
    TF32 = "tf32"
    FP8 = "fp8"
    FP6 = "fp6"
    FP4 = "fp4"
    INT32 = "int32"
    INT8 = "int8"


@dataclass
class RooflineParams:
    """Parameters for roofline model generation."""

    peak_flops: Dict[str, float] = field(default_factory=dict)  # precision -> TFLOPS
    memory_bandwidth_gbps: float = 0.0
    l2_cache_mb: Optional[int] = None
    operational_intensity_range: tuple = (0.01, 1000.0)  # FLOP/Byte range


@dataclass
class HardwareSpec:
    """Hardware specification data model with validation."""

    # Basic identification
    id: str
    name: str
    type: HardwareType
    architecture: Optional[str] = None
    form_factor: Optional[str] = None
    year: Optional[int] = None

    # Memory specifications
    memory_size_gb: int = 0
    memory_type: Optional[str] = None
    memory_bandwidth_gbps: float = 0.0
    l2_cache_mb: Optional[int] = None

    # Performance specifications
    peak_flops: Dict[str, float] = field(default_factory=dict)  # precision -> TFLOPS
    tensor_performance: Dict[str, float] = field(
        default_factory=dict
    )  # precision -> TFLOPS
    vector_performance: Dict[str, float] = field(
        default_factory=dict
    )  # precision -> TFLOPS

    # Physical specifications
    tdp_watts: Optional[int] = None
    manufacturing_process: Optional[str] = None
    interconnect: Optional[str] = None

    # Derived properties
    supported_precisions: List[str] = field(default_factory=list)
    tensor_cores: Optional[int] = None
    roofline_params: Optional[RooflineParams] = None

    def __post_init__(self):
        """Post-initialization validation and setup."""
        self._validate_required_fields()
        self._setup_supported_precisions()
        self._setup_peak_flops()
        self._setup_roofline_params()

    def _validate_required_fields(self):
        """Validate that required fields are present and valid."""
        if not self.id:
            raise ValueError("Hardware ID is required")
        if not self.name:
            raise ValueError("Hardware name is required")
        if self.memory_size_gb <= 0:
            raise ValueError("Memory size must be positive")
        if self.memory_bandwidth_gbps <= 0:
            raise ValueError("Memory bandwidth must be positive")

    def _setup_supported_precisions(self):
        """Setup supported precisions list from performance data."""
        precisions = set()

        # Add precisions from tensor performance
        for precision in self.tensor_performance.keys():
            if (
                self.tensor_performance[precision] is not None
                and self.tensor_performance[precision] > 0
            ):
                precisions.add(precision.replace("_tensor", ""))

        # Add precisions from vector performance
        for precision in self.vector_performance.keys():
            if (
                self.vector_performance[precision] is not None
                and self.vector_performance[precision] > 0
            ):
                precisions.add(precision)

        self.supported_precisions = sorted(list(precisions))

    def _setup_peak_flops(self):
        """Setup peak FLOPS dictionary combining tensor and vector performance."""
        self.peak_flops = {}

        # Use tensor performance when available, fallback to vector
        for precision in self.supported_precisions:
            tensor_key = f"{precision}_tensor"

            # Prefer tensor performance
            if (
                tensor_key in self.tensor_performance
                and self.tensor_performance[tensor_key] is not None
            ):
                self.peak_flops[precision] = self.tensor_performance[tensor_key]
            # Fallback to vector performance
            elif (
                precision in self.vector_performance
                and self.vector_performance[precision] is not None
            ):
                self.peak_flops[precision] = self.vector_performance[precision]

    def _setup_roofline_params(self):
        """Setup roofline parameters for visualization."""
        self.roofline_params = RooflineParams(
            peak_flops=self.peak_flops.copy(),
            memory_bandwidth_gbps=self.memory_bandwidth_gbps,
            l2_cache_mb=self.l2_cache_mb,
        )

    def get_peak_flops(self, precision: str) -> float:
        """Get peak FLOPS for a specific precision in TFLOPS."""
        return self.peak_flops.get(precision, 0.0)

    def get_peak_flops_per_second(self, precision: str) -> float:
        """Get peak FLOPS per second for a specific precision."""
        return self.get_peak_flops(precision) * 1e12  # Convert TFLOPS to FLOPS

    def supports_precision(self, precision: str) -> bool:
        """Check if hardware supports a specific precision."""
        return precision in self.supported_precisions

    def get_memory_bandwidth_bps(self) -> float:
        """Get memory bandwidth in bytes per second."""
        return self.memory_bandwidth_gbps * 1e9  # Convert GB/s to B/s

    def is_tensor_core_capable(self) -> bool:
        """Check if hardware has tensor core capabilities."""
        return bool(
            self.tensor_performance
            and any(
                perf is not None and perf > 0
                for perf in self.tensor_performance.values()
            )
        )


@dataclass
class ValidationResult:
    """Result of hardware validation operations."""

    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)

    def add_error(self, message: str):
        """Add an error message."""
        self.errors.append(message)
        self.is_valid = False

    def add_warning(self, message: str):
        """Add a warning message."""
        self.warnings.append(message)

    def add_recommendation(self, message: str):
        """Add a recommendation message."""
        self.recommendations.append(message)

    def has_issues(self) -> bool:
        """Check if there are any errors or warnings."""
        return bool(self.errors or self.warnings)


@dataclass
class HardwareRecommendation:
    """Hardware recommendation based on workload analysis."""

    hardware_id: str
    hardware_name: str
    score: float  # 0-100 compatibility score
    reasons: List[str] = field(default_factory=list)
    estimated_performance: Optional[float] = None  # TFLOPS
    memory_utilization: Optional[float] = None  # Percentage
    cost_effectiveness: Optional[str] = None  # 'low', 'medium', 'high'


@dataclass
class WorkloadProfile:
    """Workload profile for hardware recommendation."""

    model_type: str  # 'dense', 'moe'
    batch_size: int
    sequence_length: int
    precision_requirements: List[str]
    memory_constraints: Optional[int] = None  # GB
    latency_requirements: Optional[float] = None  # ms
    throughput_requirements: Optional[float] = None  # tokens/s

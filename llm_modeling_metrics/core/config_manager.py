"""
Configuration manager for fetching and caching model configurations.
"""

import json
import os
import re
import time
from pathlib import Path
from typing import Any, Dict, Optional

import requests
from transformers import AutoConfig

from ..utils.caching import cached, get_cache_manager


class ConfigManager:
    """Manager for fetching and caching model configurations from HuggingFace."""

    def __init__(
        self,
        cache_dir: str = None,
        token: Optional[str] = None,
        cache_expiry_days: int = 30,
        request_timeout: int = 5,
        max_retries: int = 2,
    ):
        """
        Initialize configuration manager.

        Args:
            cache_dir: Directory for caching configurations
            token: HuggingFace API token
            cache_expiry_days: Number of days before cache expires
            request_timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
        """
        if cache_dir is None:
            cache_dir = os.path.expanduser("~/.modeling_llm/model_configs")
        self.cache_dir = Path(cache_dir)
        self.modeling_dir = self.cache_dir / "modeling"
        self.token = token or os.getenv("HF_TOKEN")
        self.cache_expiry_days = cache_expiry_days
        self.request_timeout = request_timeout
        self.max_retries = max_retries
        self.proxies = self._get_proxies()

        # Create directories if they don't exist
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.modeling_dir.mkdir(parents=True, exist_ok=True)

    def _get_proxies(self) -> Optional[Dict[str, str]]:
        """Get proxy configuration from environment variables."""
        http_proxy = os.getenv("HTTP_PROXY") or os.getenv("http_proxy")
        https_proxy = os.getenv("HTTPS_PROXY") or os.getenv("https_proxy")

        if http_proxy or https_proxy:
            return {"http": http_proxy, "https": https_proxy or http_proxy}
        return None

    def fetch_config(
        self, model_name: str, force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        Fetch and cache model configuration with retry logic.

        Args:
            model_name: Name of the model to fetch configuration for
            force_refresh: Whether to force refresh from remote

        Returns:
            Model configuration dictionary

        Raises:
            ConfigurationError: If configuration cannot be fetched after retries
        """
        cache_manager = get_cache_manager()
        cache_key = f"config_{model_name}"

        # Check cache first if not forcing refresh
        if not force_refresh:
            cached_config = cache_manager.config_cache.get(cache_key)
            if cached_config is not None:
                return cached_config

        # Fetch from remote with retry logic
        last_exception = None
        for attempt in range(self.max_retries):
            try:
                config = AutoConfig.from_pretrained(
                    model_name,
                    request_timeout=self.request_timeout,
                    token=self.token,
                    trust_remote_code=True,
                    proxies=self.proxies,
                )

                config_dict = config.to_dict()

                # Cache the configuration with TTL
                cache_manager.config_cache.put(
                    cache_key, config_dict, ttl=self.cache_expiry_days * 24 * 3600
                )

                # Also maintain file-based cache for backward compatibility
                cache_path = self.cache_dir / f"{model_name.replace('/', '_')}.json"
                try:
                    with open(cache_path, "w", encoding="utf-8") as f:
                        json.dump(config_dict, f, indent=2, ensure_ascii=False)
                except IOError as e:
                    print(f"Warning: Failed to cache config for {model_name}: {e}")

                return config_dict

            except Exception as e:
                last_exception = e
                if attempt < self.max_retries - 1:
                    wait_time = 2**attempt  # Exponential backoff
                    print(
                        f"Attempt {attempt + 1} failed for {model_name}: {e}. Retrying in {wait_time}s..."
                    )
                    time.sleep(wait_time)
                else:
                    print(f"All {self.max_retries} attempts failed for {model_name}")

        # Try to get from cache as fallback (even if expired)
        cached_config = cache_manager.config_cache.get(cache_key)
        if cached_config is not None:
            print(f"Using cached config for {model_name} due to fetch failure")
            return cached_config

        # If we have a file-based cached version (even expired), return it as fallback
        cache_path = self.cache_dir / f"{model_name.replace('/', '_')}.json"
        if cache_path.exists():
            try:
                with open(cache_path, "r", encoding="utf-8") as f:
                    print(
                        f"Using expired file cache for {model_name} due to fetch failure"
                    )
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                pass

        raise ConfigurationError(
            f"Failed to fetch configuration for {model_name}: {last_exception}"
        )

    def fetch_modeling_file(self, model_name: str) -> Optional[str]:
        """
        Fetch modeling file from HuggingFace repository.

        Args:
            model_name: Name of the model

        Returns:
            Path to the cached modeling file or None if not found
        """
        try:
            # Get model config to determine architecture
            config = self.fetch_config(model_name)
            if not config or "architectures" not in config:
                print(f"No architectures found in config for {model_name}")
                return None

            # Extract architecture name and create filename
            arch_name = config["architectures"][0]
            # Remove version numbers and 'ForCausalLM' suffix, convert to lowercase
            clean_arch = re.sub(
                r"v?\d+", "", arch_name.replace("ForCausalLM", ""), flags=re.IGNORECASE
            ).lower()
            filename = f"modeling_{clean_arch}.py"
            cache_path = self.modeling_dir / filename

            # Check if already cached
            if cache_path.exists():
                file_age_days = (time.time() - cache_path.stat().st_mtime) / 86400
                if file_age_days < self.cache_expiry_days:
                    return str(cache_path)

            # Fetch from HuggingFace with retry logic
            last_exception = None
            for attempt in range(self.max_retries):
                try:
                    headers = {}
                    if self.token:
                        headers["Authorization"] = f"Bearer {self.token}"

                    url = f"https://huggingface.co/{model_name}/resolve/main/{filename}"
                    response = requests.get(
                        url,
                        headers=headers,
                        proxies=self.proxies,
                        timeout=self.request_timeout,
                        stream=True,
                    )
                    response.raise_for_status()

                    # Save the file
                    with open(cache_path, "wb") as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)

                    return str(cache_path)

                except Exception as e:
                    last_exception = e
                    if attempt < self.max_retries - 1:
                        wait_time = 2**attempt
                        print(
                            f"Attempt {attempt + 1} failed to fetch modeling file for {model_name}: {e}. Retrying in {wait_time}s..."
                        )
                        time.sleep(wait_time)

            print(
                f"Failed to fetch modeling file for {model_name} after {self.max_retries} attempts: {last_exception}"
            )
            return None

        except Exception as e:
            print(f"Error fetching modeling file for {model_name}: {e}")
            return None

    def get_cached_config(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        Get configuration from cache without fetching from remote.

        Args:
            model_name: Name of the model

        Returns:
            Cached configuration or None if not found or expired
        """
        cache_path = self.cache_dir / f"{model_name.replace('/', '_')}.json"

        if not cache_path.exists():
            return None

        try:
            # Check if cache is still valid
            file_age_days = (time.time() - cache_path.stat().st_mtime) / 86400
            if file_age_days >= self.cache_expiry_days:
                return None

            with open(cache_path, "r", encoding="utf-8") as f:
                return json.load(f)

        except (json.JSONDecodeError, IOError) as e:
            print(f"Error reading cached config for {model_name}: {e}")
            return None

    def clear_cache(self, model_name: Optional[str] = None) -> None:
        """
        Clear cached configurations.

        Args:
            model_name: Specific model to clear cache for, or None to clear all
        """
        cache_manager = get_cache_manager()

        if model_name:
            # Clear from new cache system
            cache_key = f"config_{model_name}"
            cache_manager.config_cache.delete(cache_key)

            # Clear from file-based cache
            cache_path = self.cache_dir / f"{model_name.replace('/', '_')}.json"
            if cache_path.exists():
                cache_path.unlink()
        else:
            # Clear all cached configs from new cache system
            cache_manager.config_cache.clear()

            # Clear all file-based cached configs
            for cache_file in self.cache_dir.glob("*.json"):
                cache_file.unlink()

    def list_cached_models(self) -> list[str]:
        """
        List all models that have cached configurations.

        Returns:
            List of model names with cached configurations
        """
        cached_models = []
        for cache_file in self.cache_dir.glob("*.json"):
            model_name = cache_file.stem.replace("_", "/")
            cached_models.append(model_name)
        return cached_models


class ConfigurationError(Exception):
    """Raised when model configuration cannot be fetched or is invalid."""

    pass

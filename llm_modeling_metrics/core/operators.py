"""
Operator base classes for modeling LLM operations similar to Table 2 in the paper.
This provides a structured way to model different types of operations (Attention, MatMul, Communication, etc.)
with their computational and memory characteristics.
"""

import os
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

import yaml
from loguru import logger

# Mixed precision support constants and utilities
PRECISION_BYTES = {
    "fp32": 4,
    "fp16": 2,
    "bf16": 2,
    "int8": 1,
    "fp8": 1,
    "fp4": 0.5,  # Handled as packed format
}


@dataclass
class MixedPrecisionConfig:
    """Configuration for mixed precision settings."""

    weight_dtype: str = "bf16"
    activation_dtype: str = "bf16"
    kv_cache_dtype: str = "bf16"
    expert_parameter_dtype: str = "fp8"  # For MoE models
    attention_parameter_dtype: str = "bf16"
    grad_dtype: str = "fp16"
    optimizer_dtype: str = "fp32"

    def __post_init__(self):
        """Validate precision types."""
        valid_precisions = set(PRECISION_BYTES.keys())
        for field_name, value in self.__dict__.items():
            if value not in valid_precisions:
                raise UnsupportedPrecisionError(
                    f"Invalid precision '{value}' for {field_name}"
                )

    def __repr__(self):
        return (
            f"MixedPrecisionConfig(weight={self.weight_dtype}, activation={self.activation_dtype}, "
            f"kv_cache={self.kv_cache_dtype}, expert_param={self.expert_parameter_dtype}, "
            f"attention_param={self.attention_parameter_dtype}, grad={self.grad_dtype}, "
            f"optimizer={self.optimizer_dtype})"
        )


class UnsupportedPrecisionError(ValueError):
    """Raised when an unsupported precision type is specified."""

    pass


class IncompatiblePrecisionError(ValueError):
    """Raised when precision combinations are incompatible."""

    pass


def validate_precision_type(precision: str) -> None:
    """Validate that a precision type is supported."""
    if precision is None:
        return  # None is acceptable for optional parameters

    if not isinstance(precision, str):
        raise UnsupportedPrecisionError(
            f"Precision must be a string, got {type(precision).__name__}: {precision}"
        )

    if precision not in PRECISION_BYTES:
        raise UnsupportedPrecisionError(
            f"Unsupported precision '{precision}'. "
            f"Supported precisions: {list(PRECISION_BYTES.keys())}"
        )


def validate_precision_compatibility(precisions: Dict[str, str]) -> None:
    """Validate that precision combinations are compatible."""
    # Validate all precision types first
    for component, precision in precisions.items():
        validate_precision_type(precision)

    # Check for specific incompatible combinations
    _check_precision_compatibility_rules(precisions)


def _check_precision_compatibility_rules(precisions: Dict[str, str]) -> None:
    """Check specific precision compatibility rules."""
    # Rule 1: fp4 requires special handling and may not be compatible with all operations
    fp4_components = [comp for comp, prec in precisions.items() if prec == "fp4"]
    if fp4_components:
        # fp4 should only be used for expert parameters or weights, not activations
        invalid_fp4_usage = [
            comp
            for comp in fp4_components
            if "activation" in comp.lower() or "cache" in comp.lower()
        ]
        if invalid_fp4_usage:
            raise IncompatiblePrecisionError(
                f"fp4 precision is not compatible with activation or cache components: {invalid_fp4_usage}. "
                f"fp4 should only be used for weight/parameter storage."
            )

    # Rule 2: Optimizer typically requires fp32 for numerical stability
    if "optimizer" in precisions:
        optimizer_precision = precisions["optimizer"]
        if optimizer_precision in ["fp4", "int8"]:
            raise IncompatiblePrecisionError(
                f"Optimizer precision '{optimizer_precision}' may cause numerical instability. "
                f"Consider using fp16, bf16, or fp32 for optimizer states."
            )

    # Rule 3: Mixed precision with extreme differences may cause issues
    all_precisions = set(prec for prec in precisions.values() if prec is not None)
    if len(all_precisions) > 1:
        precision_bytes = [PRECISION_BYTES[prec] for prec in all_precisions]
        max_bytes, min_bytes = max(precision_bytes), min(precision_bytes)

        # Warn if precision difference is too extreme (more than 8x difference)
        if max_bytes / min_bytes > 8:
            extreme_precisions = [
                prec
                for prec in all_precisions
                if PRECISION_BYTES[prec] in [max_bytes, min_bytes]
            ]
            # This is a warning case, not an error - just document the potential issue
            pass  # Could add logging here in the future


def validate_mixed_precision_config(
    weight_dtype: str = None,
    activation_dtype: str = None,
    kv_cache_dtype: str = None,
    expert_parameter_dtype: str = None,
    attention_parameter_dtype: str = None,
    grad_dtype: str = None,
    optimizer_dtype: str = None,
) -> None:
    """Validate a complete mixed precision configuration."""
    precisions = {}

    # Collect all non-None precisions
    if weight_dtype is not None:
        precisions["weight"] = weight_dtype
    if activation_dtype is not None:
        precisions["activation"] = activation_dtype
    if kv_cache_dtype is not None:
        precisions["kv_cache"] = kv_cache_dtype
    if expert_parameter_dtype is not None:
        precisions["expert_parameter"] = expert_parameter_dtype
    if attention_parameter_dtype is not None:
        precisions["attention_parameter"] = attention_parameter_dtype
    if grad_dtype is not None:
        precisions["grad"] = grad_dtype
    if optimizer_dtype is not None:
        precisions["optimizer"] = optimizer_dtype

    # Validate the configuration
    validate_precision_compatibility(precisions)


@dataclass
class OperatorMetrics:
    """Metrics for a single operator execution."""

    name: str = ""
    flops: int = 0
    param_size: int = 0
    memory_movement_bytes: int = 0
    execution_time_ms: float = 0.0
    arithmetic_intensity: float = 0.0  # FLOPs per byte moved
    utilization: float = 0.0  # Hardware utilization percentage

    def __post_init__(self):
        """Calculate derived metrics."""
        if self.memory_movement_bytes > 0:
            self.arithmetic_intensity = self.flops / self.memory_movement_bytes
            # print(f' {self.name } AI {self.arithmetic_intensity=}  {self.flops/1e9=} {self.memory_movement_bytes/1e9=}') #

    def __repr__(self):
        return (
            f"OperatorMetrics(name='{self.name}', flops={self.flops/1e9:.2f}G, "
            f"param_size={self.param_size/1e9:.2f}GB, "
            f"memory_movement={self.memory_movement_bytes/1e9:.2f}GB, "
            f"execution_time={self.execution_time_ms:.2f}ms, "
            f"arithmetic_intensity={self.arithmetic_intensity:.2f}, "
            f"utilization={self.utilization:.1f}%)"
        )


@dataclass
class HardwareSpecs:
    """Hardware specifications for performance modeling."""

    name: str
    peak_flops: Dict[str, float]  # TFLOPS for different precisions
    memory_bandwidth_gbps: float
    memory_size_gb: int
    tensor_cores: Optional[int] = None

    def __repr__(self):
        peak_flops_str = ", ".join(
            [f"{k}:{v:.1f}T" for k, v in self.peak_flops.items()]
        )
        return (
            f"HardwareSpecs(name='{self.name}', peak_flops=[{peak_flops_str}], "
            f"memory_bandwidth={self.memory_bandwidth_gbps:.1f}GB/s, "
            f"memory_size={self.memory_size_gb}GB, tensor_cores={self.tensor_cores})"
        )

    @classmethod
    def from_gpu_spec(
        cls, gpu_name: str, gpu_specs_path: str = None
    ) -> "HardwareSpecs":
        """Load hardware specs from GPU specifications file."""
        if gpu_specs_path is None:
            # Try multiple possible paths
            current_dir = os.path.dirname(os.path.abspath(__file__))
            possible_paths = [
                os.path.join(current_dir, "../../gpu/data/gpu_specs.yaml"),
                os.path.join(current_dir, "../../../gpu/data/gpu_specs.yaml"),
                "gpu/data/gpu_specs.yaml",
                "data/gpu_specs.yaml",
            ]

            gpu_specs_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    gpu_specs_path = path
                    break

            if gpu_specs_path is None:
                raise FileNotFoundError("Could not find gpu_specs.yaml file")

        with open(gpu_specs_path, "r") as f:
            specs = yaml.safe_load(f)

        gpu_spec = specs["gpus"].get(gpu_name)
        if not gpu_spec:
            raise ValueError(f"GPU {gpu_name} not found in specifications")

        # Extract peak FLOPS for different precisions
        peak_flops = {}
        if "tensor_performance" in gpu_spec:
            for precision, flops in gpu_spec["tensor_performance"].items():
                if flops is not None:
                    peak_flops[precision] = flops

        if "vector_performance" in gpu_spec:
            for precision, flops in gpu_spec["vector_performance"].items():
                if flops is not None:
                    peak_flops[precision] = flops

        return cls(
            name=gpu_spec["name"],
            peak_flops=peak_flops,
            memory_bandwidth_gbps=gpu_spec["memory_bandwidth_gbps"],
            memory_size_gb=gpu_spec["memory_size_gb"],
            tensor_cores=gpu_spec.get("tensor_cores_total"),
        )

    def get_compute_density(self) -> float:
        """Get the compute density for a given precision."""
        return self.peak_flops['fp8_tensor'] / self.memory_bandwidth_gbps * 1000


class BaseOperator(ABC):
    """
    Base class for all operators in the LLM modeling system.

    Each operator represents a specific computation (e.g., attention, matrix multiplication)
    and can compute its resource requirements and performance characteristics.
    """

    def __init__(
        self,
        name: str,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
    ):
        self.name = name
        self.weight_precision = weight_precision or precision
        self.activation_precision = activation_precision or precision

        # Initialize shape parameters
        self.batch_size = None
        self.sequence_length = None
        self.kv_lens = None

        # Validate precision types
        validate_precision_type(self.weight_precision)
        validate_precision_type(self.activation_precision)

    def __repr__(self):
        return (
            f"{self.__class__.__name__}(name='{self.name}', "
            f"weight_precision='{self.weight_precision}', "
            f"activation_precision='{self.activation_precision}')"
        )

    def set_shape(self, batch_size: int, sequence_length: int, kv_lens: int = None):
        """Set the shape parameters for this operator."""
        self.batch_size = batch_size
        self.sequence_length = sequence_length
        self.kv_lens = kv_lens

    def get_bytes_per_element(self, precision: str = None) -> float:
        """Get bytes per element for given precision."""
        if precision is None:
            precision = self.activation_precision
        validate_precision_type(precision)
        return PRECISION_BYTES[precision]

    @abstractmethod
    def compute_flops(self) -> int:
        """Compute the number of floating point operations."""
        pass

    @abstractmethod
    def compute_memory_access_bytes(self) -> int:
        """Compute memory mem_access_bytes requirements in bytes (data transfer)."""
        pass

    @abstractmethod
    def compute_params_bytes(self) -> int:
        """Compute the number of parameters in this operator."""
        pass

    def compute_metrics(self, hardware: HardwareSpecs) -> OperatorMetrics:
        """Compute comprehensive metrics for this operator on given hardware."""
        flops = self.compute_flops()
        param_size = self.compute_params_bytes()
        memory_movement_bytes = self.compute_memory_access_bytes()

        # Estimate execution time based on compute vs memory bound
        compute_time_ms = self._estimate_compute_time(flops, hardware)
        memory_time_ms = self._estimate_memory_time(memory_movement_bytes, hardware)
        execution_time_ms = max(compute_time_ms, memory_time_ms)

        # Estimate utilization
        utilization = self._estimate_utilization(flops, memory_movement_bytes, hardware)

        return OperatorMetrics(
            name=self.name,
            flops=flops,
            param_size=param_size,
            memory_movement_bytes=memory_movement_bytes,
            execution_time_ms=execution_time_ms,
            utilization=utilization,
        )

    def _estimate_compute_time(self, flops: int, hardware: HardwareSpecs) -> float:
        """Estimate compute time in milliseconds with hardware-specific optimizations."""
        # Determine if tensor cores can be utilized
        tensor_core_utilization = self._detect_tensor_core_utilization(hardware)

        # Get peak FLOPS for this precision with tensor core consideration
        peak_flops = self._get_hardware_peak_flops(hardware, tensor_core_utilization)

        # Apply mixed precision overhead
        mixed_precision_overhead = self._calculate_mixed_precision_overhead(hardware)

        # Calculate base compute time
        peak_flops_per_sec = peak_flops * 1e12  # Convert TFLOPS to FLOPS
        base_time_ms = (flops / peak_flops_per_sec) * 1000

        # Apply overhead and efficiency factors
        efficiency_factor = self._get_hardware_efficiency_factor(
            hardware, tensor_core_utilization
        )

        return base_time_ms * mixed_precision_overhead / efficiency_factor

    def _estimate_memory_time(
        self, memory_movement_bytes: int, hardware: HardwareSpecs
    ) -> float:
        """Estimate memory transfer time in milliseconds with hardware-specific bandwidth."""
        # Get effective memory bandwidth considering cache hierarchy
        effective_bandwidth = self._get_effective_memory_bandwidth(
            hardware, memory_movement_bytes
        )

        # Calculate memory transfer time
        bandwidth_bytes_per_sec = effective_bandwidth * 1e9  # Convert GB/s to B/s
        base_time_ms = (memory_movement_bytes / bandwidth_bytes_per_sec) * 1000

        return base_time_ms

    def _estimate_utilization(
        self, flops: int, memory_movement_bytes: int, hardware: HardwareSpecs
    ) -> float:
        """Estimate hardware utilization percentage."""
        compute_time = self._estimate_compute_time(flops, hardware)
        memory_time = self._estimate_memory_time(memory_movement_bytes, hardware)

        if compute_time > memory_time:
            # Compute bound - utilization based on achieved vs peak FLOPS
            return min(100.0, (compute_time / (compute_time + 0.1)) * 100)
        else:
            # Memory bound - utilization based on memory bandwidth
            return min(100.0, (memory_time / (memory_time + 0.1)) * 100)

    def _detect_tensor_core_utilization(self, hardware: HardwareSpecs) -> bool:
        """Detect if tensor cores can be utilized for this operator."""
        # Check if hardware has tensor cores
        if not hardware.tensor_cores or hardware.tensor_cores == 0:
            return False

        # Check if precision supports tensor cores
        tensor_core_precisions = {"fp16", "bf16", "tf32", "fp8", "int8"}
        if self.activation_precision not in tensor_core_precisions:
            return False

        # Check if operator type benefits from tensor cores
        # MatMul and Attention operators typically benefit
        operator_types_with_tensor_cores = {"MatMul", "Attention", "MLAAttention"}
        return self.name in operator_types_with_tensor_cores

    def _get_hardware_peak_flops(
        self, hardware: HardwareSpecs, use_tensor_cores: bool
    ) -> float:
        """Get peak FLOPS for this precision with tensor core consideration."""
        precision = self.activation_precision

        # Try tensor performance first if tensor cores are available
        if use_tensor_cores:
            tensor_key = f"{precision}_tensor"
            if tensor_key in hardware.peak_flops:
                return hardware.peak_flops[tensor_key]

        # Fallback to regular precision performance
        if precision in hardware.peak_flops:
            return hardware.peak_flops[precision]

        raise RuntimeError(
            f"Peak FLOPS not found for precision {precision} on hardware {hardware.name}"
        )

    def _get_hardware_efficiency_factor(
        self, hardware: HardwareSpecs, use_tensor_cores: bool
    ) -> float:
        """Get hardware efficiency factor based on utilization characteristics."""
        return 1.0
        base_efficiency = 0.85  # Base efficiency for well-optimized operations

        # Tensor cores typically have higher efficiency
        if use_tensor_cores:
            base_efficiency = 0.92

        # Hardware-specific efficiency adjustments
        if hardware.name:
            # Modern GPUs tend to have better efficiency
            if any(gpu in hardware.name.upper() for gpu in ["H100", "H200", "B200"]):
                base_efficiency *= 1.1
            elif any(gpu in hardware.name.upper() for gpu in ["A100", "V100"]):
                base_efficiency *= 1.05
            elif any(gpu in hardware.name.upper() for gpu in ["RTX", "GTX"]):
                base_efficiency *= 0.95

        return min(base_efficiency, 1.0)

    def _get_effective_memory_bandwidth(
        self, hardware: HardwareSpecs, memory_bytes: int
    ) -> float:
        """Get effective memory bandwidth considering cache hierarchy."""
        base_bandwidth = hardware.memory_bandwidth_gbps

        return base_bandwidth

    def compute_params_num(self) -> int:
        pass


class EmbeddingOperator(BaseOperator):
    """
    Embedding operator for word embeddings.
    """

    def __init__(
        self, num_embeddings: int, embedding_dim: int, precision: str = "bf16"
    ):
        super().__init__("Embedding", precision)
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim

    def __repr__(self):
        return (
            f"EmbeddingOperator(num_embeddings={self.num_embeddings}, "
            f"embedding_dim={self.embedding_dim}, precision='{self.activation_precision}')"
        )

    def compute_flops(self) -> int:
        """Compute FLOPs for embedding: batch_size * sequence_length * embedding_dim."""
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")
        return self.batch_size * self.sequence_length * self.embedding_dim

    def compute_memory_access_bytes(self) -> int:
        """Compute memory mem_access_bytes bytes for embedding: batch_size * sequence_length * embedding_dim."""
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")
        return (
            self.batch_size
            * self.sequence_length
            * self.embedding_dim
            * self.get_bytes_per_element()
        )

    def compute_params_bytes(self) -> int:
        """Compute parameters for embedding: num_embeddings * embedding_dim."""
        return self.num_embeddings * self.embedding_dim * self.get_bytes_per_element()

    def compute_params_num(self) -> int:
        return self.num_embeddings * self.embedding_dim


class MatMulOperator(BaseOperator):
    """
    Matrix multiplication operator for general M x K @ K x N operations.
    """

    def __init__(
        self,
        M: int = None,
        N: int = None,
        K: int = None,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
        input_precision: str = None,
        output_precision: str = None,
        input_dim: int = None,
        output_dim: int = None,
        name: str = None,
    ):
        super().__init__(name, precision, weight_precision, activation_precision)

        # Enhanced precision control - more granular than base class
        self.input_precision = input_precision or activation_precision or precision
        self.output_precision = output_precision or activation_precision or precision

        # Validate additional precision parameters
        validate_precision_type(self.input_precision)
        validate_precision_type(self.output_precision)

        # Handle backward compatibility
        if input_dim is not None and output_dim is not None:
            # Old interface: MatMulOperator(input_dim=K, output_dim=N)
            # Assume M=1 for backward compatibility (will be overridden in compute methods)
            self.M = 1
            self.K = input_dim
            self.N = output_dim
        elif M is not None and N is not None and K is None:
            # Another old interface: MatMulOperator(vocab_size, hidden_size)
            # This is likely MatMulOperator(K, N) where M will be determined at runtime
            self.M = 1  # Will be overridden in compute methods
            self.K = M  # First parameter is actually K (input features)
            self.N = N  # Second parameter is N (output features)
        else:
            # New interface: MatMulOperator(M, N, K)
            self.M = M  # First dimension of input matrix
            self.N = N  # Second dimension of weight matrix (output features)
            self.K = K  # Shared dimension (input features)

    def __repr__(self):
        return f"{self.name} MatMul: M={self.M} N={self.N} K={self.K}"

    def set_shape(self, batch_size: int, sequence_length: int, kv_lens: int = None):
        """Set the shape parameters for this operator."""
        super().set_shape(batch_size, sequence_length, kv_lens)
        # Update M dimension based on batch_size and sequence_length
        self.M = batch_size * sequence_length

    def compute_flops(self) -> int:
        """Compute FLOPs for matrix multiplication: M x K @ K x N = M x N."""
        # Use stored shape values
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")

        M = self.batch_size * self.sequence_length
        base_flops = M * self.N * self.K * 2

        return base_flops

    def compute_memory_access_bytes(self) -> int:
        """Compute memory mem_access_bytes requirements (data transfer)."""
        # Use stored shape values
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")

        M = self.batch_size * self.sequence_length

        # Use precision-aware byte calculations with granular precision control
        input_bytes = self.get_bytes_per_element(self.input_precision)
        weight_bytes = self.get_bytes_per_element(self.weight_precision)
        output_bytes = self.get_bytes_per_element(self.output_precision)

        # Read input and weights, write output
        input_reads = M * self.K * input_bytes
        weight_reads = self.K * self.N * weight_bytes
        output_writes = M * self.N * output_bytes

        return input_reads + weight_reads + output_writes

    def compute_params_bytes(self) -> int:
        """Compute the number of parameters in the weight matrix."""
        return self.K * self.N * self.get_bytes_per_element(self.weight_precision)

    def compute_params_num(self) -> int:
        """Compute the number of parameters in the weight matrix."""
        return self.K * self.N


class AttentionOperator(BaseOperator):
    """
    Attention operator modeling multi-head attention computation.

    Covers QKV projections, attention computation, and output projection.
    Optimized for decoding scenarios. Supports MHA and GQA.
    """

    def __init__(
        self,
        hidden_size: int,
        num_heads: int,
        num_kv_heads: int = None,
        head_dim: int = None,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
        kv_cache_precision: str = None,
        num_attention_groups: int = None,
    ):
        super().__init__("Attention", precision, weight_precision, activation_precision)
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        # num_attention_groups is for step3
        self.num_kv_heads = num_kv_heads or num_attention_groups or num_heads
        self.head_dim = head_dim or (hidden_size // num_heads)

        # KV cache precision - separate from activation precision
        self.kv_cache_precision = (
            kv_cache_precision or activation_precision or precision
        )
        validate_precision_type(self.kv_cache_precision)

        # Create MatMul operators as member variables with mixed precision support
        H, D = self.num_heads, self.head_dim
        H_kv = self.num_kv_heads

        M = 1

    def __repr__(self):
        return (
            f"AttentionOperator(hidden_size={self.hidden_size}, num_heads={self.num_heads}, "
            f"num_kv_heads={self.num_kv_heads}, head_dim={self.head_dim}, "
            f"weight_precision='{self.weight_precision}', "
            f"activation_precision='{self.activation_precision}', "
            f"kv_cache_precision='{self.kv_cache_precision}')"
        )

    def set_shape(self, batch_size: int, sequence_length: int, kv_lens: int = None):
        """Set the shape parameters for this operator and all child operators."""
        super().set_shape(batch_size, sequence_length, kv_lens)

    def compute_flops(self) -> int:
        """Compute FLOPs for attention operation during decoding."""
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        H, D = self.num_heads, self.head_dim
        flops = 0

        # Attention scores: B * H * sequence_length * kv_lens * D
        attention_scores_flops = (
            self.batch_size * H * self.sequence_length * self.kv_lens * D * 2
        )
        flops += attention_scores_flops

        # Attention values: B * H * sequence_length * kv_lens * D
        attention_values_flops = (
            self.batch_size * H * self.sequence_length * self.kv_lens * D * 2
        )
        flops += attention_values_flops

        return flops

    def compute_memory_access_bytes(self) -> int:
        """
        Compute memory mem_access_bytes requirements for attention during decoding.

        Memory access is just the size of KV cache.
        """
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        return self.get_kv_size()

    def compute_params_bytes(self) -> int:
        return 0

    def compute_params_num(self) -> int:
        return 0

    def get_kv_size(self) -> int:
        """Get the size of KV cache in bytes."""
        B = self.batch_size
        D = self.head_dim
        H_kv = self.num_kv_heads
        kv_cache_reads = (
            B
            * self.kv_lens
            * 2
            * H_kv
            * D
            * self.get_bytes_per_element(self.kv_cache_precision)
        )
        return kv_cache_reads


class AttentionLayer(BaseOperator):
    """
    Attention operator modeling multi-head attention computation.

    Covers QKV projections, attention computation, and output projection.
    Optimized for decoding scenarios. Supports MHA and GQA.
    """

    def __init__(
        self,
        hidden_size: int,
        num_heads: int,
        num_kv_heads: int = None,
        head_dim: int = None,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
        kv_cache_precision: str = None,
        num_attention_groups: int = None,
        share_q_dim: int = None,
    ):
        super().__init__(
            "AttentionLayer", precision, weight_precision, activation_precision
        )
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        # num_attention_groups is for step3
        self.num_kv_heads = num_kv_heads or num_attention_groups or num_heads
        self.head_dim = head_dim or (hidden_size // num_heads)

        # KV cache precision - separate from activation precision
        self.kv_cache_precision = (
            kv_cache_precision or activation_precision or precision
        )
        validate_precision_type(self.kv_cache_precision)

        # Create MatMul operators as member variables with mixed precision support
        H, D = self.num_heads, self.head_dim
        H_kv = self.num_kv_heads

        M = 1

        self.q_proj = MatMulOperator(
            M,
            H * D,
            self.hidden_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
            name="q_proj",
        )
        self.k_proj = MatMulOperator(
            M,
            H_kv * D,
            self.hidden_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
            name="k_proj",
        )
        self.v_proj = MatMulOperator(
            M,
            H_kv * D,
            self.hidden_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
            name="v_proj",
        )
        self.o_proj = MatMulOperator(
            M,
            self.hidden_size,
            H * D,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
            name="o_proj",
        )
        # for step3
        if share_q_dim:
            self.q_proj = MatMulOperator(
                M,
                share_q_dim,
                self.hidden_size,
                weight_precision=self.weight_precision,
                activation_precision=self.activation_precision,
                name="q_proj",
            )
            self.wq = MatMulOperator(
                M,
                share_q_dim,
                H * D,
                weight_precision=self.weight_precision,
                activation_precision=self.activation_precision,
                name="wq",
            )

        self.attn = AttentionOperator(
            hidden_size=hidden_size,
            num_heads=num_heads,
            num_kv_heads=num_kv_heads,
            head_dim=head_dim,
            precision=precision,
            weight_precision=weight_precision,
            activation_precision=activation_precision,
            kv_cache_precision=kv_cache_precision,
        )

        # Store all inner operations in a list for iteration
        self.ops = [self.q_proj, self.k_proj, self.v_proj, self.o_proj, self.attn]

        if share_q_dim:
            self.ops.append(self.wq)

    def __repr__(self):
        return (
            f"AttentionOperator(hidden_size={self.hidden_size}, num_heads={self.num_heads}, "
            f"num_kv_heads={self.num_kv_heads}, head_dim={self.head_dim}, "
            f"weight_precision='{self.weight_precision}', "
            f"activation_precision='{self.activation_precision}', "
            f"kv_cache_precision='{self.kv_cache_precision}')"
        )

    def set_shape(self, batch_size: int, sequence_length: int, kv_lens: int = None):
        """Set the shape parameters for this operator and all child operators."""
        super().set_shape(batch_size, sequence_length, kv_lens)
        # Propagate shape to all child MatMul operators
        for op in self.ops:
            op.set_shape(batch_size, sequence_length, kv_lens)

    def get_matmul_operators(self):
        """Get all MatMul operators for shape inspection."""
        return {
            "q_proj": self.q_proj,
            "k_proj": self.k_proj,
            "v_proj": self.v_proj,
            "o_proj": self.o_proj,
            "wq": self.wq if hasattr(self, "wq") else None,
        }

    def compute_flops(self) -> int:
        """Compute FLOPs for attention operation during decoding."""
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        flops = 0

        for op in self.ops:
            flops += op.compute_flops()

        return flops

    def compute_memory_access_bytes(self) -> int:
        """
        Compute memory mem_access_bytes requirements for attention during decoding.

        Memory access is just the size of KV cache.
        """
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        mem_access_bytes = 0

        for op in self.ops:
            mem_access_bytes += op.compute_memory_access_bytes()

        return mem_access_bytes

    def compute_params_bytes(self) -> int:
        """Compute the number of parameters in attention layers."""
        # Sum parameters from all MatMul operators using ops list iteration
        return sum(op.compute_params_bytes() for op in self.ops)

    def compute_params_num(self) -> int:
        """Compute the number of parameters in MLA attention layers."""
        # Sum parameters from all MatMul operators using ops list iteration
        return sum(op.compute_params_num() for op in self.ops)

    def get_kv_size(self) -> int:
        """Get the size of KV cache in bytes."""
        return self.attn.get_kv_size()


class MLAAttentionOperator(BaseOperator):
    """
    Multi-head Latent Attention (MLA) operator for DeepSeek V3.

    MLA uses compressed KV representations with LoRA-style projections
    to reduce memory usage while maintaining performance.
    """

    def __init__(
        self,
        hidden_size: int,
        num_heads: int,
        kv_lora_rank: int = 512,
        q_lora_rank: int = 1536,
        qk_rope_head_dim: int = 64,
        qk_nope_head_dim: int = 128,
        v_head_dim: int = 128,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
        kv_cache_precision: str = None,
    ):
        super().__init__(
            "MLAAttention", precision, weight_precision, activation_precision
        )
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.kv_lora_rank = kv_lora_rank
        self.q_lora_rank = q_lora_rank
        self.qk_rope_head_dim = qk_rope_head_dim
        self.qk_nope_head_dim = qk_nope_head_dim
        self.v_head_dim = v_head_dim
        self.kv_cache_precision = (
            kv_cache_precision or activation_precision or precision
        )

        # Validate KV cache precision
        validate_precision_type(self.kv_cache_precision)

        # Total head dimensions
        self.qk_head_dim = qk_rope_head_dim + qk_nope_head_dim
        self.total_kv_dim = num_heads * (
            qk_rope_head_dim + qk_nope_head_dim + v_head_dim
        )

    def __repr__(self):
        return (
            f"MLAAttentionOperator(hidden_size={self.hidden_size}, num_heads={self.num_heads}, "
            f"kv_lora_rank={self.kv_lora_rank}, q_lora_rank={self.q_lora_rank}, "
            f"qk_rope_head_dim={self.qk_rope_head_dim}, qk_nope_head_dim={self.qk_nope_head_dim}, "
            f"v_head_dim={self.v_head_dim}, "
            f"weight_precision='{self.weight_precision}', "
            f"activation_precision='{self.activation_precision}', "
            f"kv_cache_precision='{self.kv_cache_precision}')"
        )

    def set_shape(self, batch_size: int, sequence_length: int, kv_lens: int = None):
        """Set the shape parameters for this operator and all child operators."""
        super().set_shape(batch_size, sequence_length, kv_lens)

    def compute_flops(self) -> int:
        """Compute FLOPs for attention computation only (for Table 2 breakdown)."""
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        B = self.batch_size
        S = self.sequence_length

        # Attention computation: QK^T and softmax(QK^T)V
        # QK^T: B * num_heads * S * kv_lens * qk_head_dim
        qk_flops = (
            B
            * self.num_heads
            * S
            * self.kv_lens
            * (self.kv_lora_rank + self.qk_rope_head_dim)
            * 2
        )
        # softmax(QK^T)V: B * num_heads * S * kv_lens * v_head_dim
        qkv_flops = B * self.num_heads * S * self.kv_lens * self.kv_lora_rank * 2

        return qk_flops + qkv_flops

    def compute_memory_access_bytes(self) -> int:
        """
        Compute memory mem_access_bytes requirements for MLA attention with mixed precision support.

        Memory access is just the size of KV cache.
        """
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        mem_access_bytes = self.get_kv_size()

        return mem_access_bytes

    def compute_params_bytes(self) -> int:
        """Compute the number of parameters in MLA attention layers."""
        # Sum parameters from all MatMul operators using ops list iteration
        return 0

    def compute_params_num(self) -> int:
        return 0

    def get_kv_size(self) -> int:
        B = self.batch_size
        kv_cache_bytes = self.get_bytes_per_element(self.kv_cache_precision)

        # Compressed KV cache reads (using KV cache precision)
        # MLA reads compressed KV representation plus RoPE dimensions
        kv_cache_dim = self.kv_lora_rank + self.qk_rope_head_dim
        compressed_kv_reads = B * self.kv_lens * kv_cache_dim * kv_cache_bytes
        return compressed_kv_reads


class MLAAttentionLayer(BaseOperator):
    """
    Multi-head Latent Attention (MLA) operator for DeepSeek V3.

    MLA uses compressed KV representations with LoRA-style projections
    to reduce memory usage while maintaining performance.
    """

    def __init__(
        self,
        hidden_size: int,
        num_heads: int,
        kv_lora_rank: int = 512,
        q_lora_rank: int = 1536,
        qk_rope_head_dim: int = 64,
        qk_nope_head_dim: int = 128,
        v_head_dim: int = 128,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
        kv_cache_precision: str = None,
    ):
        super().__init__(
            "MLAAttention", precision, weight_precision, activation_precision
        )
        self.precision = precision
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.kv_lora_rank = kv_lora_rank
        self.q_lora_rank = q_lora_rank
        self.qk_rope_head_dim = qk_rope_head_dim
        self.qk_nope_head_dim = qk_nope_head_dim
        self.v_head_dim = v_head_dim
        self.kv_cache_precision = (
            kv_cache_precision or activation_precision or precision
        )

        # Validate KV cache precision
        validate_precision_type(self.kv_cache_precision)

        # Total head dimensions
        self.qk_head_dim = qk_rope_head_dim + qk_nope_head_dim
        self.total_kv_dim = num_heads * (
            qk_rope_head_dim + qk_nope_head_dim + v_head_dim
        )

        # Create MatMul operators as member variables with mixed precision support
        M = 1
        self.q_compress = MatMulOperator(
            M,
            self.q_lora_rank,
            self.hidden_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
            name="q_compress",
        )
        self.q_decompress = MatMulOperator(
            M,
            self.num_heads * self.qk_head_dim,
            self.q_lora_rank,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
            name="q_decompress",
        )
        # KV operations use KV cache precision for output
        self.kv_compress = MatMulOperator(
            M,
            (self.kv_lora_rank + self.qk_rope_head_dim),
            self.hidden_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
            output_precision=self.kv_cache_precision,
            name="kv_compress",
        )
        # todo is this correct?
        self.kv_decompress = MatMulOperator(
            M,
            self.total_kv_dim,
            self.kv_lora_rank,
            weight_precision=self.weight_precision,
            input_precision=self.kv_cache_precision,
            activation_precision=self.activation_precision,
            name="kv_decompress",
        )
        self.o_proj = MatMulOperator(
            M,
            self.hidden_size,
            self.num_heads * self.v_head_dim,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
            name="o_proj",
        )

        self.attn = MLAAttentionOperator(
            self.hidden_size,
            self.num_heads,
            self.kv_lora_rank,
            self.q_lora_rank,
            self.qk_rope_head_dim,
            self.qk_nope_head_dim,
            self.v_head_dim,
            self.precision,
            self.weight_precision,
            self.activation_precision,
            self.kv_cache_precision,
        )

        # Store all inner operations in a list for iteration
        self.ops = [
            self.q_compress,
            self.q_decompress,
            self.kv_compress,
            self.kv_decompress,
            self.o_proj,
            self.attn,
        ]

    def __repr__(self):
        return (
            f"MLAAttentionOperator(hidden_size={self.hidden_size}, num_heads={self.num_heads}, "
            f"kv_lora_rank={self.kv_lora_rank}, q_lora_rank={self.q_lora_rank}, "
            f"qk_rope_head_dim={self.qk_rope_head_dim}, qk_nope_head_dim={self.qk_nope_head_dim}, "
            f"v_head_dim={self.v_head_dim}, "
            f"weight_precision='{self.weight_precision}', "
            f"activation_precision='{self.activation_precision}', "
            f"kv_cache_precision='{self.kv_cache_precision}')"
        )

    def set_shape(self, batch_size: int, sequence_length: int, kv_lens: int = None):
        """Set the shape parameters for this operator and all child operators."""
        super().set_shape(batch_size, sequence_length, kv_lens)
        # Propagate shape to all child MatMul operators
        for op in self.ops:
            op.set_shape(batch_size, sequence_length, kv_lens)

    def get_matmul_operators(self):
        """Get all MatMul operators for shape inspection."""
        return {
            "q_compress": self.q_compress,
            "q_decompress": self.q_decompress,
            "kv_compress": self.kv_compress,
            "kv_decompress": self.kv_decompress,
            "o_proj": self.o_proj,
        }

    def compute_flops(self) -> int:
        """Compute FLOPs for MLA attention operation."""
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        flops = (
            self.compute_linear_projection_flops()
            + self.compute_attention_computation_flops()
        )

        return flops

    def compute_linear_projection_flops(self) -> int:
        """Compute FLOPs for linear projections only (for Table 2 breakdown)."""
        # Use ops list iteration
        return sum(
            op.compute_flops() for op in self.ops if isinstance(op, MatMulOperator)
        )

    def compute_attention_computation_flops(self) -> int:
        """Compute FLOPs for attention computation only (for Table 2 breakdown)."""
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        return self.attn.compute_flops()

    def compute_memory_access_bytes(self) -> int:
        """
        Compute memory mem_access_bytes requirements for MLA attention with mixed precision support.

        Memory access is just the size of KV cache.
        """
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        mem_access_bytes = 0
        for op in self.ops:
            mem_access_bytes += op.compute_memory_access_bytes()

        return mem_access_bytes

    def compute_attnetion_memory_access_bytes(self) -> int:
        """Compute memory mem_access_bytes requirements for attention during decoding."""
        if (
            self.batch_size is None
            or self.sequence_length is None
            or self.kv_lens is None
        ):
            raise ValueError("Shape not set. Call set_shape() first.")

        return self.attn.compute_memory_access_bytes()

    def compute_params_bytes(self) -> int:
        """Compute the number of parameters in MLA attention layers."""
        # Sum parameters from all MatMul operators using ops list iteration
        return sum(op.compute_params_bytes() for op in self.ops)

    def compute_params_num(self) -> int:
        """Compute the number of parameters in MLA attention layers."""
        # Sum parameters from all MatMul operators using ops list iteration
        return sum(op.compute_params_num() for op in self.ops)

    def get_projection_operators(self):
        """Get the individual MatMul operators for detailed analysis."""
        # Return member variables directly
        return self.get_matmul_operators()

    def get_projection_breakdown(self):
        """Get detailed breakdown of each projection's FLOPs and memory."""
        operators = self.get_matmul_operators()

        breakdown = {}
        for name, op in operators.items():
            breakdown[name] = {
                "flops": op.compute_flops(),
                "param_size": op.compute_params_bytes(),
                "memory_movement": op.compute_memory_access_bytes(),
                "weight_params": op.K * op.N,  # Weight matrix size
                "description": self._get_projection_description(name),
            }

        return breakdown

    def _get_projection_description(self, name: str) -> str:
        """Get human-readable description of each projection."""
        descriptions = {
            "q_compress": f"Query compression: {self.hidden_size} → {self.q_lora_rank}",
            "q_decompress": f"Query decompression: {self.q_lora_rank} → {self.num_heads * self.qk_head_dim}",
            "kv_compress": f"KV compression: {self.hidden_size} → {self.kv_lora_rank+ self.qk_rope_head_dim}",
            "kv_decompress": f"KV decompression: {self.kv_lora_rank} → {self.total_kv_dim}",
            "o_proj": f"Output projection: {self.num_heads * self.v_head_dim} → {self.hidden_size}",
        }
        return descriptions.get(name, "Unknown projection")


def extract_all_matmul_operators(operators):
    """
    Extract all MatMul operators from a list of operators for shape inspection.

    Args:
        operators: List of operator instances

    Returns:
        Dict mapping operator_name.matmul_name to MatMul operator
    """
    all_matmuls = {}

    for i, op in enumerate(operators):
        if hasattr(op, "get_matmul_operators"):
            # Get MatMul operators from composite operators
            matmuls = op.get_matmul_operators()
            for name, matmul_op in matmuls.items():
                key = f"{op.name}_{i}.{name}"
                all_matmuls[key] = matmul_op
        elif isinstance(op, MatMulOperator):
            # Direct MatMul operator
            key = f"{op.name}_{i}"
            all_matmuls[key] = op

    return all_matmuls


def print_matmul_shapes(operators, title="MatMul Operator Shapes"):
    """
    Print all MatMul operator shapes in a model.

    Args:
        operators: List of operator instances
        title: Title for the output

    Note: Operators must have their shapes set via set_shape() before calling this function.
    """
    all_matmuls = extract_all_matmul_operators(operators)

    print(f"=== {title} ===")
    print(
        f"{'Operator':<30} {'Shape (M x K @ K x N)':<25} {'FLOPs (M)':<12} {'Params (M)':<12}"
    )
    print("-" * 85)

    total_flops = 0
    total_params = 0

    for name, matmul in all_matmuls.items():
        try:
            shape_str = f"{matmul.M} x {matmul.K} @ {matmul.K} x {matmul.N}"
            flops_m = matmul.compute_flops() / 1e6
            params_m = (matmul.K * matmul.N) / 1e6

            total_flops += matmul.compute_flops()
            total_params += matmul.K * matmul.N

            print(f"{name:<30} {shape_str:<25} {flops_m:<12.2f} {params_m:<12.2f}")
        except ValueError:
            print(
                f"{name:<30} {'Shape not set':<25} {'N/A':<12} {(matmul.K * matmul.N)/1e6:<12.2f}"
            )

    print("-" * 85)
    print(f"{'TOTAL':<30} {'':<25} {total_flops/1e6:<12.2f} {total_params/1e6:<12.2f}")
    print()


def compare_flash_attention_benefits(
    operators, context_lengths=[1024, 4096, 8192, 16384]
):
    """
    Compare memory mem_access_bytes with and without FlashAttention optimizations.

    Args:
        operators: List of attention operators (AttentionOperator or MLAAttentionOperator)
        context_lengths: List of context lengths to analyze
    """
    print("=== FlashAttention Memory mem_access_bytes Comparison ===")
    print(
        f"{'Operator':<25} {'Context':<8} {'Without FA (GB)':<15} {'With FA (GB)':<15} {'Reduction':<10}"
    )
    print("-" * 80)

    for op in operators:
        if not hasattr(op, "use_flash_attention"):
            continue

        original_flash_setting = op.use_flash_attention

        for context_len in context_lengths:
            # Set shape for analysis
            op.set_shape(batch_size=1, sequence_length=1, kv_lens=context_len)

            # Calculate without FlashAttention
            op.use_flash_attention = False
            memory_without_fa = op.compute_memory_access_bytes() / 1e9

            # Calculate with FlashAttention
            op.use_flash_attention = True
            memory_with_fa = op.compute_memory_access_bytes() / 1e9

            # Calculate reduction factor
            reduction_factor = (
                memory_without_fa / memory_with_fa if memory_with_fa > 0 else 0
            )

            print(
                f"{op.name:<25} {context_len:<8} {memory_without_fa:<15.3f} {memory_with_fa:<15.3f} {reduction_factor:<10.1f}x"
            )

        # Restore original setting
        op.use_flash_attention = original_flash_setting
        print()

    print("FA = FlashAttention")
    print(
        "Note: FlashAttention reduces memory mem_access_bytes through tiling, fused kernels, and online softmax."
    )


class FFNLayer(BaseOperator):
    """
    MLP operator for feed-forward networks with gated activation (SwiGLU, etc.).
    Optimized for decoding scenarios.
    """

    def __init__(
        self,
        hidden_size: int,
        intermediate_size: int,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
    ):
        super().__init__("MLP", precision, weight_precision, activation_precision)
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size

        # Create MatMul operators as member variables with mixed precision support
        # M=1 initially, will be updated when set_shape is called
        M = 1
        self.gate_proj = MatMulOperator(
            M,
            self.intermediate_size,
            self.hidden_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
        )
        self.up_proj = MatMulOperator(
            M,
            self.intermediate_size,
            self.hidden_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
        )
        self.down_proj = MatMulOperator(
            M,
            self.hidden_size,
            self.intermediate_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
        )

        # Store all inner operations in a list for iteration
        self.ops = [self.gate_proj, self.up_proj, self.down_proj]

    def __repr__(self):
        return (
            f"FFNLayer(hidden_size={self.hidden_size}, "
            f"intermediate_size={self.intermediate_size}, "
            f"weight_precision='{self.weight_precision}', "
            f"activation_precision='{self.activation_precision}')"
        )

    def set_shape(self, batch_size: int, sequence_length: int, kv_lens: int = None):
        """Set the shape parameters for this operator and all child operators."""
        super().set_shape(batch_size, sequence_length, kv_lens)
        # Propagate shape to all child MatMul operators
        for op in self.ops:
            op.set_shape(batch_size, sequence_length, kv_lens)

    def get_matmul_operators(self):
        """Get all MatMul operators for shape inspection."""
        return {
            "gate_proj": self.gate_proj,
            "up_proj": self.up_proj,
            "down_proj": self.down_proj,
        }

    def compute_flops(self) -> int:
        """Compute FLOPs for MLP during decoding."""
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")

        M = self.batch_size * self.sequence_length

        # All projections using ops list iteration
        total_flops = sum(op.compute_flops() for op in self.ops)

        # Add activation FLOPs (SwiGLU: gate * silu(up))
        activation_flops = M * self.intermediate_size * 2  # silu + multiply

        return total_flops + activation_flops

    def compute_memory_access_bytes(self) -> int:
        """Compute memory mem_access_bytes requirements for MLP during decoding."""
        # Sum memory mem_access_bytes from all ops using iteration
        return sum(op.compute_memory_access_bytes() for op in self.ops)

    def compute_params_bytes(self) -> int:
        """Compute the number of parameters in MLP layers."""
        # Sum parameters from all MatMul operators using ops list iteration
        return sum(op.compute_params_bytes() for op in self.ops)


class CommunicationOperator(BaseOperator):
    """
    Communication operator for modeling AllReduce, AllGather, etc.
    """

    def __init__(
        self,
        operation_type: str,
        data_size_bytes: int,
        num_devices: int,
        bandwidth_gbps: float = 100.0,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
    ):
        super().__init__(
            f"Communication_{operation_type}",
            precision,
            weight_precision,
            activation_precision,
        )
        self.operation_type = operation_type
        self.data_size_bytes = data_size_bytes
        self.num_devices = num_devices
        self.bandwidth_gbps = bandwidth_gbps

    def __repr__(self):
        return (
            f"CommunicationOperator(operation_type='{self.operation_type}', "
            f"data_size_bytes={self.data_size_bytes}, num_devices={self.num_devices}, "
            f"bandwidth_gbps={self.bandwidth_gbps}, precision='{self.activation_precision}')"
        )

    def compute_flops(self) -> int:
        """Communication operations don't have FLOPs."""
        return 0

    def compute_memory_access_bytes(self) -> int:
        """Memory mem_access_bytes for communication (data transfer)."""
        return self.data_size_bytes

    def compute_params_bytes(self) -> int:
        """Communication operations don't have parameters."""
        return 0

    def compute_communication_time_ms(self) -> float:
        """Compute communication time based on bandwidth and topology."""
        # Simplified model: assume ring topology for AllReduce
        if self.operation_type.lower() == "allreduce":
            # Ring AllReduce: 2 * (N-1) / N * data_size / bandwidth
            effective_data = (
                2 * (self.num_devices - 1) / self.num_devices * self.data_size_bytes
            )
        elif self.operation_type.lower() == "allgather":
            # AllGather: (N-1) / N * data_size / bandwidth
            effective_data = (
                (self.num_devices - 1) / self.num_devices * self.data_size_bytes
            )
        else:
            effective_data = self.data_size_bytes

        # Convert bandwidth to bytes/ms
        bandwidth_bytes_per_ms = self.bandwidth_gbps * 1e9 / 1000
        return effective_data / bandwidth_bytes_per_ms


class MoELayer(BaseOperator):
    """
    Mixture of Experts operator combining routing and expert computation.
    Supports mixed precision with separate precision control for expert parameters.
    """

    def __init__(
        self,
        hidden_size: int,
        intermediate_size: int,
        experts_per_token: int,
        num_routed_experts: int = 0,
        num_shared_experts: int = 0,
        precision: str = "fp16",
        weight_precision: str = None,
        activation_precision: str = None,
        expert_parameter_precision: str = None,
    ):
        super().__init__("MoE", precision, weight_precision, activation_precision)
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        self.num_routed_experts = num_routed_experts
        self.num_shared_experts = num_shared_experts
        self.num_total_experts = num_routed_experts + num_shared_experts

        self.experts_per_token = experts_per_token

        # Expert parameter precision - separate from shared parameters
        # Default to fp8 for expert parameters as per design document
        self.expert_parameter_precision = expert_parameter_precision or "fp8"
        validate_precision_type(self.expert_parameter_precision)

        # Router (shared parameter) - uses standard weight precision
        # M=1 for parameter counting, will be scaled by batch_size * sequence_length in FLOPS
        self.router = MatMulOperator(
            M=1,
            N=num_routed_experts,
            K=hidden_size,
            weight_precision=self.weight_precision,
            activation_precision=self.activation_precision,
        )

        # Expert (routed parameters) - uses expert parameter precision
        # Single expert template, will be scaled by experts_per_token
        self.router_expert = FFNLayer(
            hidden_size,
            intermediate_size,
            weight_precision=self.expert_parameter_precision,
            activation_precision=self.activation_precision,
        )
        self.shared_expert = FFNLayer(
            hidden_size,
            intermediate_size,
            weight_precision=self.expert_parameter_precision,
            activation_precision=self.activation_precision,
        )

    def __repr__(self):
        return (
            f"MoELayer(hidden_size={self.hidden_size}, "
            f"intermediate_size={self.intermediate_size}, "
            f"num_shared_experts={self.num_shared_experts}, num_routed_experts={self.num_routed_experts}, "
            f"experts_per_token={self.experts_per_token}, "
            f"weight_precision='{self.weight_precision}', "
            f"activation_precision='{self.activation_precision}', "
            f"expert_parameter_precision='{self.expert_parameter_precision}')"
        )

    def set_shape(self, batch_size: int, sequence_length: int, kv_lens: int = None):
        """Set the shape parameters for this operator and all child operators."""
        super().set_shape(batch_size, sequence_length, kv_lens)
        # Propagate shape to router and expert operators
        self.router.set_shape(batch_size, sequence_length, kv_lens)
        self.router_expert.set_shape(batch_size, sequence_length, kv_lens)
        self.shared_expert.set_shape(batch_size, sequence_length, kv_lens)

    def compute_flops(self) -> int:
        """Compute FLOPs for MoE operation."""
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")

        router_flops = (
            self.batch_size
            * self.sequence_length
            * self.hidden_size
            * self.num_routed_experts
        )

        # Expert FLOPs (only for active experts)
        active_expert_flops = (
            self.router_expert.compute_flops() * self.experts_per_token
            + self.shared_expert.compute_flops() * self.num_shared_experts
        )

        # Routing overhead (softmax, top-k selection)
        routing_flops = (
            self.batch_size * self.sequence_length * self.num_routed_experts * 3
        )  # Simplified

        return router_flops + active_expert_flops + routing_flops

    def compute_memory_access_bytes(self) -> int:
        """Compute memory mem_access_bytes for MoE with mixed precision support."""
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")

        # Router memory mem_access_bytes uses the router's mixed precision settings
        router_bw = self.router.compute_memory_access_bytes()
        # Scale by actual batch size and sequence length
        router_bw = router_bw * self.batch_size * self.sequence_length

        # Only active experts contribute to bandwidth
        # Expert memory mem_access_bytes uses expert parameter precision for weights
        expert_bw = self.expert.compute_memory_access_bytes()
        active_expert_bw = expert_bw * self.experts_per_token

        return router_bw + active_expert_bw

    def compute_params_bytes(self) -> int:
        """Compute the number of parameters in MoE operator.

        Parameter counts remain unchanged regardless of precision - we count
        the number of parameters, not their byte size.
        """
        # Router parameters (shared parameters)
        router_params = self.router.compute_params_bytes()

        # All expert parameters (routed parameters - not just active ones for total param count)
        # Parameter count is independent of expert_parameter_precision
        total_expert_params = (
            self.router_expert.compute_params_bytes() * self.num_routed_experts
            + self.shared_expert.compute_params_bytes() * self.num_shared_experts
        )

        return router_params + total_expert_params

    def get_expert_parameter_precision(self) -> str:
        """Get the precision used for expert parameters."""
        return self.expert_parameter_precision

    def get_shared_parameter_precision(self) -> str:
        """Get the precision used for shared parameters (router)."""
        return self.weight_precision

    def get_precision_breakdown(self) -> Dict[str, str]:
        """Get a breakdown of precisions used by different components."""
        return {
            "shared_parameters": self.weight_precision,
            "expert_parameters": self.expert_parameter_precision,
            "activations": self.activation_precision,
        }


class LayerNormOperator(BaseOperator):
    """
    Layer normalization operator.
    """

    def __init__(
        self,
        hidden_size: int,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
    ):
        super().__init__("LayerNorm", precision, weight_precision, activation_precision)
        self.hidden_size = hidden_size

    def __repr__(self):
        return (
            f"LayerNormOperator(hidden_size={self.hidden_size}, "
            f"weight_precision='{self.weight_precision}', "
            f"activation_precision='{self.activation_precision}')"
        )

    def compute_flops(self) -> int:
        """Compute FLOPs for layer normalization (RMSNorm approximation)."""
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")

        # RMSNorm: ~5 FLOPs per element (mean, variance, normalize, scale)
        return self.batch_size * self.sequence_length * self.hidden_size * 5

    def compute_memory_access_bytes(self) -> int:
        """Compute memory mem_access_bytes requirements (data transfer)."""
        if self.batch_size is None or self.sequence_length is None:
            raise ValueError("Shape not set. Call set_shape() first.")

        bytes_per_element = self.get_bytes_per_element()

        # Read input and parameters, write output
        return (
            2 * self.batch_size * self.sequence_length * self.hidden_size
            + self.hidden_size
        ) * bytes_per_element

    def compute_params_bytes(self) -> int:
        """Compute the number of parameters in layer normalization."""
        # Layer norm has scale parameters (and optionally bias, but RMSNorm typically doesn't)
        return self.hidden_size

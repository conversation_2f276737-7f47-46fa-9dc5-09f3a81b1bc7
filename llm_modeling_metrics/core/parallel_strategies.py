"""
Parallel strategy calculations for tensor parallelism and other strategies.
"""

import math
from typing import Any, Dict, List, Optional, Tuple

from .base_model import BaseModel, ParallelConfig


class ParallelStrategyCalculator:
    """Calculator for parallel execution strategies."""

    @staticmethod
    def compute_tensor_parallel_shapes(
        base_shapes: Dict[str, <PERSON><PERSON>[int, ...]], tp_size: int
    ) -> Dict[str, <PERSON><PERSON>[int, ...]]:
        """
        Compute matrix shapes under tensor parallelism.

        Tensor parallelism partitions matrices along specific dimensions:
        - Column-parallel: Split output dimension (e.g., Q, K, V projections)
        - Row-parallel: Split input dimension (e.g., output projections)

        Args:
            base_shapes: Original matrix shapes
            tp_size: Tensor parallel size

        Returns:
            Modified shapes under tensor parallelism
        """
        if tp_size <= 1:
            return base_shapes.copy()

        parallel_shapes = {}

        # Define column-parallel and row-parallel operations
        column_parallel_ops = {
            "q_proj",
            "k_proj",
            "v_proj",
            "q_weight",
            "k_weight",
            "v_weight",
            "gate_proj",
            "up_proj",
            "gate_weight",
            "up_weight",
            "shared_expert_gate",
            "shared_expert_up",
            "routed_expert_gate",
            "routed_expert_up",
        }

        row_parallel_ops = {
            "o_proj",
            "down_proj",
            "o_weight",
            "down_weight",
            "shared_expert_down",
            "routed_expert_down",
        }

        for op_name, shape in base_shapes.items():
            if len(shape) < 2:
                # Skip non-matrix shapes (e.g., biases, scalars)
                parallel_shapes[op_name] = shape
                continue

            # Determine operation type from name
            base_op_name = op_name.split("_")[0] if "_" in op_name else op_name

            if any(col_op in op_name for col_op in column_parallel_ops):
                # Column-parallel: split output dimension (last dimension)
                new_shape = list(shape)
                new_shape[-1] = new_shape[-1] // tp_size
                parallel_shapes[op_name] = tuple(new_shape)

            elif any(row_op in op_name for row_op in row_parallel_ops):
                # Row-parallel: split input dimension (first dimension for weights)
                new_shape = list(shape)
                new_shape[0] = new_shape[0] // tp_size
                parallel_shapes[op_name] = tuple(new_shape)

            else:
                # Replicated operations (e.g., embeddings, layer norms)
                parallel_shapes[op_name] = shape

        return parallel_shapes

    @staticmethod
    def validate_parallel_config(
        config: ParallelConfig, model_config: Dict[str, Any]
    ) -> bool:
        """
        Validate that parallel configuration is feasible.

        Checks divisibility constraints and resource requirements for the given
        parallel configuration against the model architecture.

        Args:
            config: Parallel configuration
            model_config: Model configuration

        Returns:
            True if configuration is valid
        """
        errors = ParallelStrategyCalculator.get_validation_errors(config, model_config)
        return len(errors) == 0

    @staticmethod
    def get_validation_errors(
        config: ParallelConfig, model_config: Dict[str, Any]
    ) -> List[str]:
        """
        Get detailed validation errors for a parallel configuration.

        Args:
            config: Parallel configuration
            model_config: Model configuration

        Returns:
            List of validation error messages
        """
        errors = []

        # Basic parameter validation
        if config.tensor_parallel_size < 1:
            errors.append("tensor_parallel_size must be >= 1")
        if config.pipeline_parallel_size < 1:
            errors.append("pipeline_parallel_size must be >= 1")
        if config.data_parallel_size < 1:
            errors.append("data_parallel_size must be >= 1")
        if config.expert_parallel_size < 1:
            errors.append("expert_parallel_size must be >= 1")
        if config.expert_data_parallel_size < 1:
            errors.append("expert_data_parallel_size must be >= 1")

        # Extract model parameters
        hidden_size = model_config.get("hidden_size", 0)
        num_attention_heads = model_config.get("num_attention_heads", 0)
        num_key_value_heads = model_config.get(
            "num_key_value_heads", num_attention_heads
        )
        intermediate_size = model_config.get("intermediate_size", 0)
        moe_intermediate_size = model_config.get(
            "moe_intermediate_size", intermediate_size
        )
        n_routed_experts = model_config.get("n_routed_experts", 0)

        # Tensor parallelism validation
        tp_size = config.tensor_parallel_size
        if tp_size > 1:
            # Check attention head divisibility
            if num_attention_heads > 0 and num_attention_heads % tp_size != 0:
                errors.append(
                    f"num_attention_heads ({num_attention_heads}) not divisible by tensor_parallel_size ({tp_size})"
                )

            if num_key_value_heads > 0 and num_key_value_heads % tp_size != 0:
                errors.append(
                    f"num_key_value_heads ({num_key_value_heads}) not divisible by tensor_parallel_size ({tp_size})"
                )

            # Check MLP intermediate size divisibility
            if intermediate_size > 0 and intermediate_size % tp_size != 0:
                errors.append(
                    f"intermediate_size ({intermediate_size}) not divisible by tensor_parallel_size ({tp_size})"
                )

            if moe_intermediate_size > 0 and moe_intermediate_size % tp_size != 0:
                errors.append(
                    f"moe_intermediate_size ({moe_intermediate_size}) not divisible by tensor_parallel_size ({tp_size})"
                )

            # Check hidden size divisibility (for some operations)
            if hidden_size > 0 and hidden_size % tp_size != 0:
                errors.append(
                    f"hidden_size ({hidden_size}) not divisible by tensor_parallel_size ({tp_size})"
                )

        # Expert parallelism validation (for MoE models)
        ep_size = config.expert_parallel_size
        if ep_size > 1 and n_routed_experts > 0:
            if n_routed_experts % ep_size != 0:
                errors.append(
                    f"n_routed_experts ({n_routed_experts}) not divisible by expert_parallel_size ({ep_size})"
                )

        # Pipeline parallelism validation
        pp_size = config.pipeline_parallel_size
        num_layers = model_config.get("num_hidden_layers", 0)
        if pp_size > 1 and num_layers > 0:
            if num_layers < pp_size:
                errors.append(
                    f"num_hidden_layers ({num_layers}) must be >= pipeline_parallel_size ({pp_size})"
                )

        # Total parallelism constraint
        total_parallel_size = (
            config.tensor_parallel_size
            * config.pipeline_parallel_size
            * config.data_parallel_size
        )

        # Add expert parallelism for MoE models
        if n_routed_experts > 0:
            total_parallel_size *= (
                config.expert_parallel_size * config.expert_data_parallel_size
            )

        # Warn about very high parallelism (practical constraint)
        if total_parallel_size > 1024:
            errors.append(
                f"Total parallelism degree ({total_parallel_size}) is very high and may be impractical"
            )

        return errors

    @staticmethod
    def compute_communication_volume(
        model: BaseModel,
        parallel_config: ParallelConfig,
        sequence_length: int = 2048,
        batch_size: int = 1,
    ) -> Dict[str, int]:
        """
        Compute communication requirements for parallel execution.

        Estimates the volume of data that needs to be communicated between
        devices for different parallel strategies.

        Args:
            model: Model instance
            parallel_config: Parallel configuration
            sequence_length: Input sequence length
            batch_size: Batch size

        Returns:
            Communication volume breakdown (in bytes)
        """
        comm_volume = {
            "tensor_parallel": 0,
            "pipeline_parallel": 0,
            "data_parallel": 0,
            "expert_parallel": 0,
            "total": 0,
        }

        # Assume FP16 (2 bytes per element)
        bytes_per_element = 2

        # Get model configuration
        if not hasattr(model, "_parsed_config") or not model._parsed_config:
            return comm_volume

        config = model._parsed_config
        hidden_size = config.get("hidden_size", 0)
        num_layers = config.get("num_hidden_layers", 0)

        # Tensor parallelism communication
        tp_size = parallel_config.tensor_parallel_size
        if tp_size > 1:
            # All-reduce operations for row-parallel layers
            # Each transformer layer has attention output and MLP output
            per_layer_comm = (
                2 * batch_size * sequence_length * hidden_size * bytes_per_element
            )
            comm_volume["tensor_parallel"] = per_layer_comm * num_layers

        # Pipeline parallelism communication
        pp_size = parallel_config.pipeline_parallel_size
        if pp_size > 1:
            # Forward pass: send activations between pipeline stages
            # Backward pass: send gradients between pipeline stages
            activation_size = (
                batch_size * sequence_length * hidden_size * bytes_per_element
            )
            # Communication happens (pp_size - 1) times per forward/backward pass
            comm_volume["pipeline_parallel"] = 2 * activation_size * (pp_size - 1)

        # Data parallelism communication
        dp_size = parallel_config.data_parallel_size
        if dp_size > 1:
            # All-reduce gradients across data parallel replicas
            total_params = model.get_total_params()
            comm_volume["data_parallel"] = total_params * bytes_per_element

        # Expert parallelism communication (for MoE models)
        ep_size = parallel_config.expert_parallel_size
        if ep_size > 1 and hasattr(model, "_parsed_config"):
            n_routed_experts = config.get("n_routed_experts", 0)
            if n_routed_experts > 0:
                # All-to-all communication for expert routing
                moe_intermediate_size = config.get("moe_intermediate_size", 0)
                num_moe_layers = config.get("num_moe_layers", 0)

                if moe_intermediate_size > 0 and num_moe_layers > 0:
                    # Token routing communication
                    per_layer_expert_comm = (
                        batch_size
                        * sequence_length
                        * moe_intermediate_size
                        * bytes_per_element
                    )
                    comm_volume["expert_parallel"] = (
                        per_layer_expert_comm * num_moe_layers
                    )

        # Total communication volume
        comm_volume["total"] = (
            comm_volume["tensor_parallel"]
            + comm_volume["pipeline_parallel"]
            + comm_volume["data_parallel"]
            + comm_volume["expert_parallel"]
        )

        return comm_volume

    @staticmethod
    def estimate_communication_overhead(
        model: BaseModel,
        parallel_config: ParallelConfig,
        bandwidth_gbps: float = 100.0,
        latency_us: float = 10.0,
    ) -> Dict[str, float]:
        """
        Estimate communication overhead in terms of time.

        Args:
            model: Model instance
            parallel_config: Parallel configuration
            bandwidth_gbps: Network bandwidth in Gbps
            latency_us: Network latency in microseconds

        Returns:
            Communication time breakdown (in milliseconds)
        """
        comm_volume = ParallelStrategyCalculator.compute_communication_volume(
            model, parallel_config
        )

        # Convert bandwidth to bytes per second
        bandwidth_bps = bandwidth_gbps * 1e9 / 8  # Gbps to bytes per second
        latency_s = latency_us * 1e-6  # microseconds to seconds

        comm_time = {}

        for comm_type, volume_bytes in comm_volume.items():
            if volume_bytes > 0:
                # Time = latency + volume / bandwidth
                transfer_time = volume_bytes / bandwidth_bps
                total_time = latency_s + transfer_time
                comm_time[comm_type] = total_time * 1000  # Convert to milliseconds
            else:
                comm_time[comm_type] = 0.0

        return comm_time

    @staticmethod
    def suggest_optimal_parallel_config(
        model: BaseModel,
        num_devices: int,
        memory_per_device_gb: float = 80.0,
        sequence_length: int = 2048,
        batch_size: int = 1,
    ) -> List[ParallelConfig]:
        """
        Suggest optimal parallel configurations for given constraints.

        Args:
            model: Model instance
            num_devices: Total number of available devices
            memory_per_device_gb: Memory per device in GB
            sequence_length: Target sequence length
            batch_size: Target batch size

        Returns:
            List of suggested parallel configurations, ranked by efficiency
        """
        suggestions = []

        if not hasattr(model, "_parsed_config") or not model._parsed_config:
            return suggestions

        config = model._parsed_config
        hidden_size = config.get("hidden_size", 0)
        num_layers = config.get("num_hidden_layers", 0)
        num_heads = config.get("num_attention_heads", 0)
        intermediate_size = config.get("intermediate_size", 0)

        # Find valid tensor parallel sizes
        valid_tp_sizes = []
        for tp in [1, 2, 4, 8, 16, 32]:
            if tp > num_devices:
                break
            if (
                num_heads % tp == 0
                and intermediate_size % tp == 0
                and hidden_size % tp == 0
            ):
                valid_tp_sizes.append(tp)

        # Find valid pipeline parallel sizes
        valid_pp_sizes = []
        for pp in [1, 2, 4, 8, 16]:
            if pp > num_devices or pp > num_layers:
                break
            valid_pp_sizes.append(pp)

        # Generate configurations
        for tp in valid_tp_sizes:
            for pp in valid_pp_sizes:
                remaining_devices = num_devices // (tp * pp)
                if remaining_devices >= 1:
                    dp = remaining_devices

                    config_candidate = ParallelConfig(
                        tensor_parallel_size=tp,
                        pipeline_parallel_size=pp,
                        data_parallel_size=dp,
                    )

                    # Validate configuration
                    if ParallelStrategyCalculator.validate_parallel_config(
                        config_candidate, model._parsed_config
                    ):
                        # Estimate memory usage
                        memory_req = (
                            ParallelStrategyCalculator._estimate_memory_per_device(
                                model, config_candidate, sequence_length, batch_size
                            )
                        )

                        if memory_req <= memory_per_device_gb:
                            # Estimate communication overhead
                            comm_overhead = ParallelStrategyCalculator.estimate_communication_overhead(
                                model, config_candidate
                            )

                            # Score configuration (lower is better)
                            score = comm_overhead.get("total", 0) + tp * 0.1 + pp * 0.2

                            suggestions.append((config_candidate, score, memory_req))

        # Sort by score (lower is better)
        suggestions.sort(key=lambda x: x[1])

        # Return top configurations
        return [config for config, score, memory in suggestions[:5]]

    @staticmethod
    def _estimate_memory_per_device(
        model: BaseModel,
        parallel_config: ParallelConfig,
        sequence_length: int,
        batch_size: int,
    ) -> float:
        """
        Estimate memory usage per device for a parallel configuration.

        Args:
            model: Model instance
            parallel_config: Parallel configuration
            sequence_length: Input sequence length
            batch_size: Batch size

        Returns:
            Estimated memory usage per device in GB
        """
        # Get base memory requirements
        memory_breakdown = model.compute_memory_requirements(
            sequence_length, batch_size
        )

        # Adjust for parallelism
        tp_size = parallel_config.tensor_parallel_size
        pp_size = parallel_config.pipeline_parallel_size
        dp_size = parallel_config.data_parallel_size

        # Parameters are sharded across tensor parallel devices
        param_memory = memory_breakdown.get("parameters", 0) / tp_size

        # Activations are sharded across tensor parallel and pipeline parallel
        activation_memory = memory_breakdown.get("activations", 0) / (tp_size * pp_size)

        # Gradients follow the same pattern as parameters
        gradient_memory = memory_breakdown.get("gradients", 0) / tp_size

        # Optimizer states follow the same pattern as parameters
        optimizer_memory = memory_breakdown.get("optimizer_states", 0) / tp_size

        total_memory_bytes = (
            param_memory + activation_memory + gradient_memory + optimizer_memory
        )

        # Convert to GB
        return total_memory_bytes / (1024**3)

    @staticmethod
    def analyze_memory_requirements(
        model: BaseModel,
        parallel_config: ParallelConfig,
        sequence_length: int = 2048,
        batch_size: int = 1,
    ) -> Dict[str, Any]:
        """
        Analyze detailed memory requirements for parallel configuration.

        Args:
            model: Model instance
            parallel_config: Parallel configuration
            sequence_length: Input sequence length
            batch_size: Batch size

        Returns:
            Detailed memory analysis
        """
        # Get base memory requirements
        base_memory = model.compute_memory_requirements(sequence_length, batch_size)

        # Calculate memory per device
        memory_per_device = ParallelStrategyCalculator._estimate_memory_per_device(
            model, parallel_config, sequence_length, batch_size
        )

        # Calculate memory efficiency
        total_devices = (
            parallel_config.tensor_parallel_size
            * parallel_config.pipeline_parallel_size
            * parallel_config.data_parallel_size
        )

        total_memory_gb = sum(base_memory.values()) / (1024**3)
        distributed_memory_gb = memory_per_device * total_devices
        memory_efficiency = (
            total_memory_gb / distributed_memory_gb if distributed_memory_gb > 0 else 0
        )

        # Analyze memory breakdown by component
        tp_size = parallel_config.tensor_parallel_size
        pp_size = parallel_config.pipeline_parallel_size

        memory_breakdown = {}
        for component, memory_bytes in base_memory.items():
            if component in ["parameters", "gradients", "optimizer_states"]:
                # These are sharded across tensor parallel devices
                memory_breakdown[component] = {
                    "total_gb": memory_bytes / (1024**3),
                    "per_device_gb": memory_bytes / (tp_size * 1024**3),
                    "sharding_strategy": "tensor_parallel",
                }
            elif component == "activations":
                # Activations are sharded across tensor and pipeline parallel
                memory_breakdown[component] = {
                    "total_gb": memory_bytes / (1024**3),
                    "per_device_gb": memory_bytes / (tp_size * pp_size * 1024**3),
                    "sharding_strategy": "tensor_and_pipeline_parallel",
                }
            else:
                memory_breakdown[component] = {
                    "total_gb": memory_bytes / (1024**3),
                    "per_device_gb": memory_bytes / (1024**3),
                    "sharding_strategy": "replicated",
                }

        return {
            "total_memory_gb": total_memory_gb,
            "memory_per_device_gb": memory_per_device,
            "distributed_total_memory_gb": distributed_memory_gb,
            "memory_efficiency": memory_efficiency,
            "total_devices": total_devices,
            "memory_breakdown": memory_breakdown,
            "parallel_config": {
                "tensor_parallel_size": parallel_config.tensor_parallel_size,
                "pipeline_parallel_size": parallel_config.pipeline_parallel_size,
                "data_parallel_size": parallel_config.data_parallel_size,
                "expert_parallel_size": parallel_config.expert_parallel_size,
                "expert_data_parallel_size": parallel_config.expert_data_parallel_size,
            },
        }

    @staticmethod
    def analyze_performance_characteristics(
        model: BaseModel,
        parallel_config: ParallelConfig,
        sequence_length: int = 2048,
        batch_size: int = 1,
        bandwidth_gbps: float = 100.0,
        latency_us: float = 10.0,
        compute_tflops: float = 312.0,
    ) -> Dict[str, Any]:
        """
        Analyze performance characteristics for parallel configuration.

        Args:
            model: Model instance
            parallel_config: Parallel configuration
            sequence_length: Input sequence length
            batch_size: Batch size
            bandwidth_gbps: Network bandwidth in Gbps
            latency_us: Network latency in microseconds
            compute_tflops: Peak compute performance in TFLOPS

        Returns:
            Performance analysis
        """
        # Get FLOP requirements
        flops_breakdown = model.compute_flops(sequence_length, batch_size)
        total_flops = sum(flops_breakdown.values())

        # Get communication requirements
        comm_volume = ParallelStrategyCalculator.compute_communication_volume(
            model, parallel_config, sequence_length, batch_size
        )
        comm_time = ParallelStrategyCalculator.estimate_communication_overhead(
            model, parallel_config, bandwidth_gbps, latency_us
        )

        # Estimate compute time
        total_devices = (
            parallel_config.tensor_parallel_size
            * parallel_config.pipeline_parallel_size
            * parallel_config.data_parallel_size
        )

        # Adjust for expert parallelism in MoE models
        if (
            hasattr(model, "_parsed_config")
            and model._parsed_config.get("n_routed_experts", 0) > 0
        ):
            total_devices *= (
                parallel_config.expert_parallel_size
                * parallel_config.expert_data_parallel_size
            )

        # Compute time per device (assuming perfect parallelization)
        compute_tflops_per_device = compute_tflops
        compute_time_ms = (
            (total_flops / total_devices) / (compute_tflops_per_device * 1e12) * 1000
        )

        # Pipeline parallelism affects compute utilization
        if parallel_config.pipeline_parallel_size > 1:
            # Pipeline bubble overhead (simplified model)
            pipeline_efficiency = 0.8  # Typical efficiency with micro-batching
            compute_time_ms /= pipeline_efficiency

        # Total iteration time
        total_time_ms = compute_time_ms + comm_time.get("total", 0)

        # Calculate efficiency metrics
        compute_efficiency = compute_time_ms / total_time_ms if total_time_ms > 0 else 0
        communication_overhead = (
            comm_time.get("total", 0) / total_time_ms if total_time_ms > 0 else 0
        )

        # Throughput analysis
        tokens_per_second = (
            (batch_size * sequence_length) / (total_time_ms / 1000)
            if total_time_ms > 0
            else 0
        )

        # Memory bandwidth utilization
        memory_analysis = ParallelStrategyCalculator.analyze_memory_requirements(
            model, parallel_config, sequence_length, batch_size
        )

        # Model FLOPS utilization (MFU)
        theoretical_flops_per_device = compute_tflops_per_device * 1e12
        actual_flops_per_device = (
            (total_flops / total_devices) / (total_time_ms / 1000)
            if total_time_ms > 0
            else 0
        )
        model_flops_utilization = (
            actual_flops_per_device / theoretical_flops_per_device
            if theoretical_flops_per_device > 0
            else 0
        )

        return {
            "compute_time_ms": compute_time_ms,
            "communication_time_ms": comm_time.get("total", 0),
            "total_time_ms": total_time_ms,
            "compute_efficiency": compute_efficiency,
            "communication_overhead": communication_overhead,
            "model_flops_utilization": model_flops_utilization,
            "tokens_per_second": tokens_per_second,
            "total_devices": total_devices,
            "flops_breakdown": flops_breakdown,
            "communication_breakdown": comm_time,
            "memory_per_device_gb": memory_analysis["memory_per_device_gb"],
            "parallel_config": {
                "tensor_parallel_size": parallel_config.tensor_parallel_size,
                "pipeline_parallel_size": parallel_config.pipeline_parallel_size,
                "data_parallel_size": parallel_config.data_parallel_size,
                "expert_parallel_size": parallel_config.expert_parallel_size,
                "expert_data_parallel_size": parallel_config.expert_data_parallel_size,
            },
            "hardware_assumptions": {
                "bandwidth_gbps": bandwidth_gbps,
                "latency_us": latency_us,
                "compute_tflops": compute_tflops,
            },
        }

    @staticmethod
    def generate_optimization_suggestions(
        model: BaseModel, current_config: ParallelConfig, constraints: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate optimization suggestions for parallel configuration.

        Args:
            model: Model instance
            current_config: Current parallel configuration
            constraints: Hardware and performance constraints

        Returns:
            List of optimization suggestions
        """
        suggestions = []

        # Extract constraints
        num_devices = constraints.get("num_devices", 8)
        memory_per_device_gb = constraints.get("memory_per_device_gb", 80.0)
        target_throughput = constraints.get("target_throughput_tokens_per_sec", None)
        max_latency_ms = constraints.get("max_latency_ms", None)
        bandwidth_gbps = constraints.get("bandwidth_gbps", 100.0)
        latency_us = constraints.get("latency_us", 10.0)

        # Analyze current configuration
        current_perf = ParallelStrategyCalculator.analyze_performance_characteristics(
            model, current_config, bandwidth_gbps=bandwidth_gbps, latency_us=latency_us
        )
        current_memory = ParallelStrategyCalculator.analyze_memory_requirements(
            model, current_config
        )

        # Generate alternative configurations
        alternative_configs = (
            ParallelStrategyCalculator.suggest_optimal_parallel_config(
                model, num_devices, memory_per_device_gb
            )
        )

        for alt_config in alternative_configs:
            if alt_config == current_config:
                continue

            alt_perf = ParallelStrategyCalculator.analyze_performance_characteristics(
                model, alt_config, bandwidth_gbps=bandwidth_gbps, latency_us=latency_us
            )
            alt_memory = ParallelStrategyCalculator.analyze_memory_requirements(
                model, alt_config
            )

            # Compare performance
            throughput_improvement = (
                (alt_perf["tokens_per_second"] - current_perf["tokens_per_second"])
                / current_perf["tokens_per_second"]
                if current_perf["tokens_per_second"] > 0
                else 0
            )
            latency_change = alt_perf["total_time_ms"] - current_perf["total_time_ms"]
            memory_change = (
                alt_memory["memory_per_device_gb"]
                - current_memory["memory_per_device_gb"]
            )

            # Generate suggestion
            suggestion = {
                "config": alt_config,
                "performance_impact": {
                    "throughput_improvement_percent": throughput_improvement * 100,
                    "latency_change_ms": latency_change,
                    "memory_change_gb": memory_change,
                    "compute_efficiency": alt_perf["compute_efficiency"],
                    "communication_overhead": alt_perf["communication_overhead"],
                },
                "meets_constraints": True,
                "recommendation_reasons": [],
            }

            # Check constraints
            if target_throughput and alt_perf["tokens_per_second"] < target_throughput:
                suggestion["meets_constraints"] = False
                suggestion["recommendation_reasons"].append(
                    f"Throughput ({alt_perf['tokens_per_second']:.1f}) below target ({target_throughput})"
                )

            if max_latency_ms and alt_perf["total_time_ms"] > max_latency_ms:
                suggestion["meets_constraints"] = False
                suggestion["recommendation_reasons"].append(
                    f"Latency ({alt_perf['total_time_ms']:.1f}ms) exceeds limit ({max_latency_ms}ms)"
                )

            if alt_memory["memory_per_device_gb"] > memory_per_device_gb:
                suggestion["meets_constraints"] = False
                suggestion["recommendation_reasons"].append(
                    f"Memory usage ({alt_memory['memory_per_device_gb']:.1f}GB) exceeds limit ({memory_per_device_gb}GB)"
                )

            # Add positive recommendation reasons
            if throughput_improvement > 0.1:
                suggestion["recommendation_reasons"].append(
                    f"Improves throughput by {throughput_improvement*100:.1f}%"
                )

            if latency_change < -10:
                suggestion["recommendation_reasons"].append(
                    f"Reduces latency by {-latency_change:.1f}ms"
                )

            if (
                alt_perf["compute_efficiency"]
                > current_perf["compute_efficiency"] + 0.05
            ):
                suggestion["recommendation_reasons"].append(
                    f"Improves compute efficiency from {current_perf['compute_efficiency']:.1%} to {alt_perf['compute_efficiency']:.1%}"
                )

            if (
                alt_perf["communication_overhead"]
                < current_perf["communication_overhead"] - 0.05
            ):
                suggestion["recommendation_reasons"].append(
                    f"Reduces communication overhead from {current_perf['communication_overhead']:.1%} to {alt_perf['communication_overhead']:.1%}"
                )

            suggestions.append(suggestion)

        # Sort suggestions by performance improvement
        suggestions.sort(
            key=lambda x: x["performance_impact"]["throughput_improvement_percent"],
            reverse=True,
        )

        # Add general optimization suggestions
        general_suggestions = []

        if current_perf["communication_overhead"] > 0.3:
            general_suggestions.append(
                {
                    "type": "general",
                    "suggestion": "High communication overhead detected. Consider reducing tensor parallelism or improving network bandwidth.",
                    "current_overhead": current_perf["communication_overhead"],
                }
            )

        if current_perf["model_flops_utilization"] < 0.3:
            general_suggestions.append(
                {
                    "type": "general",
                    "suggestion": "Low model FLOPS utilization. Consider increasing batch size or sequence length.",
                    "current_mfu": current_perf["model_flops_utilization"],
                }
            )

        if current_memory["memory_efficiency"] < 0.7:
            general_suggestions.append(
                {
                    "type": "general",
                    "suggestion": "Low memory efficiency. Consider adjusting parallelism strategy to better utilize available memory.",
                    "current_efficiency": current_memory["memory_efficiency"],
                }
            )

        return {
            "configuration_alternatives": suggestions[:5],  # Top 5 alternatives
            "general_suggestions": general_suggestions,
            "current_performance": current_perf,
            "current_memory": current_memory,
        }

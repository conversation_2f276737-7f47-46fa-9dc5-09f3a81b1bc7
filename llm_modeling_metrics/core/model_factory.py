"""
Model factory for creating appropriate model instances based on architecture.
"""

from typing import Any, Dict, Optional, Type

from .base_model import BaseModel
from .config_manager import ConfigManager


class ModelFactory:
    """Factory class for creating model instances based on architecture."""

    _model_registry: Dict[str, Type[BaseModel]] = {}
    _config_manager: Optional[ConfigManager] = None
    _default_models_registered: bool = False

    @classmethod
    def _ensure_default_models_registered(cls) -> None:
        """Ensure default model types are registered."""
        if not cls._default_models_registered:
            try:
                # Import here to avoid circular imports
                from ..models.dense_model import DenseModel
                from ..models.moe_model import MoEModel

                # Register default model types
                cls._model_registry["moe"] = MoEModel
                cls._model_registry["dense"] = DenseModel

                cls._default_models_registered = True
            except ImportError as e:
                # If models can't be imported, continue without default registration
                # This allows the factory to work even if model classes aren't available
                pass

    @classmethod
    def set_config_manager(cls, config_manager: ConfigManager) -> None:
        """
        Set the configuration manager for fetching model configs.

        Args:
            config_manager: ConfigManager instance
        """
        cls._config_manager = config_manager

    @classmethod
    def register_model(cls, architecture: str, model_class: Type[BaseModel]) -> None:
        """
        Register a new model architecture.

        Args:
            architecture: Architecture name (e.g., 'llama', 'deepseek')
            model_class: Model class that extends BaseModel

        Raises:
            TypeError: If model_class is not a subclass of BaseModel
        """
        if not issubclass(model_class, BaseModel):
            raise TypeError(
                f"Model class {model_class} must be a subclass of BaseModel"
            )

        cls._model_registry[architecture.lower()] = model_class

    @classmethod
    def create_model(cls, model_name: str, config: Optional[Any] = None) -> BaseModel:
        """
        Create appropriate model instance based on architecture.

        All model names are supported by detecting architecture from config/name
        and falling back to default implementations when needed.

        Args:
            model_name: Name of the model
            config: Model configuration object (will be fetched if None)

        Returns:
            BaseModel instance for the detected or fallback architecture

        Raises:
            ConfigurationError: If configuration cannot be fetched
            ModelNotSupportedError: Only if no fallback architecture is available
        """
        # Ensure default models are registered
        cls._ensure_default_models_registered()

        # Fetch config if not provided
        if config is None:
            if cls._config_manager is None:
                cls._config_manager = ConfigManager()
            config = cls._config_manager.fetch_config(model_name)

        # Determine architecture from config
        architecture = cls._detect_architecture(model_name, config)

        # Get model class from registry
        model_class = cls._model_registry.get(architecture.lower())

        if model_class is None:
            # Fallback to dense architecture if detected architecture is not registered
            # This ensures all models are "supported" by using a default implementation
            model_class = cls._model_registry.get("dense")
            if model_class is None:
                raise ModelNotSupportedError(
                    f"No fallback architecture available. "
                    f"Supported architectures: {list(cls._model_registry.keys())}"
                )

        # Create and return model instance
        return model_class(model_name, config)

    @classmethod
    def _detect_architecture(cls, model_name: str, config: Dict[str, Any]) -> str:
        """
        Detect model architecture from config and model name.

        Models are classified into two types:
        - 'dense': Standard transformer models where all parameters are active
        - 'moe': Mixture of Experts models with sparse activation

        Args:
            model_name: Name of the model
            config: Model configuration dictionary

        Returns:
            Detected architecture name ('dense' or 'moe')
        """
        # Check for MoE indicators in config
        moe_indicators = [
            "num_experts_per_tok",  # DeepSeek V3 style
            "num_experts",  # Generic MoE
            "n_routed_experts",  # DeepSeek V2 style
            "experts_per_token",  # Alternative naming
            "num_local_experts",  # Mixtral style
            "moe_num_experts",  # Another common naming
        ]

        if "text_config" in config:
            config = config["text_config"]
        # print(f'{config=}')
        # Check if any MoE indicator exists and has a value > 0
        for indicator in moe_indicators:
            if indicator in config and config.get(indicator, 0) > 0:
                return "moe"

        # Default to dense for all other models
        return "dense"

    @classmethod
    def get_supported_architectures(cls) -> list[str]:
        """
        Get list of supported model architectures.

        Returns:
            List of supported architecture names
        """
        return list(cls._model_registry.keys())

    @classmethod
    def is_architecture_supported(cls, architecture: str) -> bool:
        """
        Check if an architecture is supported.

        Args:
            architecture: Architecture name to check

        Returns:
            True if architecture is supported, False otherwise
        """
        return architecture.lower() in cls._model_registry

    @classmethod
    def get_model_class(cls, architecture: str) -> Optional[Type[BaseModel]]:
        """
        Get the model class for a specific architecture.

        Args:
            architecture: Architecture name

        Returns:
            Model class or None if not found
        """
        return cls._model_registry.get(architecture.lower())

    @classmethod
    def unregister_model(cls, architecture: str) -> bool:
        """
        Unregister a model architecture.

        Args:
            architecture: Architecture name to unregister

        Returns:
            True if architecture was unregistered, False if not found
        """
        architecture_lower = architecture.lower()
        if architecture_lower in cls._model_registry:
            del cls._model_registry[architecture_lower]
            return True
        return False

    @classmethod
    def clear_registry(cls) -> None:
        """Clear all registered model architectures."""
        cls._model_registry.clear()


class ModelNotSupportedError(Exception):
    """Raised when a model architecture is not supported."""

    pass


class ConfigurationError(Exception):
    """Raised when model configuration cannot be fetched or is invalid."""

    pass


def create_model(model_name: str, config: Optional[Any] = None) -> BaseModel:
    """
    Convenience function to create a model with default factory settings.

    This is the simplest way to create a model - just provide the model name
    and everything else (config fetching, architecture detection, model creation)
    is handled automatically.

    Args:
        model_name: Name of the model (e.g., 'deepseek-ai/DeepSeek-V3')
        config: Model configuration object (will be fetched if None)

    Returns:
        BaseModel instance for the detected architecture

    Raises:
        ConfigurationError: If configuration cannot be fetched

    Example:
        >>> model = create_model("deepseek-ai/DeepSeek-V3")
        >>> print(f"Created {type(model).__name__} with {model.get_total_params() / 1e9:.1f}B parameters")
    """
    return ModelFactory.create_model(model_name, config)

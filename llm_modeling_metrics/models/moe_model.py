"""
Mixture of Experts (MoE) model implementation for architectures like DeepSeek V2/V3.
"""

import math
import sys
from typing import Any, Dict, List, Optional, Tuple

from loguru import logger

from ..core.base_model import BaseModel, ModelMetrics, ParallelConfig
from ..core.operators import (
    AttentionLayer,
    AttentionOperator,
    CommunicationOperator,
    EmbeddingOperator,
    FFNLayer,
    HardwareSpecs,
    LayerNormOperator,
    MatMulOperator,
    MLAAttentionLayer,
    MLAAttentionOperator,
    MoELayer,
    OperatorMetrics,
)

logger.remove(0)
logger.add(
    sys.stderr,
    format="<level>{level}</level> <cyan>{file}</cyan>:<white>{line}</white> <level>{message}</level>",
)


class MoEModel(BaseModel):
    """
    Implementation for Mixture of Experts transformer models.

    Supports MoE architectures like DeepSeek V2/V3 with both shared and routed experts,
    providing accurate parameter counts, FLOP calculations, and per-token efficiency analysis.
    """

    def _parse_config(self) -> None:
        """Parse the model configuration and extract key parameters."""
        if self.config is None:
            raise ValueError("Model configuration is required")

        # Helper function to get config values (handles both dict and object configs)
        def get_config_value(key, default=None):
            if hasattr(self.config, "get") and callable(getattr(self.config, "get")):
                return self.config.get(key, default)
            elif hasattr(self.config, key):
                return getattr(self.config, key)
            elif isinstance(self.config, dict):
                return self.config.get(key, default)
            else:
                return default

        n_shared_experts = get_config_value("n_shared_experts", 0)

        if get_config_value("n_routed_experts"):
            n_routed_experts = get_config_value("n_routed_experts")
        elif get_config_value("num_experts"):
            n_routed_experts = get_config_value("num_experts")
        elif get_config_value("num_local_experts"):
            n_routed_experts = get_config_value("num_local_experts")
        elif get_config_value("moe_num_experts"):
            n_routed_experts = get_config_value("moe_num_experts")
        else:
            raise RuntimeError("Cannot find num_experts in config")

        num_key_value_heads = (
            get_config_value("num_key_value_heads")
            or get_config_value("num_attention_groups")
            or get_config_value("num_attention_heads")
        )

        num_experts_per_tok = get_config_value(
            "num_experts_per_tok",
            get_config_value("experts_per_token", get_config_value("moe_top_k", 1)),
        )

        # Handle grouped query attention (GQA)
        # if self._parsed_config['num_key_value_heads'] is None:
        #     self._parsed_config['num_key_value_heads'] = get_config_value('num_attention_heads')

        self._parsed_config = {
            "hidden_size": get_config_value("hidden_size")
            or get_config_value("n_embd")
            or get_config_value("d_model")
            or 0,
            "num_hidden_layers": get_config_value("num_hidden_layers")
            or get_config_value("n_layer")
            or get_config_value("num_layers")
            or 0,
            "num_attention_heads": get_config_value("num_attention_heads")
            or get_config_value("n_head")
            or get_config_value("num_heads")
            or 0,
            "num_key_value_heads": num_key_value_heads,
            "num_attention_groups": get_config_value("num_attention_groups", None),
            "intermediate_size": get_config_value("intermediate_size")
            or get_config_value("n_inner")
            or get_config_value("d_ff")
            or 0,
            "vocab_size": get_config_value("vocab_size", 0),
            "max_position_embeddings": get_config_value("max_position_embeddings")
            or get_config_value("n_positions")
            or get_config_value("max_seq_len")
            or 0,
            "model_type": get_config_value("model_type", "unknown"),
            "tie_word_embeddings": get_config_value("tie_word_embeddings", False),
            "rms_norm_eps": get_config_value("rms_norm_eps", 1e-6),
            # MoE-specific parameters (handle different naming conventions)
            "n_shared_experts": n_shared_experts,
            "n_routed_experts": n_routed_experts,
            "num_experts_per_tok": num_experts_per_tok,
            "moe_intermediate_size": get_config_value(
                "moe_intermediate_size", get_config_value("intermediate_size", 0)
            ),
            "moe_layer_freq": get_config_value("moe_layer_freq", 1),
            "first_k_dense_replace": get_config_value("first_k_dense_replace", 0),
            "routed_scaling_factor": get_config_value("routed_scaling_factor", 1.0),
            "ep_size": get_config_value("ep_size", 1),
            "topk_method": get_config_value("topk_method", "greedy"),
            "scoring_func": get_config_value("scoring_func", "softmax"),
            "aux_loss_alpha": get_config_value("aux_loss_alpha", 0.001),
            # DeepSeek-specific attention parameters
            "kv_lora_rank": get_config_value("kv_lora_rank", None),
            "q_lora_rank": get_config_value("q_lora_rank", None),
            "qk_rope_head_dim": get_config_value("qk_rope_head_dim", None),
            "v_head_dim": get_config_value("v_head_dim", None),
            "qk_nope_head_dim": get_config_value("qk_nope_head_dim", None),
        }

        if get_config_value("share_q_dim", None):
            self._parsed_config["share_q_dim"] = get_config_value("share_q_dim")

        if get_config_value("moe_layers_enum", None):
            self._parsed_config["moe_layers_enum"] = get_config_value("moe_layers_enum")

        # Handle missing intermediate_size (common in older models)
        if (
            self._parsed_config["intermediate_size"] == 0
            or self._parsed_config["intermediate_size"] is None
        ):
            # Default to 4x hidden_size (standard for most transformer models)
            self._parsed_config["intermediate_size"] = (
                self._parsed_config["hidden_size"] * 4
            )

        # Handle missing moe_intermediate_size
        if (
            self._parsed_config["moe_intermediate_size"] == 0
            or self._parsed_config["moe_intermediate_size"] is None
        ):
            # Default to same as intermediate_size for MoE layers
            self._parsed_config["moe_intermediate_size"] = self._parsed_config[
                "intermediate_size"
            ]

        # Calculate head dimensions
        if get_config_value("head_dim"):
            self._parsed_config["head_dim"] = get_config_value("head_dim")
        else:
            self._parsed_config["head_dim"] = self._parsed_config[
                "hidden_size"
            ] // get_config_value("num_attention_heads")

        # Determine layer types (dense vs MoE)
        self._compute_layer_distribution()

        # Validate configuration
        self._validate_config()

    def _compute_layer_distribution(self) -> None:
        """Compute which layers are dense vs MoE based on configuration."""
        num_layers = self._parsed_config["num_hidden_layers"]
        moe_freq = self._parsed_config["moe_layer_freq"]

        dense_layers = []
        moe_layers = []

        if "moe_layers_enum" in self._parsed_config:
            moe_layer_idx = self._parsed_config["moe_layers_enum"].split(",")
            moe_layer_idx = [int(idx) for idx in moe_layer_idx]
            for layer_idx in range(num_layers):
                if layer_idx in moe_layer_idx:
                    moe_layers.append(layer_idx)
                else:
                    dense_layers.append(layer_idx)
        else:
            first_k_dense = self._parsed_config["first_k_dense_replace"]

            # Determine which layers are MoE vs dense

            for layer_idx in range(num_layers):
                if layer_idx < first_k_dense:
                    # First k layers are always dense
                    dense_layers.append(layer_idx)
                elif moe_freq == 1:
                    # Every layer after first_k_dense is MoE
                    moe_layers.append(layer_idx)
                else:
                    # MoE layers appear every moe_freq layers
                    if (layer_idx - first_k_dense) % moe_freq == 0:
                        moe_layers.append(layer_idx)
                    else:
                        dense_layers.append(layer_idx)

        self._parsed_config["dense_layers"] = dense_layers
        self._parsed_config["moe_layers"] = moe_layers
        self._parsed_config["num_dense_layers"] = len(dense_layers)
        self._parsed_config["num_moe_layers"] = len(moe_layers)

    def _validate_config(self) -> None:
        """Validate the parsed configuration."""
        required_params = [
            "hidden_size",
            "num_hidden_layers",
            "num_attention_heads",
            "vocab_size",
        ]
        for param in required_params:
            if self._parsed_config.get(param, 0) <= 0:
                raise ValueError(f"Invalid or missing configuration parameter: {param}")

        # Validate MoE-specific parameters
        # Check if this is actually a MoE model (has routed experts or shared experts)
        has_routed_experts = self._parsed_config["n_routed_experts"] > 0
        has_shared_experts = self._parsed_config["n_shared_experts"] > 0

        if not has_routed_experts and not has_shared_experts:
            raise ValueError(
                "Model appears to be classified as MoE but has no routed or shared experts. "
                "This model should probably use a dense architecture instead."
            )

        if has_routed_experts:
            if self._parsed_config["num_experts_per_tok"] <= 0:
                raise ValueError(
                    "num_experts_per_tok must be > 0 when using routed experts"
                )

            if (
                self._parsed_config["num_experts_per_tok"]
                > self._parsed_config["n_routed_experts"]
            ):
                raise ValueError(
                    f"num_experts_per_tok ({self._parsed_config['num_experts_per_tok']}) cannot exceed n_routed_experts ({self._parsed_config['n_routed_experts']})"
                )

        # Only validate moe_intermediate_size if we actually have MoE layers
        if (has_routed_experts or has_shared_experts) and self._parsed_config[
            "moe_intermediate_size"
        ] <= 0:
            raise ValueError("moe_intermediate_size must be > 0 for MoE models")

        # Validate head dimensions
        if (
            self._parsed_config["hidden_size"]
            % self._parsed_config["num_attention_heads"]
            != 0
        ):
            raise ValueError("hidden_size must be divisible by num_attention_heads")

    def compute_attention_params(self) -> int:
        """
        Compute the number of parameters in attention layers using operators.

        For DeepSeek models, attention may use Multi-head Latent Attention (MLA)
        with compressed KV representations and LoRA-style projections.

        Returns:
            Number of parameters in all attention layers
        """
        hidden_size = self._parsed_config["hidden_size"]
        num_layers = self._parsed_config["num_hidden_layers"]
        num_heads = self._parsed_config["num_attention_heads"]
        num_kv_heads = self._parsed_config["num_key_value_heads"]

        # DeepSeek-specific attention parameters
        kv_lora_rank = self._parsed_config.get("kv_lora_rank")
        q_lora_rank = self._parsed_config.get("q_lora_rank")
        qk_rope_head_dim = self._parsed_config.get("qk_rope_head_dim", 64)
        v_head_dim = self._parsed_config.get("v_head_dim", 128)
        qk_nope_head_dim = self._parsed_config.get("qk_nope_head_dim", 128)

        if kv_lora_rank is not None:
            # Multi-head Latent Attention (MLA) using MLAAttentionOperator
            attention_layer = MLAAttentionLayer(
                hidden_size=hidden_size,
                num_heads=num_heads,
                kv_lora_rank=kv_lora_rank,
                q_lora_rank=q_lora_rank,
                qk_rope_head_dim=qk_rope_head_dim,
                qk_nope_head_dim=qk_nope_head_dim,
                v_head_dim=v_head_dim,
                precision="fp16",  # Precision doesn't affect parameter count
            )
        else:
            # Standard multi-head attention using AttentionOperator
            attention_layer = AttentionLayer(
                hidden_size=hidden_size,
                num_heads=num_heads,
                num_kv_heads=num_kv_heads,
                head_dim=self._parsed_config["head_dim"],
                share_q_dim=(
                    self._parsed_config["share_q_dim"]
                    if "share_q_dim" in self._parsed_config
                    else None
                ),
                precision="fp16",  # Precision doesn't affect parameter count
            )

        params_per_layer = attention_layer.compute_params_num()
        return params_per_layer * num_layers

    def compute_mlp_params(self) -> int:
        """
        Compute the number of parameters in MLP/feed-forward layers using operators.

        Includes both dense MLP layers and MoE layers with shared and routed experts.

        Returns:
            Number of parameters in all MLP layers
        """
        hidden_size = self._parsed_config["hidden_size"]
        intermediate_size = self._parsed_config["intermediate_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        n_shared_experts = self._parsed_config["n_shared_experts"]
        n_routed_experts = self._parsed_config["n_routed_experts"]
        num_dense_layers = self._parsed_config["num_dense_layers"]
        num_moe_layers = self._parsed_config["num_moe_layers"]

        total_params = 0

        # Dense MLP layers using FFNLayer
        if num_dense_layers > 0:
            dense_mlp_op = FFNLayer(
                hidden_size=hidden_size,
                intermediate_size=intermediate_size,
                precision="fp16",  # Precision doesn't affect parameter count
            )
            dense_params_per_layer = dense_mlp_op.compute_params_bytes()
            total_params += dense_params_per_layer * num_dense_layers

        # MoE layers using MoELayer
        if num_moe_layers > 0:
            moe_op = MoELayer(
                hidden_size=hidden_size,
                intermediate_size=moe_intermediate_size,
                # num_experts=n_shared_experts + n_routed_experts,
                num_shared_experts=n_shared_experts,
                num_routed_experts=n_routed_experts,
                experts_per_token=self._parsed_config["num_experts_per_tok"],
                precision="fp16",
            )
            moe_params_per_layer = moe_op.compute_params_bytes()
            total_params += moe_params_per_layer * num_moe_layers

        return total_params

    def _compute_dense_mlp_params(
        self, hidden_size: int, intermediate_size: int
    ) -> int:
        """Compute parameters for a dense MLP layer."""
        # Gate, up, and down projections for gated activation (SwiGLU, etc.)
        gate_params = hidden_size * intermediate_size
        up_params = hidden_size * intermediate_size
        down_params = intermediate_size * hidden_size

        return gate_params + up_params + down_params

    def _compute_moe_mlp_params(
        self,
        hidden_size: int,
        moe_intermediate_size: int,
        n_shared_experts: int,
        n_routed_experts: int,
    ) -> int:
        """Compute parameters for an MoE layer."""
        total_params = 0

        # Shared experts (always activated)
        if n_shared_experts > 0:
            shared_expert_params = self._compute_dense_mlp_params(
                hidden_size, moe_intermediate_size
            )
            total_params += shared_expert_params * n_shared_experts

        # Routed experts (selectively activated)
        if n_routed_experts > 0:
            routed_expert_params = self._compute_dense_mlp_params(
                hidden_size, moe_intermediate_size
            )
            total_params += routed_expert_params * n_routed_experts

        # Router/gating network: hidden_size * n_routed_experts
        router_params = hidden_size * n_routed_experts
        total_params += router_params

        return total_params

    def compute_embedding_params(self) -> int:
        """
        Compute the number of parameters in embedding layers.

        Returns:
            Number of parameters in embedding layers
        """
        vocab_size = self._parsed_config["vocab_size"]
        hidden_size = self._parsed_config["hidden_size"]
        tie_embeddings = self._parsed_config["tie_word_embeddings"]

        # Token embeddings
        token_embedding_params = vocab_size * hidden_size

        # Output embeddings (language modeling head)
        if tie_embeddings:
            output_embedding_params = 0
        else:
            output_embedding_params = vocab_size * hidden_size

        return token_embedding_params + output_embedding_params

    def compute_active_params_per_token(
        self, expert_parameter_dtype: str = None
    ) -> int:
        """
        Compute the number of parameters activated per token.

        This is a key efficiency metric for MoE models, showing how many parameters
        are actually used for each token despite the large total parameter count.

        Args:
            expert_parameter_dtype: Data type for expert parameters (for compatibility, not used in count)

        Returns:
            Number of parameters activated per token
        """
        # All attention and embedding parameters are always active
        attention_params = self.compute_attention_params()
        embedding_params = self.compute_embedding_params()

        # Dense MLP layers are always fully active
        hidden_size = self._parsed_config["hidden_size"]
        intermediate_size = self._parsed_config["intermediate_size"]
        num_dense_layers = self._parsed_config["num_dense_layers"]

        dense_mlp_params = 0
        if num_dense_layers > 0:
            dense_params_per_layer = self._compute_dense_mlp_params(
                hidden_size, intermediate_size
            )
            dense_mlp_params = dense_params_per_layer * num_dense_layers

        # MoE layers: only shared experts + selected routed experts are active
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        n_shared_experts = self._parsed_config["n_shared_experts"]
        num_experts_per_tok = self._parsed_config["num_experts_per_tok"]
        num_moe_layers = self._parsed_config["num_moe_layers"]

        moe_active_params = 0
        if num_moe_layers > 0:
            # Shared experts (always active)
            shared_expert_params = 0
            if n_shared_experts > 0:
                shared_params_per_expert = self._compute_dense_mlp_params(
                    hidden_size, moe_intermediate_size
                )
                shared_expert_params = shared_params_per_expert * n_shared_experts

            # Routed experts (only num_experts_per_tok are active)
            routed_expert_params = 0
            if num_experts_per_tok > 0:
                routed_params_per_expert = self._compute_dense_mlp_params(
                    hidden_size, moe_intermediate_size
                )
                routed_expert_params = routed_params_per_expert * num_experts_per_tok

            # Router parameters (always active)
            router_params = hidden_size * self._parsed_config["n_routed_experts"]

            moe_params_per_layer = (
                shared_expert_params + routed_expert_params + router_params
            )
            moe_active_params = moe_params_per_layer * num_moe_layers

        logger.debug(
            f"Shared experts: {n_shared_experts}, Routed experts: {self._parsed_config['n_routed_experts']}, \
            Experts per token: {num_experts_per_tok=} {num_moe_layers=} {num_dense_layers=}"
        )
        logger.debug(
            f"{attention_params/1e9=:.3f} {embedding_params/1e9=:.3f} {dense_mlp_params/1e9=:.3f} {moe_active_params/1e9=:.3f}"
        )

        return (
            attention_params + embedding_params + dense_mlp_params + moe_active_params
        )

    def compute_flops_per_token(
        self, sequence_length: int = 2048, batch_size: int = 1
    ) -> Dict[str, float]:
        """
        Compute FLOPs per token for MoE model.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size

        Returns:
            Dictionary with FLOP breakdown per token
        """
        total_flops = self.compute_flops(sequence_length, batch_size)
        total_tokens = batch_size * sequence_length

        flops_per_token = {}
        for component, flops in total_flops.items():
            flops_per_token[component] = flops / total_tokens if total_tokens > 0 else 0

        return flops_per_token

    def compute_active_params_memory_per_token(
        self,
        weight_dtype: str = "fp16",
        expert_parameter_dtype: str = None,
        attention_parameter_dtype: str = None,
    ) -> int:
        """
        Compute the memory footprint of parameters activated per token with mixed precision support.

        This accounts for different precisions used for different parameter types in MoE models.

        Args:
            weight_dtype: Data type for general model weights (embeddings, layer norms, etc.)
            expert_parameter_dtype: Data type for expert parameters (shared and routed experts)
            attention_parameter_dtype: Data type for attention parameters

        Returns:
            Memory footprint of active parameters per token in bytes
        """
        from ..core.operators import validate_mixed_precision_config
        from ..metrics.memory_calculator import MemoryCalculator

        # Validate mixed precision configuration
        validate_mixed_precision_config(
            weight_dtype=weight_dtype,
            expert_parameter_dtype=expert_parameter_dtype,
            attention_parameter_dtype=attention_parameter_dtype,
        )

        # Use provided dtypes or fall back to weight_dtype
        effective_expert_parameter_dtype = expert_parameter_dtype or weight_dtype
        effective_attention_parameter_dtype = attention_parameter_dtype or weight_dtype

        # Get bytes per element for each precision type
        weight_bytes = MemoryCalculator.PRECISION_BYTES.get(weight_dtype, 2)
        expert_bytes = MemoryCalculator.PRECISION_BYTES.get(
            effective_expert_parameter_dtype, 2
        )
        attention_bytes = MemoryCalculator.PRECISION_BYTES.get(
            effective_attention_parameter_dtype, 2
        )

        total_memory = 0

        # All attention and embedding parameters are always active
        attention_params = self.compute_attention_params()
        embedding_params = self.compute_embedding_params()

        total_memory += attention_params * attention_bytes
        total_memory += embedding_params * weight_bytes

        # Dense MLP layers are always fully active
        hidden_size = self._parsed_config["hidden_size"]
        intermediate_size = self._parsed_config["intermediate_size"]
        num_dense_layers = self._parsed_config["num_dense_layers"]

        if num_dense_layers > 0:
            dense_params_per_layer = self._compute_dense_mlp_params(
                hidden_size, intermediate_size
            )
            dense_mlp_params = dense_params_per_layer * num_dense_layers
            total_memory += dense_mlp_params * weight_bytes

        # MoE layers: only shared experts + selected routed experts are active
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        n_shared_experts = self._parsed_config["n_shared_experts"]
        num_experts_per_tok = self._parsed_config["num_experts_per_tok"]
        num_moe_layers = self._parsed_config["num_moe_layers"]

        if num_moe_layers > 0:
            # Shared experts (always active) - use expert parameter dtype
            if n_shared_experts > 0:
                shared_params_per_expert = self._compute_dense_mlp_params(
                    hidden_size, moe_intermediate_size
                )
                shared_expert_params = (
                    shared_params_per_expert * n_shared_experts * num_moe_layers
                )
                total_memory += shared_expert_params * expert_bytes

            # Routed experts (only num_experts_per_tok are active) - use expert parameter dtype
            if num_experts_per_tok > 0:
                routed_params_per_expert = self._compute_dense_mlp_params(
                    hidden_size, moe_intermediate_size
                )
                routed_expert_params = (
                    routed_params_per_expert * num_experts_per_tok * num_moe_layers
                )
                total_memory += routed_expert_params * expert_bytes

            # Router parameters (always active) - use general weight dtype
            router_params = (
                hidden_size * self._parsed_config["n_routed_experts"] * num_moe_layers
            )
            total_memory += router_params * weight_bytes

        # Layer norm parameters (always active) - use general weight dtype
        num_layers = self._parsed_config["num_hidden_layers"]
        layernorm_params = hidden_size * (2 * num_layers + 1)  # 2 per layer + final
        total_memory += layernorm_params * weight_bytes

        return total_memory

    def get_parameter_breakdown_debug(self) -> Dict[str, Any]:
        """
        Get detailed parameter breakdown for debugging purposes.

        Returns:
            Dictionary with detailed parameter breakdown
        """
        hidden_size = self._parsed_config["hidden_size"]
        num_layers = self._parsed_config["num_hidden_layers"]
        vocab_size = self._parsed_config["vocab_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        n_shared_experts = self._parsed_config["n_shared_experts"]
        n_routed_experts = self._parsed_config["n_routed_experts"]
        num_experts_per_tok = self._parsed_config["num_experts_per_tok"]

        # Attention parameters
        attention_params = self.compute_attention_params()
        attention_per_layer = attention_params // num_layers

        # MoE parameters
        mlp_params = self.compute_mlp_params()
        mlp_per_layer = mlp_params // num_layers

        # Expert parameters breakdown
        params_per_expert = self._compute_dense_mlp_params(
            hidden_size, moe_intermediate_size
        )
        total_expert_params = (n_shared_experts + n_routed_experts) * params_per_expert
        router_params = hidden_size * n_routed_experts

        # Active parameters
        active_params = self.compute_active_params_per_token()
        active_expert_params = (
            n_shared_experts + num_experts_per_tok
        ) * params_per_expert

        # Embedding parameters
        embedding_params = self.compute_embedding_params()

        return {
            "model_config": {
                "hidden_size": hidden_size,
                "num_layers": num_layers,
                "vocab_size": vocab_size,
                "moe_intermediate_size": moe_intermediate_size,
                "n_shared_experts": n_shared_experts,
                "n_routed_experts": n_routed_experts,
                "num_experts_per_tok": num_experts_per_tok,
                "total_experts": n_shared_experts + n_routed_experts,
            },
            "parameter_breakdown": {
                "attention_total": attention_params,
                "attention_per_layer": attention_per_layer,
                "mlp_total": mlp_params,
                "mlp_per_layer": mlp_per_layer,
                "embedding_total": embedding_params,
                "total_parameters": self.get_total_params(),
            },
            "expert_breakdown": {
                "params_per_expert": params_per_expert,
                "total_expert_params_per_layer": total_expert_params,
                "router_params_per_layer": router_params,
                "active_expert_params_per_token": active_expert_params,
                "expert_utilization_rate": (
                    num_experts_per_tok / n_routed_experts
                    if n_routed_experts > 0
                    else 0
                ),
            },
            "efficiency_metrics": {
                "total_parameters": self.get_total_params(),
                "active_parameters_per_token": active_params,
                "parameter_efficiency": (
                    (active_params / self.get_total_params()) * 100
                    if self.get_total_params() > 0
                    else 0
                ),
            },
        }

    def compute_flops(
        self,
        sequence_length: int = 2048,
        batch_size: int = 1,
        kv_lens: Optional[int] = None,
    ) -> Dict[str, int]:
        """
        Compute FLOPs for forward pass using operators with MoE-specific routing considerations.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            kv_lens: KV cache length for decode analysis (defaults to sequence_length if not specified)

        Returns:
            Dictionary with FLOP breakdown by component
        """
        hidden_size = self._parsed_config["hidden_size"]
        num_layers = self._parsed_config["num_hidden_layers"]
        num_heads = self._parsed_config["num_attention_heads"]
        num_kv_heads = self._parsed_config["num_key_value_heads"]
        intermediate_size = self._parsed_config["intermediate_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        vocab_size = self._parsed_config["vocab_size"]

        # MoE-specific parameters
        n_shared_experts = self._parsed_config["n_shared_experts"]
        n_routed_experts = self._parsed_config["n_routed_experts"]
        num_experts_per_tok = self._parsed_config["num_experts_per_tok"]
        num_dense_layers = self._parsed_config["num_dense_layers"]
        num_moe_layers = self._parsed_config["num_moe_layers"]

        flops = {}

        # Embedding lookup (no FLOPs, just memory access)
        flops["embeddings"] = 0

        # Attention FLOPs using operators (same for all layers)
        # Use kv_lens if provided, otherwise default to sequence_length
        effective_kv_lens = kv_lens if kv_lens is not None else sequence_length
        attention_flops_per_layer = self._compute_attention_flops_with_operators(
            batch_size, sequence_length, effective_kv_lens
        )
        flops["attention"] = attention_flops_per_layer * num_layers

        # Dense MLP FLOPs using FFNLayer
        if num_dense_layers > 0:
            dense_mlp_op = FFNLayer(
                hidden_size=hidden_size,
                intermediate_size=intermediate_size,
                precision="fp16",
            )
            # Set shape and compute FLOPs
            dense_mlp_op.set_shape(batch_size, sequence_length)
            dense_mlp_flops_per_layer = dense_mlp_op.compute_flops()
            flops["dense_mlp"] = dense_mlp_flops_per_layer * num_dense_layers
        else:
            flops["dense_mlp"] = 0

        # MoE FLOPs using MoELayer
        if num_moe_layers > 0:
            moe_op = MoELayer(
                hidden_size=hidden_size,
                intermediate_size=moe_intermediate_size,
                # num_experts=n_shared_experts + n_routed_experts,
                num_shared_experts=n_shared_experts,
                num_routed_experts=n_routed_experts,
                experts_per_token=num_experts_per_tok,
                precision="fp16",
            )
            # Set shape and compute FLOPs
            moe_op.set_shape(batch_size, sequence_length)
            moe_flops_per_layer = moe_op.compute_flops()
            flops["moe"] = moe_flops_per_layer * num_moe_layers
        else:
            flops["moe"] = 0

        # Layer norm FLOPs using LayerNormOperator
        layernorm_op = LayerNormOperator(hidden_size=hidden_size, precision="fp16")
        # Set shape and compute FLOPs
        layernorm_op.set_shape(batch_size, sequence_length)
        layernorm_flops_per_layer = layernorm_op.compute_flops()
        # 2 layer norms per transformer layer (pre-attention and pre-MLP)
        flops["layernorm"] = layernorm_flops_per_layer * 2 * num_layers

        # Final layer norm
        flops["final_layernorm"] = layernorm_flops_per_layer

        # Language modeling head using MatMulOperator
        if not self._parsed_config["tie_word_embeddings"]:
            lm_head_op = MatMulOperator(
                M=1,  # Will be updated by set_shape
                N=vocab_size,
                K=hidden_size,
                precision="fp16",
            )
            # Set shape and compute FLOPs
            lm_head_op.set_shape(batch_size, sequence_length)
            flops["lm_head"] = lm_head_op.compute_flops()
        else:
            flops["lm_head"] = 0

        return flops

    def _compute_attention_flops_with_operators(
        self, batch_size: int, sequence_length: int, kv_lens: int
    ) -> int:
        """Compute FLOPs for attention computation using operators."""
        hidden_size = self._parsed_config["hidden_size"]
        num_heads = self._parsed_config["num_attention_heads"]
        num_kv_heads = self._parsed_config["num_key_value_heads"]

        # Check if using Multi-head Latent Attention (MLA)
        kv_lora_rank = self._parsed_config.get("kv_lora_rank")

        if kv_lora_rank is not None:
            # MLA using MLAAttentionOperator
            q_lora_rank = self._parsed_config.get("q_lora_rank")
            qk_rope_head_dim = self._parsed_config.get("qk_rope_head_dim", 64)
            qk_nope_head_dim = self._parsed_config.get("qk_nope_head_dim", 128)
            v_head_dim = self._parsed_config.get("v_head_dim", 128)

            attention_op = MLAAttentionOperator(
                hidden_size=hidden_size,
                num_heads=num_heads,
                kv_lora_rank=kv_lora_rank,
                q_lora_rank=q_lora_rank,
                qk_rope_head_dim=qk_rope_head_dim,
                qk_nope_head_dim=qk_nope_head_dim,
                v_head_dim=v_head_dim,
                precision="fp16",
            )
            # Set shape for MLA attention
            attention_op.set_shape(batch_size, sequence_length, kv_lens=kv_lens)
        else:
            # Standard attention using AttentionOperator
            attention_op = AttentionOperator(
                hidden_size=hidden_size,
                num_heads=num_heads,
                num_kv_heads=num_kv_heads,
                precision="fp16",
            )
            # Set shape for standard attention
            attention_op.set_shape(batch_size, sequence_length, kv_lens=kv_lens)

        return attention_op.compute_flops()

    def _compute_attention_flops(self, batch_size: int, sequence_length: int) -> int:
        """Compute FLOPs for attention computation."""
        hidden_size = self._parsed_config["hidden_size"]
        num_heads = self._parsed_config["num_attention_heads"]
        num_kv_heads = self._parsed_config["num_key_value_heads"]

        B, S = batch_size, sequence_length

        # Check if using Multi-head Latent Attention (MLA)
        kv_lora_rank = self._parsed_config.get("kv_lora_rank")

        if kv_lora_rank is not None:
            # MLA FLOPs computation
            q_lora_rank = self._parsed_config.get("q_lora_rank")
            qk_rope_head_dim = self._parsed_config.get("qk_rope_head_dim", 64)
            v_head_dim = self._parsed_config.get("v_head_dim", 128)
            qk_nope_head_dim = self._parsed_config.get("qk_nope_head_dim", 128)

            flops = 0

            # Q projection
            if q_lora_rank is not None:
                # Low-rank Q: B * S * hidden_size * q_lora_rank + B * S * q_lora_rank * num_heads * (qk_rope_head_dim + qk_nope_head_dim)
                flops += B * S * hidden_size * q_lora_rank
                flops += (
                    B
                    * S
                    * q_lora_rank
                    * num_heads
                    * (qk_rope_head_dim + qk_nope_head_dim)
                )
            else:
                # Standard Q: B * S * hidden_size * num_heads * (qk_rope_head_dim + qk_nope_head_dim)
                flops += (
                    B
                    * S
                    * hidden_size
                    * num_heads
                    * (qk_rope_head_dim + qk_nope_head_dim)
                )

            # KV compression: B * S * hidden_size * kv_lora_rank
            flops += B * S * hidden_size * kv_lora_rank

            # KV decompression: B * S * kv_lora_rank * num_kv_heads * (qk_rope_head_dim + qk_nope_head_dim + v_head_dim)
            flops += (
                B
                * S
                * kv_lora_rank
                * num_kv_heads
                * (qk_rope_head_dim + qk_nope_head_dim + v_head_dim)
            )

            # Attention computation: B * num_heads * S * S * (qk_rope_head_dim + qk_nope_head_dim) (for scores)
            flops += B * num_heads * S * S * (qk_rope_head_dim + qk_nope_head_dim)

            # Attention values: B * num_heads * S * S * v_head_dim
            flops += B * num_heads * S * S * v_head_dim

            # Output projection: B * S * num_heads * v_head_dim * hidden_size
            flops += B * S * num_heads * v_head_dim * hidden_size

            return flops

        else:
            # Standard multi-head attention
            head_dim = self._parsed_config["head_dim"]

            # Q, K, V projections
            qkv_proj_flops = (
                B
                * S
                * hidden_size
                * (num_heads * head_dim + 2 * num_kv_heads * head_dim)
            )

            # Attention computation
            attention_computation_flops = 2 * B * num_heads * S * S * head_dim

            # Output projection
            output_proj_flops = B * S * (num_heads * head_dim) * hidden_size

            return qkv_proj_flops + attention_computation_flops + output_proj_flops

    def _compute_dense_mlp_flops(
        self,
        batch_size: int,
        sequence_length: int,
        hidden_size: int,
        intermediate_size: int,
    ) -> int:
        """Compute FLOPs for dense MLP layer."""
        B, S = batch_size, sequence_length

        # Gate and up projections: 2 * B * S * hidden_size * intermediate_size
        gate_up_flops = 2 * B * S * hidden_size * intermediate_size

        # Down projection: B * S * intermediate_size * hidden_size
        down_flops = B * S * intermediate_size * hidden_size

        return gate_up_flops + down_flops

    def _compute_moe_flops(
        self,
        batch_size: int,
        sequence_length: int,
        hidden_size: int,
        moe_intermediate_size: int,
        n_shared_experts: int,
        n_routed_experts: int,
        num_experts_per_tok: int,
    ) -> int:
        """Compute FLOPs for MoE layer accounting for expert routing."""
        B, S = batch_size, sequence_length

        total_flops = 0

        # Router computation: B * S * hidden_size * n_routed_experts
        router_flops = B * S * hidden_size * n_routed_experts
        total_flops += router_flops

        # Shared experts (always computed)
        if n_shared_experts > 0:
            shared_expert_flops = self._compute_dense_mlp_flops(
                B, S, hidden_size, moe_intermediate_size
            )
            total_flops += shared_expert_flops * n_shared_experts

        # Routed experts (only num_experts_per_tok are computed per token)
        if num_experts_per_tok > 0:
            routed_expert_flops = self._compute_dense_mlp_flops(
                B, S, hidden_size, moe_intermediate_size
            )
            # Only the selected experts are computed
            total_flops += routed_expert_flops * num_experts_per_tok

        # Expert combination/mixing (weighted sum): B * S * hidden_size * (n_shared_experts + num_experts_per_tok)
        combination_flops = (
            B * S * hidden_size * (n_shared_experts + num_experts_per_tok)
        )
        total_flops += combination_flops

        return total_flops

    def _compute_mixed_precision_parameter_memory(
        self,
        weight_dtype: str,
        expert_parameter_dtype: str,
        attention_parameter_dtype: str,
    ) -> int:
        """
        Compute parameter memory with mixed precision support for MoE models.

        Args:
            weight_dtype: Data type for general model weights (embeddings, layer norms, etc.)
            expert_parameter_dtype: Data type for expert parameters (shared and routed experts)
            attention_parameter_dtype: Data type for attention parameters

        Returns:
            Total parameter memory in bytes
        """
        from ..core.operators import validate_mixed_precision_config
        from ..metrics.memory_calculator import MemoryCalculator

        # Validate mixed precision configuration
        validate_mixed_precision_config(
            weight_dtype=weight_dtype,
            expert_parameter_dtype=expert_parameter_dtype,
            attention_parameter_dtype=attention_parameter_dtype,
        )

        # Get bytes per element for each precision type
        weight_bytes = MemoryCalculator.PRECISION_BYTES.get(weight_dtype, 2)
        expert_bytes = MemoryCalculator.PRECISION_BYTES.get(expert_parameter_dtype, 2)
        attention_bytes = MemoryCalculator.PRECISION_BYTES.get(
            attention_parameter_dtype, 2
        )

        total_memory = 0

        # Embedding parameters (use general weight dtype)
        embedding_params = self.compute_embedding_params()
        total_memory += embedding_params * weight_bytes

        # Attention parameters (use attention parameter dtype)
        attention_params = self.compute_attention_params()
        total_memory += attention_params * attention_bytes

        # MLP parameters - separate expert parameters from dense MLP parameters
        hidden_size = self._parsed_config["hidden_size"]
        intermediate_size = self._parsed_config["intermediate_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        n_shared_experts = self._parsed_config["n_shared_experts"]
        n_routed_experts = self._parsed_config["n_routed_experts"]
        num_dense_layers = self._parsed_config["num_dense_layers"]
        num_moe_layers = self._parsed_config["num_moe_layers"]

        # Dense MLP layers (use general weight dtype)
        if num_dense_layers > 0:
            dense_params_per_layer = self._compute_dense_mlp_params(
                hidden_size, intermediate_size
            )
            dense_mlp_params = dense_params_per_layer * num_dense_layers
            total_memory += dense_mlp_params * weight_bytes

        # MoE layers - expert parameters use expert_parameter_dtype
        if num_moe_layers > 0:
            # Shared expert parameters
            if n_shared_experts > 0:
                shared_expert_params_per_layer = (
                    self._compute_dense_mlp_params(hidden_size, moe_intermediate_size)
                    * n_shared_experts
                )
                shared_expert_params = shared_expert_params_per_layer * num_moe_layers
                total_memory += shared_expert_params * expert_bytes

            # Routed expert parameters
            if n_routed_experts > 0:
                routed_expert_params_per_layer = (
                    self._compute_dense_mlp_params(hidden_size, moe_intermediate_size)
                    * n_routed_experts
                )
                routed_expert_params = routed_expert_params_per_layer * num_moe_layers
                total_memory += routed_expert_params * expert_bytes

            # Router parameters (use general weight dtype)
            router_params_per_layer = hidden_size * n_routed_experts
            router_params = router_params_per_layer * num_moe_layers
            total_memory += router_params * weight_bytes

        # Layer norm parameters (use general weight dtype)
        # 2 layer norms per layer (pre-attention and pre-MLP) + final layer norm
        num_layers = self._parsed_config["num_hidden_layers"]
        layernorm_params = hidden_size * (2 * num_layers + 1)
        total_memory += layernorm_params * weight_bytes

        return total_memory

    def compute_memory_requirements(
        self,
        sequence_length: int = 2048,
        batch_size: int = 1,
        dtype: str = "fp16",
        weight_dtype: str = None,
        activation_dtype: str = None,
        grad_dtype: str = None,
        optimizer_dtype: str = None,
        kv_cache_dtype: str = None,
        expert_parameter_dtype: str = None,
        attention_parameter_dtype: str = None,
        training: bool = False,
        include_kv_cache: bool = False,
    ) -> Dict[str, int]:
        """
        Compute memory requirements for the MoE model with mixed precision support.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            dtype: Default data type for calculations (backward compatibility)
            weight_dtype: Data type for model weights (overrides dtype if provided)
            activation_dtype: Data type for activations (overrides dtype if provided)
            grad_dtype: Data type for gradients (overrides dtype if provided)
            optimizer_dtype: Data type for optimizer states (overrides dtype if provided)
            kv_cache_dtype: Data type for KV cache (overrides dtype if provided)
            expert_parameter_dtype: Data type for expert parameters (overrides dtype if provided)
            attention_parameter_dtype: Data type for attention parameters (overrides dtype if provided)
            training: Whether this is for training (includes gradients/optimizer)
            include_kv_cache: Whether to include KV cache memory

        Returns:
            Dictionary with memory breakdown by component (in bytes)
        """
        from ..core.operators import validate_mixed_precision_config
        from ..metrics.memory_calculator import MemoryCalculator

        # Ensure config is parsed
        if not hasattr(self, "_parsed_config") or self._parsed_config is None:
            self._parse_config()

        # Validate mixed precision configuration
        validate_mixed_precision_config(
            weight_dtype=weight_dtype,
            activation_dtype=activation_dtype,
            kv_cache_dtype=kv_cache_dtype,
            expert_parameter_dtype=expert_parameter_dtype,
            attention_parameter_dtype=attention_parameter_dtype,
            grad_dtype=grad_dtype,
            optimizer_dtype=optimizer_dtype,
        )

        # Use provided dtypes or fall back to default dtype for backward compatibility
        effective_weight_dtype = weight_dtype or dtype
        effective_activation_dtype = activation_dtype or dtype
        effective_grad_dtype = grad_dtype or dtype
        effective_optimizer_dtype = (
            optimizer_dtype or "fp32"
        )  # Optimizer typically uses fp32
        effective_kv_cache_dtype = kv_cache_dtype or dtype
        effective_expert_parameter_dtype = expert_parameter_dtype or dtype
        effective_attention_parameter_dtype = attention_parameter_dtype or dtype

        # Get configuration parameters
        hidden_size = self._parsed_config["hidden_size"]
        num_layers = self._parsed_config["num_hidden_layers"]
        num_heads = self._parsed_config["num_attention_heads"]
        intermediate_size = self._parsed_config["intermediate_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        vocab_size = self._parsed_config["vocab_size"]

        # Get bytes per element based on dtypes
        weight_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(
            effective_weight_dtype, 2
        )
        activation_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(
            effective_activation_dtype, 2
        )

        B, S = batch_size, sequence_length

        memory = {}

        # Parameter memory with mixed precision support for MoE-specific components
        memory["parameters"] = self._compute_mixed_precision_parameter_memory(
            effective_weight_dtype,
            effective_expert_parameter_dtype,
            effective_attention_parameter_dtype,
        )

        # Activation memory (intermediate tensors during forward pass)
        activation_memory = 0

        # Input embeddings: B * S * hidden_size
        activation_memory += B * S * hidden_size * activation_bytes_per_param

        # Per layer activations
        per_layer_activations = 0

        # Attention activations (similar to dense model but may be different for MLA)
        kv_lora_rank = self._parsed_config.get("kv_lora_rank")
        if kv_lora_rank is not None:
            # MLA activations
            # Compressed KV: B * S * kv_lora_rank
            per_layer_activations += B * S * kv_lora_rank * activation_bytes_per_param

            # Decompressed K, V: B * S * num_kv_heads * (qk_rope_head_dim + qk_nope_head_dim + v_head_dim)
            num_kv_heads = self._parsed_config["num_key_value_heads"]
            qk_rope_head_dim = self._parsed_config.get("qk_rope_head_dim", 64)
            qk_nope_head_dim = self._parsed_config.get("qk_nope_head_dim", 128)
            v_head_dim = self._parsed_config.get("v_head_dim", 128)

            kv_size = num_kv_heads * (qk_rope_head_dim + qk_nope_head_dim + v_head_dim)
            per_layer_activations += B * S * kv_size * activation_bytes_per_param

            # Q tensor: B * S * num_heads * (qk_rope_head_dim + qk_nope_head_dim)
            q_size = num_heads * (qk_rope_head_dim + qk_nope_head_dim)
            per_layer_activations += B * S * q_size * activation_bytes_per_param

            # Attention scores: B * num_heads * S * S
            per_layer_activations += B * num_heads * S * S * activation_bytes_per_param

            # Attention output: B * S * num_heads * v_head_dim
            per_layer_activations += (
                B * S * num_heads * v_head_dim * activation_bytes_per_param
            )
        else:
            # Standard attention activations
            head_dim = self._parsed_config.get("head_dim", hidden_size // num_heads)
            per_layer_activations += (
                3 * B * S * hidden_size * activation_bytes_per_param
            )  # Q, K, V
            per_layer_activations += (
                B * num_heads * S * S * activation_bytes_per_param
            )  # Attention scores
            per_layer_activations += (
                B * S * hidden_size * activation_bytes_per_param
            )  # Attention output

        # MLP activations (different for dense vs MoE layers)
        num_dense_layers = self._parsed_config.get("num_dense_layers", 0)
        num_moe_layers = self._parsed_config.get("num_moe_layers", num_layers)

        # Dense MLP activations
        if num_dense_layers > 0:
            dense_mlp_activations = (
                2 * B * S * intermediate_size * activation_bytes_per_param
            )  # Gate and up
            dense_mlp_activations += (
                B * S * hidden_size * activation_bytes_per_param
            )  # Output
            per_layer_activations += dense_mlp_activations

        # MoE activations (only for active experts)
        if num_moe_layers > 0:
            n_shared_experts = self._parsed_config.get("n_shared_experts", 0)
            num_experts_per_tok = self._parsed_config.get("num_experts_per_tok", 2)

            # Router activations: B * S * n_routed_experts
            moe_activations = (
                B
                * S
                * self._parsed_config.get("n_routed_experts", 8)
                * activation_bytes_per_param
            )

            # Active expert activations
            active_experts = n_shared_experts + num_experts_per_tok
            expert_activations = (
                2 * B * S * moe_intermediate_size * activation_bytes_per_param
            )  # Gate and up per expert
            moe_activations += expert_activations * active_experts

            # Expert output combination: B * S * hidden_size
            moe_activations += B * S * hidden_size * activation_bytes_per_param

            per_layer_activations += moe_activations

        activation_memory += per_layer_activations * num_layers

        # Output logits: B * S * vocab_size
        activation_memory += B * S * vocab_size * activation_bytes_per_param

        memory["activations"] = activation_memory

        # Training-specific memory components
        if training:
            # For gradients and optimizer states, we need to compute memory based on mixed precision
            # but typically gradients and optimizer states use consistent precision across all parameters
            total_params = self.get_total_params()
            grad_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(
                effective_grad_dtype, 2
            )
            optimizer_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(
                effective_optimizer_dtype, 4
            )

            # Gradient memory
            memory["gradients"] = total_params * grad_bytes_per_param

            # Optimizer states (Adam: 2x parameters)
            memory["optimizer_states"] = total_params * 2 * optimizer_bytes_per_param

        # KV cache memory (for inference)
        if include_kv_cache and not training:
            kv_cache_memory = MemoryCalculator.compute_kv_cache_memory(
                self._parsed_config,
                sequence_length,
                batch_size,
                dtype=effective_kv_cache_dtype,
            )
            memory["kv_cache"] = kv_cache_memory.get("total", 0)

        # Check if using mixed precision
        using_mixed_precision = any(
            [
                weight_dtype,
                activation_dtype,
                grad_dtype,
                optimizer_dtype,
                kv_cache_dtype,
                expert_parameter_dtype,
                attention_parameter_dtype,
            ]
        )

        # Add dtype information for consistency
        memory["dtype"] = effective_weight_dtype if using_mixed_precision else dtype
        memory["bytes_per_element"] = MemoryCalculator.PRECISION_BYTES.get(
            effective_weight_dtype, 2
        )

        # Add mixed precision dtype information
        if using_mixed_precision:
            memory["dtypes"] = {
                "weight": effective_weight_dtype,
                "activation": effective_activation_dtype,
                "expert_parameter": effective_expert_parameter_dtype,
                "attention_parameter": effective_attention_parameter_dtype,
                "grad": effective_grad_dtype if training else None,
                "optimizer": effective_optimizer_dtype if training else None,
                "kv_cache": (
                    effective_kv_cache_dtype
                    if include_kv_cache and not training
                    else None
                ),
            }

            # Add bytes per element for each precision type
            memory["bytes_per_precision"] = {
                "weight": MemoryCalculator.PRECISION_BYTES.get(
                    effective_weight_dtype, 2
                ),
                "activation": MemoryCalculator.PRECISION_BYTES.get(
                    effective_activation_dtype, 2
                ),
                "expert_parameter": MemoryCalculator.PRECISION_BYTES.get(
                    effective_expert_parameter_dtype, 2
                ),
                "attention_parameter": MemoryCalculator.PRECISION_BYTES.get(
                    effective_attention_parameter_dtype, 2
                ),
                "kv_cache": MemoryCalculator.PRECISION_BYTES.get(
                    effective_kv_cache_dtype, 2
                ),
            }

            if training:
                memory["bytes_per_precision"]["grad"] = (
                    MemoryCalculator.PRECISION_BYTES.get(effective_grad_dtype, 2)
                )
                memory["bytes_per_precision"]["optimizer"] = (
                    MemoryCalculator.PRECISION_BYTES.get(effective_optimizer_dtype, 4)
                )
        else:
            # Backward compatibility - single precision mode
            memory["dtypes"] = {
                "weight": dtype,
                "activation": dtype,
                "expert_parameter": dtype,
                "attention_parameter": dtype,
                "grad": dtype if training else None,
                "optimizer": (
                    "fp32" if training else None
                ),  # Optimizer typically uses fp32
                "kv_cache": dtype if include_kv_cache and not training else None,
            }

        # Calculate total memory
        total_memory = memory["parameters"] + memory["activations"]
        if training:
            total_memory += memory.get("gradients", 0) + memory.get(
                "optimizer_states", 0
            )
        if include_kv_cache and not training:
            total_memory += memory.get("kv_cache", 0)

        memory["total"] = total_memory

        return memory

    def get_matrix_shapes(
        self,
        parallel_config: Optional[ParallelConfig] = None,
        sequence_length: int = 1,
        batch_size: int = 1,
        detailed: bool = False,
    ) -> Dict[str, Any]:
        """
        Get matrix shapes for MoE model operations under parallel configuration.

        Args:
            parallel_config: Parallel execution configuration
            sequence_length: Input sequence length for activation shapes
            batch_size: Batch size for activation shapes
            detailed: Whether to return detailed shape analysis

        Returns:
            Dictionary with matrix shapes for different operations
        """
        if detailed:
            # Use ShapeAnalyzer for comprehensive analysis
            from ..metrics.shape_analyzer import ShapeAnalyzer

            return ShapeAnalyzer.get_detailed_shape_analysis(
                self._parsed_config, sequence_length, batch_size, parallel_config
            )

        hidden_size = self._parsed_config["hidden_size"]
        num_heads = self._parsed_config["num_attention_heads"]
        num_kv_heads = self._parsed_config["num_key_value_heads"]
        intermediate_size = self._parsed_config["intermediate_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        vocab_size = self._parsed_config["vocab_size"]
        n_routed_experts = self._parsed_config["n_routed_experts"]

        shapes = {
            "attention": {},
            "dense_mlp": {},
            "moe": {},
            "embeddings": {
                "token_embeddings": (vocab_size, hidden_size),
            },
        }

        # Attention shapes (depends on whether using MLA)
        kv_lora_rank = self._parsed_config.get("kv_lora_rank")
        if kv_lora_rank is not None:
            # Multi-head Latent Attention shapes
            q_lora_rank = self._parsed_config.get("q_lora_rank")
            qk_rope_head_dim = self._parsed_config.get("qk_rope_head_dim", 64)
            qk_nope_head_dim = self._parsed_config.get("qk_nope_head_dim", 128)
            v_head_dim = self._parsed_config.get("v_head_dim", 128)

            if q_lora_rank is not None:
                shapes["attention"]["q_proj_down"] = (hidden_size, q_lora_rank)
                shapes["attention"]["q_proj_up"] = (
                    q_lora_rank,
                    num_heads * (qk_rope_head_dim + qk_nope_head_dim),
                )
            else:
                shapes["attention"]["q_proj"] = (
                    hidden_size,
                    num_heads * (qk_rope_head_dim + qk_nope_head_dim),
                )

            shapes["attention"]["kv_compress"] = (hidden_size, kv_lora_rank)
            shapes["attention"]["kv_decompress"] = (
                kv_lora_rank,
                num_kv_heads * (qk_rope_head_dim + qk_nope_head_dim + v_head_dim),
            )
            shapes["attention"]["o_proj"] = (num_heads * v_head_dim, hidden_size)
        else:
            # Standard attention shapes
            head_dim = self._parsed_config["head_dim"]
            shapes["attention"]["q_proj"] = (hidden_size, num_heads * head_dim)
            shapes["attention"]["k_proj"] = (hidden_size, num_kv_heads * head_dim)
            shapes["attention"]["v_proj"] = (hidden_size, num_kv_heads * head_dim)
            shapes["attention"]["o_proj"] = (num_heads * head_dim, hidden_size)

        # Dense MLP shapes
        if self._parsed_config["num_dense_layers"] > 0:
            shapes["dense_mlp"] = {
                "gate_proj": (hidden_size, intermediate_size),
                "up_proj": (hidden_size, intermediate_size),
                "down_proj": (intermediate_size, hidden_size),
            }

        # MoE shapes
        if self._parsed_config["num_moe_layers"] > 0:
            shapes["moe"] = {
                "router": (hidden_size, n_routed_experts),
                "expert_gate_proj": (hidden_size, moe_intermediate_size),  # Per expert
                "expert_up_proj": (hidden_size, moe_intermediate_size),  # Per expert
                "expert_down_proj": (moe_intermediate_size, hidden_size),  # Per expert
            }

        # Add output embeddings if not tied
        if not self._parsed_config["tie_word_embeddings"]:
            shapes["embeddings"]["lm_head"] = (hidden_size, vocab_size)

        # Apply parallelism if specified
        if parallel_config:
            if parallel_config.tensor_parallel_size > 1:
                shapes = self._apply_tensor_parallelism(
                    shapes, parallel_config.tensor_parallel_size
                )

            if parallel_config.expert_parallel_size > 1:
                shapes = self._apply_expert_parallelism(shapes, parallel_config)

        return shapes

    def _apply_tensor_parallelism(
        self, shapes: Dict[str, Any], tp_size: int
    ) -> Dict[str, Any]:
        """
        Apply tensor parallelism to MoE matrix shapes.

        Args:
            shapes: Original matrix shapes
            tp_size: Tensor parallel size

        Returns:
            Modified shapes under tensor parallelism
        """
        parallel_shapes = {}

        # Attention shapes under tensor parallelism
        if "attention" in shapes:
            attention_shapes = shapes["attention"].copy()

            # Handle MLA vs standard attention
            if "kv_compress" in attention_shapes:
                # MLA tensor parallelism
                # KV compression is replicated
                # KV decompression is column-parallel
                kv_decomp_shape = attention_shapes["kv_decompress"]
                attention_shapes["kv_decompress"] = (
                    kv_decomp_shape[0],
                    kv_decomp_shape[1] // tp_size,
                )

                # Q projection handling
                if "q_proj_up" in attention_shapes:
                    q_up_shape = attention_shapes["q_proj_up"]
                    attention_shapes["q_proj_up"] = (
                        q_up_shape[0],
                        q_up_shape[1] // tp_size,
                    )
                elif "q_proj" in attention_shapes:
                    q_shape = attention_shapes["q_proj"]
                    attention_shapes["q_proj"] = (q_shape[0], q_shape[1] // tp_size)

                # Output projection is row-parallel
                o_shape = attention_shapes["o_proj"]
                attention_shapes["o_proj"] = (o_shape[0] // tp_size, o_shape[1])
            else:
                # Standard attention tensor parallelism
                for proj in ["q_proj", "k_proj", "v_proj"]:
                    if proj in attention_shapes:
                        shape = attention_shapes[proj]
                        attention_shapes[proj] = (shape[0], shape[1] // tp_size)

                if "o_proj" in attention_shapes:
                    o_shape = attention_shapes["o_proj"]
                    attention_shapes["o_proj"] = (o_shape[0] // tp_size, o_shape[1])

            parallel_shapes["attention"] = attention_shapes

        # Dense MLP shapes under tensor parallelism
        if "dense_mlp" in shapes:
            dense_mlp_shapes = shapes["dense_mlp"].copy()

            # Gate and up projections are column-parallel
            for proj in ["gate_proj", "up_proj"]:
                if proj in dense_mlp_shapes:
                    shape = dense_mlp_shapes[proj]
                    dense_mlp_shapes[proj] = (shape[0], shape[1] // tp_size)

            # Down projection is row-parallel
            if "down_proj" in dense_mlp_shapes:
                down_shape = dense_mlp_shapes["down_proj"]
                dense_mlp_shapes["down_proj"] = (
                    down_shape[0] // tp_size,
                    down_shape[1],
                )

            parallel_shapes["dense_mlp"] = dense_mlp_shapes

        # MoE shapes under tensor parallelism
        if "moe" in shapes:
            moe_shapes = shapes["moe"].copy()

            # Router can be replicated or column-parallel depending on strategy
            # For simplicity, we'll keep it replicated here

            # Expert projections are column/row parallel like dense MLP
            for proj in ["expert_gate_proj", "expert_up_proj"]:
                if proj in moe_shapes:
                    shape = moe_shapes[proj]
                    moe_shapes[proj] = (shape[0], shape[1] // tp_size)

            if "expert_down_proj" in moe_shapes:
                down_shape = moe_shapes["expert_down_proj"]
                moe_shapes["expert_down_proj"] = (
                    down_shape[0] // tp_size,
                    down_shape[1],
                )

            parallel_shapes["moe"] = moe_shapes

        # Embeddings remain the same (replicated across devices)
        if "embeddings" in shapes:
            parallel_shapes["embeddings"] = shapes["embeddings"].copy()

        return parallel_shapes

    def get_metrics(
        self,
        sequence_length: int = 2048,
        batch_size: int = 1,
        parallel_config: Optional[ParallelConfig] = None,
        kv_lens: Optional[int] = None,
    ) -> ModelMetrics:
        """
        Get comprehensive MoE model metrics including efficiency metrics.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            parallel_config: Parallel execution configuration
            kv_lens: KV cache length for decode analysis (defaults to sequence_length if not specified)

        Returns:
            ModelMetrics object with all computed metrics including MoE-specific ones
        """
        # Get base metrics
        metrics = super().get_metrics(
            sequence_length, batch_size, parallel_config, kv_lens
        )

        # Add MoE-specific metrics
        metrics.experts_per_token = self._parsed_config["num_experts_per_tok"]
        metrics.num_shared_experts = self._parsed_config["n_shared_experts"]
        metrics.num_routed_experts = self._parsed_config["n_routed_experts"]
        metrics.active_params_per_token = self.compute_active_params_per_token()

        return metrics

    def validate_parallel_config(self, parallel_config: ParallelConfig) -> bool:
        """
        Validate that a parallel configuration is feasible for this MoE model.

        Args:
            parallel_config: Parallel configuration to validate

        Returns:
            True if configuration is valid, False otherwise
        """
        if not super().validate_parallel_config(parallel_config):
            return False

        tp_size = parallel_config.tensor_parallel_size

        # Check MoE-specific constraints
        n_routed_experts = self._parsed_config["n_routed_experts"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]

        # MoE intermediate size must be divisible by tensor parallel size
        if moe_intermediate_size % tp_size != 0:
            return False

        # For expert parallelism, number of experts should be divisible by some parallel degree
        # This is a more complex constraint that depends on the specific parallelization strategy

        return True

    def get_expert_metrics(self) -> Dict[str, Any]:
        """
        Get MoE-specific metrics including expert utilization.

        Returns:
            Dictionary with expert metrics and utilization analysis
        """
        total_params = self.get_total_params()
        active_params = self.compute_active_params_per_token()

        n_shared_experts = self._parsed_config["n_shared_experts"]
        n_routed_experts = self._parsed_config["n_routed_experts"]
        num_experts_per_tok = self._parsed_config["num_experts_per_tok"]

        return {
            "total_parameters": total_params,
            "active_parameters_per_token": active_params,
            "parameter_efficiency": (
                active_params / total_params if total_params > 0 else 0
            ),
            # "num_experts": n_shared_experts + n_routed_experts,
            "total_experts": n_shared_experts + n_routed_experts,
            "shared_experts": n_shared_experts,
            "routed_experts": n_routed_experts,
            "experts_per_token": num_experts_per_tok,
            "expert_utilization_rate": (
                num_experts_per_tok / n_routed_experts if n_routed_experts > 0 else 0
            ),
            "routing_overhead": self._compute_routing_overhead(),
        }

    def get_expert_utilization_analysis(self) -> Dict[str, Any]:
        """
        Get analysis of expert utilization and efficiency.

        Returns:
            Dictionary with expert utilization metrics
        """
        # Delegate to get_expert_metrics for consistency
        return self.get_expert_metrics()

    def _compute_routing_overhead(self) -> float:
        """Compute the computational overhead of expert routing."""
        hidden_size = self._parsed_config["hidden_size"]
        n_routed_experts = self._parsed_config["n_routed_experts"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]

        # Router computation cost
        router_params = hidden_size * n_routed_experts

        # Expert computation cost (per expert)
        expert_params = 3 * hidden_size * moe_intermediate_size  # gate, up, down

        # Routing overhead as percentage of expert computation
        return router_params / expert_params if expert_params > 0 else 0

    def validate_moe_parallel_config(self, parallel_config: ParallelConfig) -> bool:
        """
        Validate MoE-specific parallel configuration constraints.

        Args:
            parallel_config: Parallel configuration to validate

        Returns:
            True if MoE parallel configuration is valid
        """
        tp_size = parallel_config.tensor_parallel_size
        ep_size = parallel_config.expert_parallel_size
        edp_size = parallel_config.expert_data_parallel_size

        n_routed_experts = self._parsed_config["n_routed_experts"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]

        # Expert parallelism constraints
        if ep_size > 1:
            # Number of routed experts must be divisible by expert parallel size
            if n_routed_experts % ep_size != 0:
                return False

            # Each expert parallel group should have at least one expert
            experts_per_group = n_routed_experts // ep_size
            if experts_per_group < 1:
                return False

        # Expert data parallelism constraints
        if edp_size > 1:
            # Expert data parallelism is orthogonal to expert parallelism
            # but we need to ensure reasonable load balancing
            pass

        # Tensor parallelism constraints for MoE
        if tp_size > 1:
            # MoE intermediate size must be divisible by tensor parallel size
            if moe_intermediate_size % tp_size != 0:
                return False

        # Combined constraints
        total_parallel_size = tp_size * ep_size * edp_size
        if total_parallel_size > n_routed_experts:
            # Too much parallelism for the number of experts
            return False

        return True

    def get_moe_parallel_validation_errors(
        self, parallel_config: ParallelConfig
    ) -> List[str]:
        """
        Get detailed MoE parallel configuration validation errors.

        Args:
            parallel_config: Parallel configuration to validate

        Returns:
            List of validation error messages
        """
        errors = []

        tp_size = parallel_config.tensor_parallel_size
        ep_size = parallel_config.expert_parallel_size
        edp_size = parallel_config.expert_data_parallel_size

        n_routed_experts = self._parsed_config["n_routed_experts"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]

        # Expert parallelism validation
        if ep_size > 1:
            if n_routed_experts % ep_size != 0:
                errors.append(
                    f"Number of routed experts ({n_routed_experts}) must be divisible by "
                    f"expert parallel size ({ep_size})"
                )

            experts_per_group = n_routed_experts // ep_size
            if experts_per_group < 1:
                errors.append(
                    f"Expert parallel size ({ep_size}) is too large for "
                    f"{n_routed_experts} experts"
                )

        # Tensor parallelism validation for MoE
        if tp_size > 1:
            if moe_intermediate_size % tp_size != 0:
                errors.append(
                    f"MoE intermediate size ({moe_intermediate_size}) must be divisible by "
                    f"tensor parallel size ({tp_size})"
                )

        # Combined parallelism validation
        total_parallel_size = tp_size * ep_size * edp_size
        if total_parallel_size > n_routed_experts:
            errors.append(
                f"Total parallelism ({total_parallel_size}) exceeds number of experts "
                f"({n_routed_experts})"
            )

        return errors

    def compute_moe_expert_placement(
        self, parallel_config: ParallelConfig
    ) -> Dict[str, Any]:
        """
        Compute expert placement strategy for parallel execution.

        Args:
            parallel_config: Parallel configuration

        Returns:
            Dictionary with expert placement information
        """
        ep_size = parallel_config.expert_parallel_size
        edp_size = parallel_config.expert_data_parallel_size

        n_shared_experts = self._parsed_config["n_shared_experts"]
        n_routed_experts = self._parsed_config["n_routed_experts"]

        placement = {
            "expert_parallel_size": ep_size,
            "expert_data_parallel_size": edp_size,
            "shared_experts": n_shared_experts,
            "routed_experts": n_routed_experts,
        }

        if ep_size > 1:
            # Distribute routed experts across expert parallel groups
            experts_per_group = n_routed_experts // ep_size
            remaining_experts = n_routed_experts % ep_size

            expert_groups = []
            start_idx = 0

            for group_idx in range(ep_size):
                # Some groups get one extra expert if not evenly divisible
                group_size = experts_per_group + (
                    1 if group_idx < remaining_experts else 0
                )
                end_idx = start_idx + group_size

                expert_groups.append(
                    {
                        "group_id": group_idx,
                        "expert_range": (start_idx, end_idx),
                        "num_experts": group_size,
                    }
                )

                start_idx = end_idx

            placement["expert_groups"] = expert_groups
            placement["experts_per_group"] = experts_per_group
        else:
            # All experts on single device
            placement["expert_groups"] = [
                {
                    "group_id": 0,
                    "expert_range": (0, n_routed_experts),
                    "num_experts": n_routed_experts,
                }
            ]

        # Shared experts are replicated across all expert parallel groups
        placement["shared_expert_replication"] = ep_size

        return placement

    def compute_moe_communication_volume(
        self,
        parallel_config: ParallelConfig,
        sequence_length: int = 2048,
        batch_size: int = 1,
    ) -> Dict[str, Any]:
        """
        Compute communication volume for MoE parallel execution.

        Args:
            parallel_config: Parallel configuration
            sequence_length: Input sequence length
            batch_size: Batch size

        Returns:
            Dictionary with communication volume analysis
        """
        tp_size = parallel_config.tensor_parallel_size
        ep_size = parallel_config.expert_parallel_size
        edp_size = parallel_config.expert_data_parallel_size

        hidden_size = self._parsed_config["hidden_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        n_routed_experts = self._parsed_config["n_routed_experts"]
        num_experts_per_tok = self._parsed_config["num_experts_per_tok"]
        num_moe_layers = self._parsed_config["num_moe_layers"]

        B, S = batch_size, sequence_length

        # Assume FP16 (2 bytes per element)
        bytes_per_element = 2

        communication = {
            "tensor_parallel": {},
            "expert_parallel": {},
            "expert_data_parallel": {},
            "total_per_layer": {},
            "total_all_layers": {},
        }

        # Tensor parallel communication (AllReduce for MoE layers)
        if tp_size > 1:
            # Router output needs AllReduce: B * S * n_routed_experts
            router_comm = B * S * n_routed_experts * bytes_per_element

            # Expert outputs need AllReduce: B * S * hidden_size per active expert
            expert_output_comm = (
                B * S * hidden_size * num_experts_per_tok * bytes_per_element
            )

            communication["tensor_parallel"] = {
                "router_allreduce": router_comm,
                "expert_output_allreduce": expert_output_comm,
                "total": router_comm + expert_output_comm,
            }

        # Expert parallel communication (AllToAll for token routing)
        if ep_size > 1:
            # Token routing: redistribute tokens to appropriate expert groups
            # Each token needs to be sent to the device containing its selected experts

            # Worst case: all tokens need to be redistributed
            token_routing_comm = B * S * hidden_size * bytes_per_element

            # Expert output gathering: collect results from expert groups
            expert_gather_comm = (
                B * S * hidden_size * num_experts_per_tok * bytes_per_element
            )

            communication["expert_parallel"] = {
                "token_routing_alltoall": token_routing_comm,
                "expert_output_gather": expert_gather_comm,
                "total": token_routing_comm + expert_gather_comm,
            }

        # Expert data parallel communication (AllReduce for gradients)
        if edp_size > 1:
            # Gradient synchronization for expert parameters
            expert_params_per_group = self._compute_expert_params_per_group(
                parallel_config
            )
            gradient_sync_comm = expert_params_per_group * bytes_per_element

            communication["expert_data_parallel"] = {
                "gradient_allreduce": gradient_sync_comm,
                "total": gradient_sync_comm,
            }

        # Compute per-layer totals
        tp_comm = communication["tensor_parallel"].get("total", 0)
        ep_comm = communication["expert_parallel"].get("total", 0)
        edp_comm = communication["expert_data_parallel"].get("total", 0)

        communication["total_per_layer"] = {
            "tensor_parallel": tp_comm,
            "expert_parallel": ep_comm,
            "expert_data_parallel": edp_comm,
            "total": tp_comm + ep_comm + edp_comm,
        }

        # Scale by number of MoE layers
        communication["total_all_layers"] = {
            "tensor_parallel": tp_comm * num_moe_layers,
            "expert_parallel": ep_comm * num_moe_layers,
            "expert_data_parallel": edp_comm * num_moe_layers,
            "total": (tp_comm + ep_comm + edp_comm) * num_moe_layers,
        }

        return communication

    def _compute_expert_params_per_group(self, parallel_config: ParallelConfig) -> int:
        """Compute number of expert parameters per expert parallel group."""
        ep_size = parallel_config.expert_parallel_size

        hidden_size = self._parsed_config["hidden_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        n_routed_experts = self._parsed_config["n_routed_experts"]

        # Parameters per expert
        expert_params = self._compute_dense_mlp_params(
            hidden_size, moe_intermediate_size
        )

        # Experts per group
        experts_per_group = n_routed_experts // ep_size

        return expert_params * experts_per_group

    def get_moe_parallel_efficiency_analysis(
        self,
        parallel_config: ParallelConfig,
        sequence_length: int = 2048,
        batch_size: int = 1,
    ) -> Dict[str, Any]:
        """
        Analyze parallel efficiency for MoE execution.

        Args:
            parallel_config: Parallel configuration
            sequence_length: Input sequence length
            batch_size: Batch size

        Returns:
            Dictionary with efficiency analysis
        """
        tp_size = parallel_config.tensor_parallel_size
        ep_size = parallel_config.expert_parallel_size
        edp_size = parallel_config.expert_data_parallel_size

        # Compute communication overhead
        comm_analysis = self.compute_moe_communication_volume(
            parallel_config, sequence_length, batch_size
        )

        # Compute computation load
        flops = self.compute_flops(sequence_length, batch_size)
        moe_flops = flops.get("moe", 0)

        # Expert utilization analysis
        expert_analysis = self.get_expert_utilization_analysis()

        # Load balancing analysis
        n_routed_experts = self._parsed_config["n_routed_experts"]
        num_experts_per_tok = self._parsed_config["num_experts_per_tok"]

        # Theoretical perfect load balancing
        perfect_load_per_expert = num_experts_per_tok / n_routed_experts

        # Actual load depends on routing decisions (assume uniform for analysis)
        actual_load_per_expert = perfect_load_per_expert  # Simplified assumption

        load_imbalance = (
            abs(actual_load_per_expert - perfect_load_per_expert)
            / perfect_load_per_expert
        )

        efficiency = {
            "parallel_configuration": {
                "tensor_parallel_size": tp_size,
                "expert_parallel_size": ep_size,
                "expert_data_parallel_size": edp_size,
                "total_devices": tp_size * ep_size * edp_size,
            },
            "computation_analysis": {
                "moe_flops": moe_flops,
                "flops_per_device": moe_flops // (tp_size * ep_size * edp_size),
                "expert_utilization_rate": expert_analysis["expert_utilization_rate"],
            },
            "communication_analysis": comm_analysis["total_all_layers"],
            "load_balancing": {
                "perfect_load_per_expert": perfect_load_per_expert,
                "actual_load_per_expert": actual_load_per_expert,
                "load_imbalance": load_imbalance,
                "experts_per_device": (
                    n_routed_experts // ep_size if ep_size > 1 else n_routed_experts
                ),
            },
            "efficiency_metrics": {
                "communication_to_computation_ratio": (
                    comm_analysis["total_all_layers"]["total"] / moe_flops
                    if moe_flops > 0
                    else float("inf")
                ),
                "parallel_efficiency": 1.0
                / (1.0 + load_imbalance),  # Simplified metric
                "memory_efficiency": expert_analysis["parameter_efficiency"],
            },
        }

        return efficiency

    def validate_parallel_config(self, parallel_config: ParallelConfig) -> bool:
        """
        Validate that a parallel configuration is feasible for this MoE model.

        Args:
            parallel_config: Parallel configuration to validate

        Returns:
            True if configuration is valid, False otherwise
        """
        # First check base model validation
        if not super().validate_parallel_config(parallel_config):
            return False

        # Then check MoE-specific validation
        return self.validate_moe_parallel_config(parallel_config)

    def _apply_expert_parallelism(
        self, shapes: Dict[str, Any], parallel_config: ParallelConfig
    ) -> Dict[str, Any]:
        """
        Apply expert parallelism to MoE shapes.

        Args:
            shapes: Original matrix shapes
            parallel_config: Parallel configuration

        Returns:
            Modified shapes under expert parallelism
        """
        ep_size = parallel_config.expert_parallel_size

        if ep_size <= 1 or "moe" not in shapes:
            return shapes

        moe_shapes = shapes["moe"].copy()
        n_routed_experts = self._parsed_config["n_routed_experts"]

        # Router shape remains the same (replicated)
        # Expert shapes are distributed across devices
        experts_per_device = n_routed_experts // ep_size

        # Add expert distribution information
        moe_shapes["experts_per_device"] = experts_per_device
        moe_shapes["expert_parallel_size"] = ep_size

        # The actual expert weight shapes remain the same per expert,
        # but each device only holds a subset of experts

        shapes["moe"] = moe_shapes
        return shapes

    def get_attention_mechanism_type(self) -> str:
        """
        Return the attention mechanism type for this MoE model.

        Implements attention mechanism detection logic specific to MoE models,
        with special handling for MLA (Multi-head Latent Attention) used in DeepSeek models.

        Returns:
            String indicating attention mechanism type ('MHA', 'GQA', 'MLA', 'Unknown')
        """
        from ..metrics.memory_calculator import MemoryCalculator

        # Use the parsed config for more reliable detection
        attention_info = MemoryCalculator.get_attention_mechanism_info(
            self._parsed_config
        )

        return attention_info["type"]

    def get_memory_breakdown_by_dtype(
        self,
        sequence_length: int = 2048,
        batch_size: int = 1,
        dtype: str = "fp16",
        include_kv_cache: bool = True,
    ) -> Dict[str, Any]:
        """
        Get detailed memory breakdown with dtype-specific calculations for MoE models.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            dtype: Data type for calculations ('fp16', 'bf16', 'fp32', 'int8')
            include_kv_cache: Whether to include KV cache memory

        Returns:
            Dictionary with detailed memory breakdown including MoE model specific info
        """
        from ..core.operators import validate_precision_type
        from ..metrics.memory_calculator import MemoryCalculator

        # Validate dtype parameter
        validate_precision_type(dtype)

        # Get base memory breakdown
        result = super().get_memory_breakdown_by_dtype(
            sequence_length, batch_size, dtype, include_kv_cache
        )

        # Add MoE model specific information
        result["model_type"] = "moe"
        result["architecture_details"] = {
            "hidden_size": self._parsed_config["hidden_size"],
            "num_layers": self._parsed_config["num_hidden_layers"],
            "num_attention_heads": self._parsed_config["num_attention_heads"],
            "num_key_value_heads": self._parsed_config["num_key_value_heads"],
            "intermediate_size": self._parsed_config["intermediate_size"],
            "vocab_size": self._parsed_config["vocab_size"],
            # MoE specific details
            "n_shared_experts": self._parsed_config["n_shared_experts"],
            "n_routed_experts": self._parsed_config["n_routed_experts"],
            "num_experts_per_tok": self._parsed_config["num_experts_per_tok"],
            "moe_intermediate_size": self._parsed_config["moe_intermediate_size"],
            "num_dense_layers": self._parsed_config["num_dense_layers"],
            "num_moe_layers": self._parsed_config["num_moe_layers"],
            # MLA specific details (if present)
            "kv_lora_rank": self._parsed_config.get("kv_lora_rank"),
            "qk_rope_head_dim": self._parsed_config.get("qk_rope_head_dim"),
            "qk_nope_head_dim": self._parsed_config.get("qk_nope_head_dim"),
            "v_head_dim": self._parsed_config.get("v_head_dim"),
        }

        # Add MoE efficiency metrics
        result["moe_efficiency"] = {
            "total_experts": self._parsed_config["n_routed_experts"]
            + self._parsed_config["n_shared_experts"],
            "active_experts_per_token": self._parsed_config["num_experts_per_tok"]
            + self._parsed_config["n_shared_experts"],
            "expert_utilization_ratio": (
                self._parsed_config["num_experts_per_tok"]
                + self._parsed_config["n_shared_experts"]
            )
            / max(
                1,
                self._parsed_config["n_routed_experts"]
                + self._parsed_config["n_shared_experts"],
            ),
            "parameter_efficiency": self.compute_active_params_per_token()
            / max(1, self.get_total_params()),
        }

        # Add KV cache specific analysis if requested
        if include_kv_cache:
            kv_cache_analysis = MemoryCalculator.compute_kv_cache_memory_by_dtype(
                self._parsed_config, sequence_length, batch_size, dtype
            )
            result["kv_cache_analysis"] = kv_cache_analysis

        return result

    def compute_total_params(self) -> int:
        """
        Compute total number of parameters in the MoE model.

        This is an alias for get_total_params() to maintain compatibility
        with the web API that expects this method name.

        Returns:
            Total number of parameters in the model
        """
        return self.get_total_params()

    def get_operator_breakdown(
        self,
        batch_size: int = 1,
        sequence_length: int = 1,
        hardware_name: str = "nvidia_h100_sxm5",
        parallel_config: Optional[ParallelConfig] = None,
    ) -> Dict[str, Any]:
        """
        Get detailed operator-level breakdown for MoE model similar to Table 2 in the paper.

        Args:
            batch_size: Batch size for computation
            sequence_length: Sequence length
            hardware_name: Hardware specification to use for performance modeling
            parallel_config: Parallel configuration for communication analysis

        Returns:
            Dictionary with operator-level analysis including MoE-specific operators
        """
        try:
            hardware = HardwareSpecs.from_gpu_spec(hardware_name)
        except (FileNotFoundError, ValueError):
            # Fallback to default specs if GPU specs file not found
            hardware = HardwareSpecs(
                name="Default GPU",
                peak_flops={"fp16": 100.0, "bf16": 100.0, "fp32": 50.0},
                memory_bandwidth_gbps=1000.0,
                memory_size_gb=40,
            )

        hidden_size = self._parsed_config["hidden_size"]
        num_heads = self._parsed_config["num_attention_heads"]
        num_kv_heads = self._parsed_config["num_key_value_heads"]
        intermediate_size = self._parsed_config["intermediate_size"]
        moe_intermediate_size = self._parsed_config["moe_intermediate_size"]
        num_layers = self._parsed_config["num_hidden_layers"]
        num_dense_layers = self._parsed_config["num_dense_layers"]
        num_moe_layers = self._parsed_config["num_moe_layers"]
        vocab_size = self._parsed_config["vocab_size"]
        n_shared_experts = self._parsed_config["n_shared_experts"]
        n_routed_experts = self._parsed_config["n_routed_experts"]
        num_experts_per_tok = self._parsed_config["num_experts_per_tok"]

        operators = {}

        # Attention operator (same for all layers)
        attention_op = AttentionOperator(
            hidden_size=hidden_size, num_heads=num_heads, num_kv_heads=num_kv_heads
        )
        # Set shape and compute metrics
        attention_op.set_shape(batch_size, sequence_length, kv_lens=sequence_length)
        attention_metrics = attention_op.compute_metrics(hardware)
        operators["attention"] = {
            "operator": attention_op,
            "metrics_per_layer": attention_metrics,
            "metrics_total": OperatorMetrics(
                flops=attention_metrics.flops * num_layers,
                memory_capacity_bytes=attention_metrics.memory_capacity_bytes
                * num_layers,
                memory_movement_bytes=attention_metrics.memory_movement_bytes
                * num_layers,
                execution_time_ms=attention_metrics.execution_time_ms * num_layers,
                arithmetic_intensity=attention_metrics.arithmetic_intensity,
                utilization=attention_metrics.utilization,
            ),
        }

        # Dense MLP operator (for dense layers)
        if num_dense_layers > 0:
            dense_mlp_op = FFNLayer(
                hidden_size=hidden_size, intermediate_size=intermediate_size
            )
            # Set shape and compute metrics
            dense_mlp_op.set_shape(batch_size, sequence_length)
            dense_mlp_metrics = dense_mlp_op.compute_metrics(hardware)
            operators["dense_mlp"] = {
                "operator": dense_mlp_op,
                "metrics_per_layer": dense_mlp_metrics,
                "metrics_total": OperatorMetrics(
                    flops=dense_mlp_metrics.flops * num_dense_layers,
                    memory_capacity_bytes=dense_mlp_metrics.memory_capacity_bytes
                    * num_dense_layers,
                    memory_movement_bytes=dense_mlp_metrics.memory_movement_bytes
                    * num_dense_layers,
                    execution_time_ms=dense_mlp_metrics.execution_time_ms
                    * num_dense_layers,
                    arithmetic_intensity=dense_mlp_metrics.arithmetic_intensity,
                    utilization=dense_mlp_metrics.utilization,
                ),
            }

        # MoE operator (for MoE layers)
        if num_moe_layers > 0:
            moe_op = MoELayer(
                hidden_size=hidden_size,
                intermediate_size=moe_intermediate_size,
                num_experts=n_routed_experts,
                experts_per_token=num_experts_per_tok,
            )
            # Set shape and compute metrics
            moe_op.set_shape(batch_size, sequence_length)
            moe_metrics = moe_op.compute_metrics(hardware)

            # Add shared experts if present
            if n_shared_experts > 0:
                shared_expert_op = FFNLayer(
                    hidden_size=hidden_size, intermediate_size=moe_intermediate_size
                )
                # Set shape and compute metrics
                shared_expert_op.set_shape(batch_size, sequence_length)
                shared_expert_metrics = shared_expert_op.compute_metrics(hardware)

                # Combine MoE and shared expert metrics
                combined_moe_metrics = OperatorMetrics(
                    flops=moe_metrics.flops
                    + shared_expert_metrics.flops * n_shared_experts,
                    memory_capacity_bytes=moe_metrics.memory_capacity_bytes
                    + shared_expert_metrics.memory_capacity_bytes * n_shared_experts,
                    memory_movement_bytes=moe_metrics.memory_movement_bytes
                    + shared_expert_metrics.memory_movement_bytes * n_shared_experts,
                    execution_time_ms=max(
                        moe_metrics.execution_time_ms,
                        shared_expert_metrics.execution_time_ms * n_shared_experts,
                    ),
                    arithmetic_intensity=(
                        (
                            moe_metrics.flops
                            + shared_expert_metrics.flops * n_shared_experts
                        )
                        / (
                            moe_metrics.memory_movement_bytes
                            + shared_expert_metrics.memory_movement_bytes
                            * n_shared_experts
                        )
                        if (
                            moe_metrics.memory_movement_bytes
                            + shared_expert_metrics.memory_movement_bytes
                            * n_shared_experts
                        )
                        > 0
                        else 0
                    ),
                    utilization=max(
                        moe_metrics.utilization, shared_expert_metrics.utilization
                    ),
                )
                moe_metrics = combined_moe_metrics

            operators["moe"] = {
                "operator": moe_op,
                "metrics_per_layer": moe_metrics,
                "metrics_total": OperatorMetrics(
                    flops=moe_metrics.flops * num_moe_layers,
                    memory_capacity_bytes=moe_metrics.memory_capacity_bytes
                    * num_moe_layers,
                    memory_movement_bytes=moe_metrics.memory_movement_bytes
                    * num_moe_layers,
                    execution_time_ms=moe_metrics.execution_time_ms * num_moe_layers,
                    arithmetic_intensity=moe_metrics.arithmetic_intensity,
                    utilization=moe_metrics.utilization,
                ),
                "moe_details": {
                    "num_experts": n_routed_experts,
                    "shared_experts": n_shared_experts,
                    "experts_per_token": num_experts_per_tok,
                    "expert_utilization_rate": (
                        num_experts_per_tok / n_routed_experts
                        if n_routed_experts > 0
                        else 0
                    ),
                },
            }

        # Layer normalization (2 per layer: pre-attention and pre-MLP)
        layernorm_op = LayerNormOperator(hidden_size=hidden_size)
        # Set shape and compute metrics
        layernorm_op.set_shape(batch_size, sequence_length)
        layernorm_metrics = layernorm_op.compute_metrics(hardware)
        operators["layernorm"] = {
            "operator": layernorm_op,
            "metrics_per_layer": OperatorMetrics(
                flops=layernorm_metrics.flops * 2,  # 2 per layer
                memory_capacity_bytes=layernorm_metrics.memory_capacity_bytes * 2,
                memory_movement_bytes=layernorm_metrics.memory_movement_bytes * 2,
                execution_time_ms=layernorm_metrics.execution_time_ms * 2,
                arithmetic_intensity=layernorm_metrics.arithmetic_intensity,
                utilization=layernorm_metrics.utilization,
            ),
            "metrics_total": OperatorMetrics(
                flops=layernorm_metrics.flops * 2 * num_layers,
                memory_capacity_bytes=layernorm_metrics.memory_capacity_bytes
                * 2
                * num_layers,
                memory_movement_bytes=layernorm_metrics.memory_movement_bytes
                * 2
                * num_layers,
                execution_time_ms=layernorm_metrics.execution_time_ms * 2 * num_layers,
                arithmetic_intensity=layernorm_metrics.arithmetic_intensity,
                utilization=layernorm_metrics.utilization,
            ),
        }

        # Embedding layers
        token_embedding_op = EmbeddingOperator(vocab_size, hidden_size)
        # Set shape and compute metrics
        token_embedding_op.set_shape(batch_size, sequence_length)
        embedding_metrics = token_embedding_op.compute_metrics(hardware)
        operators["embeddings"] = {
            "operator": token_embedding_op,
            "metrics_total": embedding_metrics,
        }

        # Language modeling head (if not tied)
        if not self._parsed_config["tie_word_embeddings"]:
            lm_head_op = MatMulOperator(hidden_size, vocab_size)
            # Set shape and compute metrics
            lm_head_op.set_shape(batch_size, sequence_length)
            lm_head_metrics = lm_head_op.compute_metrics(hardware)
            operators["lm_head"] = {
                "operator": lm_head_op,
                "metrics_total": lm_head_metrics,
            }

        # Communication operators (if parallel config provided)
        if parallel_config:
            comm_operators = self._get_communication_operators(
                parallel_config, batch_size, sequence_length, hardware
            )
            operators.update(comm_operators)

        # Compute totals
        total_flops = sum(
            op_data["metrics_total"].flops for op_data in operators.values()
        )
        total_memory_capacity = sum(
            op_data["metrics_total"].memory_capacity_bytes
            for op_data in operators.values()
        )
        total_memory_movement = sum(
            op_data["metrics_total"].memory_movement_bytes
            for op_data in operators.values()
        )
        total_time = sum(
            op_data["metrics_total"].execution_time_ms for op_data in operators.values()
        )

        return {
            "hardware": hardware,
            "operators": operators,
            "totals": {
                "flops": total_flops,
                "memory_capacity_bytes": total_memory_capacity,
                "memory_movement_bytes": total_memory_movement,
                "execution_time_ms": total_time,
                "arithmetic_intensity": (
                    total_flops / total_memory_movement
                    if total_memory_movement > 0
                    else 0
                ),
                "throughput_tokens_per_sec": (
                    (batch_size * sequence_length * 1000) / total_time
                    if total_time > 0
                    else 0
                ),
            },
            "model_config": {
                "hidden_size": hidden_size,
                "num_layers": num_layers,
                "num_dense_layers": num_dense_layers,
                "num_moe_layers": num_moe_layers,
                "num_heads": num_heads,
                "num_kv_heads": num_kv_heads,
                "intermediate_size": intermediate_size,
                "moe_intermediate_size": moe_intermediate_size,
                "vocab_size": vocab_size,
                "n_shared_experts": n_shared_experts,
                "n_routed_experts": n_routed_experts,
                "num_experts_per_tok": num_experts_per_tok,
                "batch_size": batch_size,
                "sequence_length": sequence_length,
            },
            "parallel_config": parallel_config,
        }

    def _get_communication_operators(
        self,
        parallel_config: ParallelConfig,
        batch_size: int,
        sequence_length: int,
        hardware: HardwareSpecs,
    ) -> Dict[str, Any]:
        """Get communication operators for parallel execution."""
        comm_operators = {}

        hidden_size = self._parsed_config["hidden_size"]
        bytes_per_element = 2  # FP16

        # Tensor parallel communication (AllReduce)
        if parallel_config.tensor_parallel_size > 1:
            # AllReduce for attention and MLP outputs
            allreduce_size = (
                batch_size * sequence_length * hidden_size * bytes_per_element
            )

            tp_comm_op = CommunicationOperator(
                operation_type="AllReduce",
                data_size_bytes=allreduce_size,
                num_devices=parallel_config.tensor_parallel_size,
                bandwidth_gbps=100.0,  # Assume NVLink bandwidth
            )

            # Communication happens after each layer
            num_layers = self._parsed_config["num_hidden_layers"]
            comm_time = (
                tp_comm_op.compute_communication_time_ms() * num_layers * 2
            )  # attention + MLP

            comm_operators["tensor_parallel_comm"] = {
                "operator": tp_comm_op,
                "metrics_total": OperatorMetrics(
                    flops=0,
                    param_size=tp_comm_op.compute_params_bytes(),
                    memory_movement_bytes=tp_comm_op.compute_memory_access_bytes(),
                    execution_time_ms=comm_time,
                    arithmetic_intensity=0,
                    utilization=0,
                ),
            }

        # Expert parallel communication (AllToAll for MoE)
        if (
            parallel_config.expert_parallel_size > 1
            and self._parsed_config["num_moe_layers"] > 0
        ):
            # Token routing communication
            routing_size = (
                batch_size * sequence_length * hidden_size * bytes_per_element
            )

            ep_comm_op = CommunicationOperator(
                operation_type="AllToAll",
                data_size_bytes=routing_size,
                num_devices=parallel_config.expert_parallel_size,
                bandwidth_gbps=100.0,
            )

            num_moe_layers = self._parsed_config["num_moe_layers"]
            comm_time = (
                ep_comm_op.compute_communication_time_ms() * num_moe_layers * 2
            )  # routing + gathering

            comm_operators["expert_parallel_comm"] = {
                "operator": ep_comm_op,
                "metrics_total": OperatorMetrics(
                    flops=0,
                    param_size=tp_comm_op.compute_params_bytes(),
                    memory_movement_bytes=ep_comm_op.compute_memory_access_bytes(),
                    execution_time_ms=comm_time,
                    arithmetic_intensity=0,
                    utilization=0,
                ),
            }

        return comm_operators

    def get_roofline_analysis(
        self,
        batch_size: int = 1,
        sequence_length: int = 2048,
        hardware_name: str = "nvidia_h100_sxm5",
        parallel_config: Optional[ParallelConfig] = None,
    ) -> Dict[str, Any]:
        """
        Get roofline analysis for the MoE model on specified hardware.

        Args:
            batch_size: Batch size
            sequence_length: Sequence length
            hardware_name: Hardware specification
            parallel_config: Parallel configuration

        Returns:
            Dictionary with roofline analysis data including MoE-specific analysis
        """
        operator_breakdown = self.get_operator_breakdown(
            batch_size, sequence_length, hardware_name, parallel_config
        )
        hardware = operator_breakdown["hardware"]

        # Get peak performance for different precisions
        peak_flops = hardware.peak_flops
        memory_bandwidth_gbps = hardware.memory_bandwidth_gbps

        roofline_data = {
            "hardware": {
                "name": hardware.name,
                "peak_flops": peak_flops,
                "memory_bandwidth_gbps": memory_bandwidth_gbps,
            },
            "operators": {},
            "model_point": {},
            "moe_analysis": {},
        }

        # Analyze each operator
        for op_name, op_data in operator_breakdown["operators"].items():
            metrics = op_data["metrics_total"]

            # Arithmetic intensity (FLOPs per byte)
            ai = metrics.arithmetic_intensity

            # Achieved performance (GFLOPS)
            achieved_gflops = (
                metrics.flops / (metrics.execution_time_ms / 1000) / 1e9
                if metrics.execution_time_ms > 0
                else 0
            )

            # Memory bandwidth utilization
            memory_bw_utilization = (
                (metrics.memory_movement_bytes / (metrics.execution_time_ms / 1000))
                / (memory_bandwidth_gbps * 1e9)
                * 100
                if metrics.execution_time_ms > 0
                else 0
            )

            roofline_data["operators"][op_name] = {
                "arithmetic_intensity": ai,
                "achieved_gflops": achieved_gflops,
                "memory_bandwidth_utilization": memory_bw_utilization,
                "compute_utilization": metrics.utilization,
                "is_compute_bound": ai
                > (peak_flops.get("fp16", 100) * 1e12) / (memory_bandwidth_gbps * 1e9),
                "bottleneck": (
                    "compute"
                    if ai
                    > (peak_flops.get("fp16", 100) * 1e12)
                    / (memory_bandwidth_gbps * 1e9)
                    else "memory"
                ),
            }

        # Overall model characteristics
        totals = operator_breakdown["totals"]
        model_ai = totals["arithmetic_intensity"]
        model_gflops = (
            totals["flops"] / (totals["execution_time_ms"] / 1000) / 1e9
            if totals["execution_time_ms"] > 0
            else 0
        )

        roofline_data["model_point"] = {
            "arithmetic_intensity": model_ai,
            "achieved_gflops": model_gflops,
            "peak_gflops": peak_flops.get("fp16", 100)
            * 1000,  # Convert TFLOPS to GFLOPS
            "efficiency": (
                (model_gflops / (peak_flops.get("fp16", 100) * 1000)) * 100
                if peak_flops.get("fp16", 100) > 0
                else 0
            ),
            "is_compute_bound": model_ai
            > (peak_flops.get("fp16", 100) * 1e12) / (memory_bandwidth_gbps * 1e9),
            "bottleneck": (
                "compute"
                if model_ai
                > (peak_flops.get("fp16", 100) * 1e12) / (memory_bandwidth_gbps * 1e9)
                else "memory"
            ),
        }

        # MoE-specific analysis
        if "moe" in operator_breakdown["operators"]:
            moe_data = operator_breakdown["operators"]["moe"]
            moe_details = moe_data.get("moe_details", {})

            roofline_data["moe_analysis"] = {
                "expert_utilization_rate": moe_details.get(
                    "expert_utilization_rate", 0
                ),
                "active_vs_total_experts": f"{moe_details.get('experts_per_token', 0)}/{moe_details.get('num_experts', 0)}",
                "shared_experts": moe_details.get("shared_experts", 0),
                "parameter_efficiency": self.compute_active_params_per_token()
                / max(1, self.get_total_params()),
                "moe_arithmetic_intensity": moe_data[
                    "metrics_total"
                ].arithmetic_intensity,
                "moe_bottleneck": roofline_data["operators"]["moe"]["bottleneck"],
            }

        return roofline_data

    def __repr__(self):
        result_str = super().__repr__()
        result_str += f' experts_per_token: {self._parsed_config["num_experts_per_tok"]}\n shared_experts: {self._parsed_config["n_shared_experts"]}\n routed_experts: {self._parsed_config["n_routed_experts"]}'
        return result_str

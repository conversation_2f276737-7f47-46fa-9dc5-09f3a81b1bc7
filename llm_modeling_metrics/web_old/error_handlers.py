"""
Enhanced error handling middleware and utilities for attention analysis API.

This module provides comprehensive error handling with user-friendly messages,
graceful degradation, and detailed logging for debugging.
"""

import logging
import traceback
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from pydantic import ValidationError
import asyncio

from .validation import AttentionValidationError, create_user_friendly_error_response

logger = logging.getLogger(__name__)

class AttentionErrorHandler:
    """Centralized error handler for attention analysis operations."""
    
    @staticmethod
    async def handle_api_error(request: Request, error: Exception) -> JSONResponse:
        """
        Handle API errors with comprehensive logging and user-friendly responses.
        
        Args:
            request: FastAPI request object
            error: The exception that occurred
        
        Returns:
            JSONResponse with error details and guidance
        """
        # Generate unique error ID for tracking
        error_id = f"attn_err_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{id(error) % 10000:04d}"
        
        # Extract request context
        context = {
            "error_id": error_id,
            "endpoint": str(request.url.path),
            "method": request.method,
            "timestamp": datetime.now().isoformat(),
            "client_ip": request.client.host if request.client else "unknown"
        }
        
        # Determine error type and create appropriate response
        if isinstance(error, AttentionValidationError):
            status_code = 400
            error_response = create_user_friendly_error_response(error, "validation")
            logger.warning(f"Validation error [{error_id}]: {error.message}", extra=context)
            
        elif isinstance(error, ValidationError):
            status_code = 422
            error_response = create_user_friendly_error_response(error, "request_parsing")
            logger.warning(f"Pydantic validation error [{error_id}]: {error}", extra=context)
            
        elif isinstance(error, HTTPException):
            status_code = error.status_code
            error_response = {
                "error": error.detail,
                "error_type": "http_error",
                "suggestions": AttentionErrorHandler._get_http_error_suggestions(error.status_code),
                "context": "api_request"
            }
            logger.warning(f"HTTP error [{error_id}]: {error.status_code} - {error.detail}", extra=context)
            
        elif isinstance(error, asyncio.TimeoutError):
            status_code = 504
            error_response = {
                "error": "Request timed out during attention analysis",
                "error_type": "timeout_error",
                "suggestions": [
                    "Try reducing the number of models or sequence lengths",
                    "Consider using smaller batch sizes",
                    "The analysis may be too complex for the current timeout limits"
                ],
                "context": "analysis_timeout"
            }
            logger.error(f"Timeout error [{error_id}]: {error}", extra=context)
            
        elif isinstance(error, MemoryError):
            status_code = 507
            error_response = {
                "error": "Insufficient memory for attention analysis",
                "error_type": "memory_error",
                "suggestions": [
                    "Reduce batch size or sequence lengths",
                    "Analyze fewer models simultaneously",
                    "Try using lower precision (e.g., fp16 instead of fp32)"
                ],
                "context": "memory_exhaustion"
            }
            logger.error(f"Memory error [{error_id}]: {error}", extra=context)
            
        elif "model" in str(error).lower() and "not found" in str(error).lower():
            status_code = 404
            error_response = {
                "error": f"Model not found: {str(error)}",
                "error_type": "model_not_found",
                "suggestions": [
                    "Check model name spelling and format",
                    "Use the /api/attention/config/supported-models endpoint to see available models",
                    "Ensure the model is supported for attention analysis"
                ],
                "context": "model_loading"
            }
            logger.error(f"Model not found error [{error_id}]: {error}", extra=context)
            
        elif "hardware" in str(error).lower():
            status_code = 400
            error_response = {
                "error": f"Hardware configuration error: {str(error)}",
                "error_type": "hardware_error",
                "suggestions": [
                    "Check hardware specification names",
                    "Use supported hardware: H100, A800, H20, 910B",
                    "Ensure hardware specifications are properly configured"
                ],
                "context": "hardware_validation"
            }
            logger.error(f"Hardware error [{error_id}]: {error}", extra=context)
            
        else:
            # Generic server error
            status_code = 500
            error_response = {
                "error": "An unexpected error occurred during attention analysis",
                "error_type": "internal_error",
                "suggestions": [
                    "Please try again in a few moments",
                    "If the issue persists, contact support with error ID",
                    "Check that all request parameters are valid"
                ],
                "context": "internal_processing"
            }
            logger.error(f"Unexpected error [{error_id}]: {error}", extra=context, exc_info=True)
        
        # Add error ID and timestamp to response
        error_response.update({
            "error_id": error_id,
            "timestamp": context["timestamp"]
        })
        
        return JSONResponse(
            status_code=status_code,
            content=error_response
        )
    
    @staticmethod
    def _get_http_error_suggestions(status_code: int) -> list:
        """Get suggestions based on HTTP status code."""
        suggestions_map = {
            400: [
                "Check request parameters and format",
                "Ensure all required fields are provided",
                "Validate data types and ranges"
            ],
            401: [
                "Check authentication credentials",
                "Ensure API token is valid and not expired"
            ],
            403: [
                "Check API permissions",
                "Ensure you have access to attention analysis features"
            ],
            404: [
                "Check the API endpoint URL",
                "Verify the requested resource exists"
            ],
            422: [
                "Check request body format and data types",
                "Ensure all required fields are provided with correct types"
            ],
            429: [
                "Reduce request frequency",
                "Wait before making additional requests",
                "Consider upgrading API limits if needed"
            ],
            500: [
                "Try again in a few moments",
                "Contact support if the issue persists"
            ]
        }
        return suggestions_map.get(status_code, ["Contact support for assistance"])
    
    @staticmethod
    def wrap_analysis_operation(operation_name: str):
        """
        Decorator to wrap analysis operations with comprehensive error handling.
        
        Args:
            operation_name: Name of the operation for logging context
        """
        def decorator(func):
            async def wrapper(*args, **kwargs):
                try:
                    logger.info(f"Starting {operation_name}")
                    result = await func(*args, **kwargs)
                    logger.info(f"Completed {operation_name} successfully")
                    return result
                    
                except AttentionValidationError as e:
                    logger.warning(f"{operation_name} validation error: {e.message}")
                    raise
                    
                except Exception as e:
                    logger.error(f"{operation_name} failed: {e}", exc_info=True)
                    # Re-raise with additional context
                    if hasattr(e, 'args') and e.args:
                        e.args = (f"{operation_name}: {e.args[0]}",) + e.args[1:]
                    raise
                    
            return wrapper
        return decorator


class GracefulDegradationHandler:
    """Handler for graceful degradation when partial failures occur."""
    
    @staticmethod
    def handle_partial_model_failure(
        requested_models: list,
        successful_results: dict,
        failed_models: dict
    ) -> dict:
        """
        Handle cases where some models fail but others succeed.
        
        Args:
            requested_models: List of originally requested models
            successful_results: Dict of successful analysis results
            failed_models: Dict of failed models with error messages
        
        Returns:
            Dict with partial results and warnings
        """
        warnings = []
        
        if failed_models:
            failed_list = list(failed_models.keys())
            warnings.append(f"Analysis failed for models: {failed_list}")
            
            # Add specific failure reasons
            for model, error in failed_models.items():
                if "not found" in str(error).lower():
                    warnings.append(f"Model '{model}' not found - check model name")
                elif "memory" in str(error).lower():
                    warnings.append(f"Model '{model}' requires too much memory")
                elif "timeout" in str(error).lower():
                    warnings.append(f"Model '{model}' analysis timed out")
                else:
                    warnings.append(f"Model '{model}' failed: {str(error)[:100]}")
        
        if successful_results:
            success_count = len(successful_results)
            total_count = len(requested_models)
            warnings.append(f"Partial success: {success_count}/{total_count} models analyzed")
            
            return {
                "results": successful_results,
                "warnings": warnings,
                "partial_success": True,
                "failed_models": list(failed_models.keys()),
                "success_rate": success_count / total_count
            }
        else:
            # Complete failure
            raise AttentionValidationError(
                "All models failed analysis",
                suggestions=[
                    "Check model names and availability",
                    "Try with different models or simpler configurations",
                    "Reduce batch size or sequence lengths"
                ]
            )
    
    @staticmethod
    def handle_hardware_fallback(
        requested_hardware: list,
        available_hardware: list
    ) -> tuple:
        """
        Handle hardware fallback when requested hardware is not available.
        
        Args:
            requested_hardware: List of requested hardware names
            available_hardware: List of actually available hardware
        
        Returns:
            Tuple of (fallback_hardware, warnings)
        """
        warnings = []
        fallback_hardware = []
        
        # Try to match requested hardware
        for hw in requested_hardware:
            if hw in available_hardware:
                fallback_hardware.append(hw)
            else:
                # Find closest match
                hw_lower = hw.lower()
                matches = [avail for avail in available_hardware if hw_lower in avail.lower()]
                if matches:
                    fallback_hardware.append(matches[0])
                    warnings.append(f"Hardware '{hw}' not available, using '{matches[0]}'")
                else:
                    warnings.append(f"Hardware '{hw}' not available and no close match found")
        
        # If no matches, use default hardware
        if not fallback_hardware:
            default_hardware = ["H100"] if "H100" in available_hardware else available_hardware[:1]
            fallback_hardware = default_hardware
            warnings.append(f"No requested hardware available, using default: {default_hardware}")
        
        return fallback_hardware, warnings


def create_error_context(request: Request, additional_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Create comprehensive error context for logging and debugging.
    
    Args:
        request: FastAPI request object
        additional_info: Additional context information
    
    Returns:
        Dict with error context
    """
    context = {
        "timestamp": datetime.now().isoformat(),
        "endpoint": str(request.url.path),
        "method": request.method,
        "query_params": dict(request.query_params),
        "client_ip": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown")
    }
    
    if additional_info:
        context.update(additional_info)
    
    return context


async def log_request_details(request: Request, operation: str):
    """
    Log detailed request information for debugging.
    
    Args:
        request: FastAPI request object
        operation: Name of the operation being performed
    """
    try:
        body = await request.body()
        context = create_error_context(request, {
            "operation": operation,
            "body_size": len(body),
            "content_type": request.headers.get("content-type", "unknown")
        })
        
        logger.debug(f"Request details for {operation}", extra=context)
        
    except Exception as e:
        logger.warning(f"Failed to log request details: {e}")
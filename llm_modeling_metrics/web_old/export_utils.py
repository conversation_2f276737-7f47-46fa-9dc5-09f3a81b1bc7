"""
Export utilities for attention analysis data and visualizations.

This module provides functionality to export attention analysis results in various formats:
- CSV format for data analysis
- PNG format for high-quality images
- SVG format for vector graphics
- Configuration metadata inclusion
"""

import csv
import io
import json
import os
import tempfile
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.backends.backend_svg import FigureCanvasSVG

from .models import AttentionAnalysisResponse, AttentionMetricsModel, RooflinePointModel


class AttentionExporter:
    """Handles export functionality for attention analysis results."""

    def __init__(self):
        """Initialize the exporter with default settings."""
        self.default_dpi = 300
        self.default_figsize = (12, 8)
        
    def export_to_csv(
        self, 
        analysis_response: AttentionAnalysisResponse,
        include_metadata: bool = True
    ) -> str:
        """
        Export attention analysis data to CSV format.
        
        Args:
            analysis_response: The attention analysis response containing metrics
            include_metadata: Whether to include configuration metadata
            
        Returns:
            CSV content as string
        """
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header with metadata if requested
        if include_metadata:
            writer.writerow(['# Attention Analysis Export'])
            writer.writerow(['# Generated:', analysis_response.timestamp.isoformat()])
            writer.writerow(['# Execution Time (seconds):', analysis_response.execution_time])
            writer.writerow([''])
        
        # Write main data header
        headers = [
            'model_name',
            'mechanism_type', 
            'sequence_length',
            'batch_size',
            'hardware_name',
            'total_flops',
            'attention_flops',
            'projection_flops',
            'memory_capacity_bytes',
            'memory_movement_bytes',
            'kv_cache_bytes',
            'operational_intensity',
            'achieved_performance_tflops',
            'peak_performance_tflops',
            'memory_bandwidth_gbps',
            'flash_attention_enabled',
            'precision'
        ]
        writer.writerow(headers)
        
        # Write metrics data
        for metric in analysis_response.metrics:
            row = [
                metric.model_name,
                metric.mechanism_type,
                metric.sequence_length,
                metric.batch_size,
                metric.hardware_name,
                metric.total_flops,
                metric.attention_flops,
                metric.projection_flops,
                metric.memory_capacity_bytes,
                metric.memory_movement_bytes,
                metric.kv_cache_bytes,
                metric.operational_intensity,
                metric.achieved_performance_tflops,
                metric.peak_performance_tflops,
                metric.memory_bandwidth_gbps,
                metric.flash_attention_enabled,
                metric.precision
            ]
            writer.writerow(row)
        
        # Write roofline points section if available
        if analysis_response.roofline_points:
            writer.writerow([''])
            writer.writerow(['# Roofline Plot Points'])
            roofline_headers = [
                'model_name',
                'attention_type',
                'sequence_length',
                'memory_access_gb',
                'compute_gflops',
                'operational_intensity',
                'bottleneck_type'
            ]
            writer.writerow(roofline_headers)
            
            for point in analysis_response.roofline_points:
                row = [
                    point.model_name,
                    point.attention_type,
                    point.sequence_length,
                    point.x,  # memory_access_gb
                    point.y,  # compute_gflops
                    point.operational_intensity,
                    point.bottleneck_type
                ]
                writer.writerow(row)
        
        # Write hardware limits section if available
        if analysis_response.hardware_limits:
            writer.writerow([''])
            writer.writerow(['# Hardware Limits'])
            writer.writerow(['hardware_name', 'precision', 'memory_access_gb', 'compute_gflops'])
            
            for hw_name, precisions in analysis_response.hardware_limits.items():
                for precision, limits in precisions.items():
                    # Limits should be pairs of [memory_access, compute]
                    for i in range(0, len(limits), 2):
                        if i + 1 < len(limits):
                            writer.writerow([hw_name, precision, limits[i], limits[i + 1]])
        
        return output.getvalue()
    
    def create_roofline_plot(
        self,
        analysis_response: AttentionAnalysisResponse,
        title: Optional[str] = None,
        figsize: Optional[Tuple[float, float]] = None
    ) -> plt.Figure:
        """
        Create a roofline plot from attention analysis data.
        
        Args:
            analysis_response: The attention analysis response
            title: Optional custom title for the plot
            figsize: Optional figure size tuple (width, height)
            
        Returns:
            Matplotlib figure object
        """
        if figsize is None:
            figsize = self.default_figsize
            
        # Set up the plot style
        sns.set_style("whitegrid")
        sns.set_palette("husl")
        
        fig, ax = plt.subplots(1, 1, figsize=figsize)
        
        # Plot hardware rooflines
        hardware_colors = {}
        if analysis_response.hardware_limits:
            color_palette = sns.color_palette("husl", len(analysis_response.hardware_limits))
            
            for i, (hw_name, precisions) in enumerate(analysis_response.hardware_limits.items()):
                color = color_palette[i]
                hardware_colors[hw_name] = color
                
                # Plot roofline for the primary precision (usually the first one)
                if precisions:
                    precision_name = list(precisions.keys())[0]
                    limits = precisions[precision_name]
                    
                    # Convert limits to x, y arrays
                    x_vals = []
                    y_vals = []
                    for j in range(0, len(limits), 2):
                        if j + 1 < len(limits):
                            x_vals.append(limits[j])
                            y_vals.append(limits[j + 1])
                    
                    if x_vals and y_vals:
                        ax.plot(
                            x_vals, y_vals, '--', 
                            color=color, alpha=0.7, 
                            label=hw_name, linewidth=1.5
                        )
        
        # Plot model trajectories and points
        model_colors = {}
        model_markers = {'MLA': 'o', 'GQA': 's', 'MFA': '*', 'MHA': '^'}
        
        if analysis_response.roofline_points:
            # Group points by model
            model_points = {}
            for point in analysis_response.roofline_points:
                if point.model_name not in model_points:
                    model_points[point.model_name] = []
                model_points[point.model_name].append(point)
            
            # Assign colors to models
            model_names = list(model_points.keys())
            if model_names:
                model_color_palette = sns.color_palette("deep", len(model_names))
                for i, model_name in enumerate(model_names):
                    model_colors[model_name] = model_color_palette[i]
            
            # Plot each model's trajectory
            for model_name, points in model_points.items():
                # Sort points by sequence length for trajectory
                points.sort(key=lambda p: p.sequence_length)
                
                x_vals = [p.x for p in points]
                y_vals = [p.y for p in points]
                color = model_colors.get(model_name, 'black')
                
                # Determine marker based on attention type
                attention_type = points[0].attention_type if points else 'MHA'
                marker = model_markers.get(attention_type, 'o')
                
                # Plot trajectory line
                if len(points) > 1:
                    ax.plot(x_vals, y_vals, '-', color=color, alpha=0.7, linewidth=2)
                
                # Plot individual points
                markersize = 12 if marker == '*' else 8
                ax.scatter(
                    x_vals, y_vals,
                    color=color, marker=marker,
                    s=markersize**2, label=model_name,
                    zorder=5, alpha=0.8
                )
        
        # Customize the plot
        ax.set_xlabel("Memory access (GB)", fontsize=12)
        ax.set_ylabel("Compute (GFLOPS)", fontsize=12)
        
        # Set reasonable axis limits if not automatically determined
        if analysis_response.roofline_points:
            max_x = max(p.x for p in analysis_response.roofline_points)
            max_y = max(p.y for p in analysis_response.roofline_points)
            ax.set_xlim(0, max_x * 1.1)
            ax.set_ylim(0, max_y * 1.1)
        else:
            ax.set_xlim(0, 3.5)
            ax.set_ylim(0, 600)
        
        # Add grid and legend
        ax.grid(True, alpha=0.3)
        ax.legend(loc="upper left", fontsize=10)
        
        # Set title
        if title is None:
            title = "Compute and Memory Access of Different Attention Designs During Decoding"
        ax.set_title(title, fontsize=14, pad=20)
        
        plt.tight_layout()
        return fig
    
    def export_to_png(
        self,
        analysis_response: AttentionAnalysisResponse,
        title: Optional[str] = None,
        dpi: Optional[int] = None,
        figsize: Optional[Tuple[float, float]] = None
    ) -> bytes:
        """
        Export attention analysis visualization to PNG format.
        
        Args:
            analysis_response: The attention analysis response
            title: Optional custom title for the plot
            dpi: Optional DPI for the image (default: 300)
            figsize: Optional figure size tuple (width, height)
            
        Returns:
            PNG image data as bytes
        """
        if dpi is None:
            dpi = self.default_dpi
            
        fig = self.create_roofline_plot(analysis_response, title, figsize)
        
        # Save to bytes buffer
        buffer = io.BytesIO()
        fig.savefig(buffer, format='png', dpi=dpi, bbox_inches='tight')
        buffer.seek(0)
        
        plt.close(fig)  # Clean up
        return buffer.getvalue()
    
    def export_to_svg(
        self,
        analysis_response: AttentionAnalysisResponse,
        title: Optional[str] = None,
        figsize: Optional[Tuple[float, float]] = None
    ) -> str:
        """
        Export attention analysis visualization to SVG format.
        
        Args:
            analysis_response: The attention analysis response
            title: Optional custom title for the plot
            figsize: Optional figure size tuple (width, height)
            
        Returns:
            SVG content as string
        """
        fig = self.create_roofline_plot(analysis_response, title, figsize)
        
        # Save to string buffer
        buffer = io.StringIO()
        fig.savefig(buffer, format='svg', bbox_inches='tight')
        buffer.seek(0)
        
        plt.close(fig)  # Clean up
        return buffer.getvalue()
    
    def create_export_metadata(
        self,
        analysis_response: AttentionAnalysisResponse,
        export_format: str,
        additional_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create metadata for exported files.
        
        Args:
            analysis_response: The attention analysis response
            export_format: Format of the export (csv, png, svg)
            additional_config: Additional configuration to include
            
        Returns:
            Metadata dictionary
        """
        metadata = {
            "export_format": export_format,
            "export_timestamp": datetime.now().isoformat(),
            "analysis_timestamp": analysis_response.timestamp.isoformat(),
            "execution_time_seconds": analysis_response.execution_time,
            "models_analyzed": len(set(m.model_name for m in analysis_response.metrics)),
            "hardware_analyzed": len(set(m.hardware_name for m in analysis_response.metrics)),
            "sequence_lengths": sorted(list(set(m.sequence_length for m in analysis_response.metrics))),
            "attention_mechanisms": list(set(m.mechanism_type for m in analysis_response.metrics)),
            "precisions": list(set(m.precision for m in analysis_response.metrics)),
            "total_data_points": len(analysis_response.metrics),
            "roofline_points": len(analysis_response.roofline_points),
            "hardware_limits": list(analysis_response.hardware_limits.keys()) if analysis_response.hardware_limits else []
        }
        
        if additional_config:
            metadata.update(additional_config)
            
        return metadata


# Global exporter instance
_exporter = AttentionExporter()


def export_attention_analysis_csv(
    analysis_response: AttentionAnalysisResponse,
    include_metadata: bool = True
) -> str:
    """
    Export attention analysis to CSV format.
    
    Args:
        analysis_response: The attention analysis response
        include_metadata: Whether to include configuration metadata
        
    Returns:
        CSV content as string
    """
    return _exporter.export_to_csv(analysis_response, include_metadata)


def export_attention_analysis_png(
    analysis_response: AttentionAnalysisResponse,
    title: Optional[str] = None,
    dpi: Optional[int] = None,
    figsize: Optional[Tuple[float, float]] = None
) -> bytes:
    """
    Export attention analysis visualization to PNG format.
    
    Args:
        analysis_response: The attention analysis response
        title: Optional custom title for the plot
        dpi: Optional DPI for the image
        figsize: Optional figure size tuple (width, height)
        
    Returns:
        PNG image data as bytes
    """
    return _exporter.export_to_png(analysis_response, title, dpi, figsize)


def export_attention_analysis_svg(
    analysis_response: AttentionAnalysisResponse,
    title: Optional[str] = None,
    figsize: Optional[Tuple[float, float]] = None
) -> str:
    """
    Export attention analysis visualization to SVG format.
    
    Args:
        analysis_response: The attention analysis response
        title: Optional custom title for the plot
        figsize: Optional figure size tuple (width, height)
        
    Returns:
        SVG content as string
    """
    return _exporter.export_to_svg(analysis_response, title, figsize)


def create_export_metadata(
    analysis_response: AttentionAnalysisResponse,
    export_format: str,
    additional_config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create metadata for exported files.
    
    Args:
        analysis_response: The attention analysis response
        export_format: Format of the export (csv, png, svg)
        additional_config: Additional configuration to include
        
    Returns:
        Metadata dictionary
    """
    return _exporter.create_export_metadata(analysis_response, export_format, additional_config)
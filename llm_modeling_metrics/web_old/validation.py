"""
Comprehensive validation utilities for attention analysis API endpoints.

This module provides enhanced validation functions with detailed error messages
and user-friendly guidance for the attention performance analysis system.
"""

from typing import List, Dict, Any, Optional, Tuple
import re
from pydantic import ValidationError
from fastapi import HTTPException
import logging

logger = logging.getLogger(__name__)

class AttentionValidationError(Exception):
    """Custom exception for attention analysis validation errors."""
    
    def __init__(self, message: str, field: str = None, suggestions: List[str] = None):
        self.message = message
        self.field = field
        self.suggestions = suggestions or []
        super().__init__(self.message)


class AttentionValidator:
    """Comprehensive validator for attention analysis requests."""
    
    # Supported configurations
    SUPPORTED_PRECISIONS = ["fp32", "fp16", "bf16", "fp8", "int8"]
    SUPPORTED_ATTENTION_TYPES = ["MHA", "GQA", "MLA", "MFA"]
    
    # Hardware specifications (should be loaded from actual hardware specs)
    SUPPORTED_HARDWARE = ["H100", "A800", "H20", "910B", "V100", "A100", "RTX4090"]
    
    # Model name patterns for validation
    KNOWN_MODEL_PATTERNS = [
        r".*llama.*",
        r".*qwen.*",
        r".*deepseek.*",
        r".*mistral.*",
        r".*mixtral.*",
        r".*step.*",
        r".*gpt.*",
        r".*bert.*"
    ]
    
    # Reasonable limits
    MIN_SEQUENCE_LENGTH = 1
    MAX_SEQUENCE_LENGTH = 131072  # 128K tokens
    MIN_BATCH_SIZE = 1
    MAX_BATCH_SIZE = 128
    MAX_MODELS_PER_REQUEST = 20
    MAX_HARDWARE_PER_REQUEST = 10
    MAX_SEQUENCE_LENGTHS_PER_REQUEST = 50
    
    @classmethod
    def validate_model_names(cls, model_names: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validate model names with detailed feedback.
        
        Returns:
            Tuple of (valid_models, warnings)
        """
        if not model_names:
            raise AttentionValidationError(
                "At least one model name must be provided",
                field="model_names",
                suggestions=["Try popular models like 'meta-llama/Llama-2-7b-hf' or 'deepseek-ai/DeepSeek-V3-Base'"]
            )
        
        if len(model_names) > cls.MAX_MODELS_PER_REQUEST:
            raise AttentionValidationError(
                f"Too many models requested ({len(model_names)}). Maximum allowed: {cls.MAX_MODELS_PER_REQUEST}",
                field="model_names",
                suggestions=[
                    f"Reduce the number of models to {cls.MAX_MODELS_PER_REQUEST} or fewer",
                    "Consider using batch processing for large model sets"
                ]
            )
        
        valid_models = []
        warnings = []
        
        for model_name in model_names:
            if not model_name or not model_name.strip():
                warnings.append(f"Empty model name ignored")
                continue
                
            model_name = model_name.strip()
            
            # Check for suspicious patterns
            if len(model_name) < 3:
                warnings.append(f"Model name '{model_name}' is very short and may not be valid")
            elif len(model_name) > 200:
                warnings.append(f"Model name '{model_name}' is unusually long")
            
            # Check for known patterns
            is_known_pattern = any(re.match(pattern, model_name.lower()) for pattern in cls.KNOWN_MODEL_PATTERNS)
            if not is_known_pattern:
                warnings.append(f"Model '{model_name}' doesn't match known model patterns")
            
            valid_models.append(model_name)
        
        if not valid_models:
            raise AttentionValidationError(
                "No valid model names provided after filtering",
                field="model_names",
                suggestions=[
                    "Ensure model names are not empty",
                    "Use standard model naming conventions (e.g., 'organization/model-name')"
                ]
            )
        
        return valid_models, warnings
    
    @classmethod
    def validate_sequence_lengths(cls, sequence_lengths: List[int]) -> Tuple[List[int], List[str]]:
        """
        Validate sequence lengths with detailed feedback.
        
        Returns:
            Tuple of (valid_lengths, warnings)
        """
        if not sequence_lengths:
            raise AttentionValidationError(
                "At least one sequence length must be provided",
                field="sequence_lengths",
                suggestions=["Try common lengths like [1024, 2048, 4096, 8192] for analysis"]
            )
        
        if len(sequence_lengths) > cls.MAX_SEQUENCE_LENGTHS_PER_REQUEST:
            raise AttentionValidationError(
                f"Too many sequence lengths ({len(sequence_lengths)}). Maximum: {cls.MAX_SEQUENCE_LENGTHS_PER_REQUEST}",
                field="sequence_lengths",
                suggestions=[f"Reduce to {cls.MAX_SEQUENCE_LENGTHS_PER_REQUEST} or fewer sequence lengths"]
            )
        
        valid_lengths = []
        warnings = []
        
        for length in sequence_lengths:
            if not isinstance(length, int):
                try:
                    length = int(length)
                except (ValueError, TypeError):
                    warnings.append(f"Invalid sequence length '{length}' ignored (not a number)")
                    continue
            
            if length < cls.MIN_SEQUENCE_LENGTH:
                warnings.append(f"Sequence length {length} is below minimum ({cls.MIN_SEQUENCE_LENGTH})")
                continue
            elif length > cls.MAX_SEQUENCE_LENGTH:
                warnings.append(f"Sequence length {length} exceeds maximum ({cls.MAX_SEQUENCE_LENGTH})")
                continue
            
            # Check for reasonable powers of 2 or common values
            if length > 1024 and length & (length - 1) != 0:  # Not a power of 2
                if length not in [1536, 3072, 6144, 12288, 24576]:  # Common non-power-of-2 values
                    warnings.append(f"Sequence length {length} is not a power of 2 or common value")
            
            valid_lengths.append(length)
        
        if not valid_lengths:
            raise AttentionValidationError(
                "No valid sequence lengths provided after filtering",
                field="sequence_lengths",
                suggestions=[
                    f"Use lengths between {cls.MIN_SEQUENCE_LENGTH} and {cls.MAX_SEQUENCE_LENGTH}",
                    "Consider powers of 2 like 1024, 2048, 4096, 8192 for better performance"
                ]
            )
        
        # Remove duplicates and sort
        valid_lengths = sorted(list(set(valid_lengths)))
        
        return valid_lengths, warnings
    
    @classmethod
    def validate_hardware_names(cls, hardware_names: List[str], available_hardware: Optional[List[str]] = None) -> Tuple[List[str], List[str]]:
        """
        Validate hardware names against available specifications.
        
        Args:
            hardware_names: List of requested hardware names
            available_hardware: List of actually available hardware (from hardware specs)
        
        Returns:
            Tuple of (valid_hardware, warnings)
        """
        if not hardware_names:
            raise AttentionValidationError(
                "At least one hardware specification must be provided",
                field="hardware_names",
                suggestions=[f"Try supported hardware: {', '.join(cls.SUPPORTED_HARDWARE)}"]
            )
        
        if len(hardware_names) > cls.MAX_HARDWARE_PER_REQUEST:
            raise AttentionValidationError(
                f"Too many hardware specifications ({len(hardware_names)}). Maximum: {cls.MAX_HARDWARE_PER_REQUEST}",
                field="hardware_names",
                suggestions=[f"Reduce to {cls.MAX_HARDWARE_PER_REQUEST} or fewer hardware specifications"]
            )
        
        # Use available hardware if provided, otherwise fall back to supported list
        reference_hardware = available_hardware or cls.SUPPORTED_HARDWARE
        
        valid_hardware = []
        warnings = []
        invalid_hardware = []
        
        for hw_name in hardware_names:
            if not hw_name or not hw_name.strip():
                warnings.append("Empty hardware name ignored")
                continue
            
            hw_name = hw_name.strip()
            
            # Exact match first
            if hw_name in reference_hardware:
                valid_hardware.append(hw_name)
                continue
            
            # Case-insensitive match
            hw_lower = hw_name.lower()
            matches = [hw for hw in reference_hardware if hw.lower() == hw_lower]
            if matches:
                valid_hardware.append(matches[0])  # Use the canonical name
                if matches[0] != hw_name:
                    warnings.append(f"Hardware name '{hw_name}' corrected to '{matches[0]}'")
                continue
            
            # Partial match suggestions
            partial_matches = [hw for hw in reference_hardware if hw_lower in hw.lower() or hw.lower() in hw_lower]
            if partial_matches:
                warnings.append(f"Hardware '{hw_name}' not found. Did you mean: {', '.join(partial_matches[:3])}?")
            
            invalid_hardware.append(hw_name)
        
        if invalid_hardware:
            raise AttentionValidationError(
                f"Invalid hardware specifications: {invalid_hardware}",
                field="hardware_names",
                suggestions=[
                    f"Available hardware: {', '.join(reference_hardware)}",
                    "Check spelling and use exact hardware names"
                ]
            )
        
        if not valid_hardware:
            raise AttentionValidationError(
                "No valid hardware specifications provided after filtering",
                field="hardware_names",
                suggestions=[f"Use supported hardware: {', '.join(reference_hardware)}"]
            )
        
        # Remove duplicates while preserving order
        seen = set()
        valid_hardware = [hw for hw in valid_hardware if hw not in seen and not seen.add(hw)]
        
        return valid_hardware, warnings
    
    @classmethod
    def validate_precision(cls, precision: str) -> Tuple[str, List[str]]:
        """
        Validate precision setting with suggestions.
        
        Returns:
            Tuple of (valid_precision, warnings)
        """
        if not precision or not precision.strip():
            raise AttentionValidationError(
                "Precision must be specified",
                field="precision",
                suggestions=[f"Supported precisions: {', '.join(cls.SUPPORTED_PRECISIONS)}"]
            )
        
        precision = precision.strip().lower()
        warnings = []
        
        # Exact match
        if precision in cls.SUPPORTED_PRECISIONS:
            return precision, warnings
        
        # Common aliases
        precision_aliases = {
            "float32": "fp32",
            "float16": "fp16",
            "bfloat16": "bf16",
            "half": "fp16",
            "single": "fp32",
            "int8": "int8"
        }
        
        if precision in precision_aliases:
            canonical = precision_aliases[precision]
            warnings.append(f"Precision '{precision}' mapped to '{canonical}'")
            return canonical, warnings
        
        # Suggest closest match
        suggestions = []
        for supported in cls.SUPPORTED_PRECISIONS:
            if precision in supported or supported in precision:
                suggestions.append(supported)
        
        if suggestions:
            raise AttentionValidationError(
                f"Unsupported precision '{precision}'. Did you mean: {', '.join(suggestions)}?",
                field="precision",
                suggestions=[f"Supported precisions: {', '.join(cls.SUPPORTED_PRECISIONS)}"]
            )
        else:
            raise AttentionValidationError(
                f"Unsupported precision '{precision}'",
                field="precision",
                suggestions=[f"Supported precisions: {', '.join(cls.SUPPORTED_PRECISIONS)}"]
            )
    
    @classmethod
    def validate_batch_size(cls, batch_size: int) -> Tuple[int, List[str]]:
        """
        Validate batch size with performance warnings.
        
        Returns:
            Tuple of (valid_batch_size, warnings)
        """
        warnings = []
        
        if not isinstance(batch_size, int):
            try:
                batch_size = int(batch_size)
            except (ValueError, TypeError):
                raise AttentionValidationError(
                    f"Batch size must be an integer, got: {type(batch_size).__name__}",
                    field="batch_size",
                    suggestions=["Use integer values like 1, 2, 4, 8, 16"]
                )
        
        if batch_size < cls.MIN_BATCH_SIZE:
            raise AttentionValidationError(
                f"Batch size must be at least {cls.MIN_BATCH_SIZE}",
                field="batch_size",
                suggestions=["Use batch size of 1 or higher"]
            )
        
        if batch_size > cls.MAX_BATCH_SIZE:
            raise AttentionValidationError(
                f"Batch size {batch_size} exceeds maximum ({cls.MAX_BATCH_SIZE})",
                field="batch_size",
                suggestions=[
                    f"Use batch size of {cls.MAX_BATCH_SIZE} or lower",
                    "Large batch sizes may cause memory issues"
                ]
            )
        
        # Performance warnings
        if batch_size > 32:
            warnings.append(f"Large batch size ({batch_size}) may require significant memory")
        
        if batch_size & (batch_size - 1) != 0:  # Not a power of 2
            warnings.append(f"Batch size {batch_size} is not a power of 2, which may be less efficient")
        
        return batch_size, warnings
    
    @classmethod
    def validate_complete_request(cls, request_data: Dict[str, Any], available_hardware: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Perform comprehensive validation of an attention analysis request.
        
        Args:
            request_data: Raw request data
            available_hardware: List of available hardware from hardware specs
        
        Returns:
            Dict with validated data and warnings
        """
        validated = {}
        all_warnings = []
        
        try:
            # Validate model names
            model_names = request_data.get("model_names", [])
            validated["model_names"], warnings = cls.validate_model_names(model_names)
            all_warnings.extend(warnings)
            
            # Validate sequence lengths
            sequence_lengths = request_data.get("sequence_lengths", [8192, 32768])
            validated["sequence_lengths"], warnings = cls.validate_sequence_lengths(sequence_lengths)
            all_warnings.extend(warnings)
            
            # Validate hardware names
            hardware_names = request_data.get("hardware_names", ["H100"])
            validated["hardware_names"], warnings = cls.validate_hardware_names(hardware_names, available_hardware)
            all_warnings.extend(warnings)
            
            # Validate precision
            precision = request_data.get("precision", "bf16")
            validated["precision"], warnings = cls.validate_precision(precision)
            all_warnings.extend(warnings)
            
            # Validate batch size
            batch_size = request_data.get("batch_size", 1)
            validated["batch_size"], warnings = cls.validate_batch_size(batch_size)
            all_warnings.extend(warnings)
            
            # Validate flash attention (simple boolean)
            validated["flash_attention"] = bool(request_data.get("flash_attention", True))
            
            # Performance warnings based on combinations
            if len(validated["model_names"]) * len(validated["sequence_lengths"]) * len(validated["hardware_names"]) > 100:
                all_warnings.append("Large analysis matrix may take significant time to compute")
            
            return {
                "validated_data": validated,
                "warnings": all_warnings,
                "success": True
            }
            
        except AttentionValidationError as e:
            logger.error(f"Validation error: {e.message}")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": e.message,
                    "field": e.field,
                    "suggestions": e.suggestions,
                    "error_type": "validation_error"
                }
            )
        except Exception as e:
            logger.error(f"Unexpected validation error: {e}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Internal validation error occurred",
                    "error_type": "internal_error",
                    "suggestions": ["Please try again or contact support if the issue persists"]
                }
            )


def create_user_friendly_error_response(error: Exception, context: str = "") -> Dict[str, Any]:
    """
    Create a user-friendly error response with helpful guidance.
    
    Args:
        error: The exception that occurred
        context: Additional context about where the error occurred
    
    Returns:
        Dict with error details and user guidance
    """
    if isinstance(error, AttentionValidationError):
        return {
            "error": error.message,
            "field": error.field,
            "suggestions": error.suggestions,
            "error_type": "validation_error",
            "context": context
        }
    elif isinstance(error, ValidationError):
        # Handle Pydantic validation errors
        errors = []
        suggestions = []
        
        for err in error.errors():
            field = ".".join(str(loc) for loc in err["loc"])
            message = err["msg"]
            errors.append(f"{field}: {message}")
            
            # Add specific suggestions based on error type
            if "missing" in message.lower():
                suggestions.append(f"Provide a value for {field}")
            elif "type" in message.lower():
                suggestions.append(f"Check the data type for {field}")
            elif "value" in message.lower():
                suggestions.append(f"Check the value range for {field}")
        
        return {
            "error": f"Request validation failed: {'; '.join(errors)}",
            "field": None,
            "suggestions": suggestions,
            "error_type": "pydantic_validation_error",
            "context": context
        }
    elif isinstance(error, HTTPException):
        return {
            "error": error.detail,
            "field": None,
            "suggestions": ["Check the API documentation for correct request format"],
            "error_type": "http_error",
            "context": context
        }
    else:
        return {
            "error": f"An unexpected error occurred: {str(error)}",
            "field": None,
            "suggestions": [
                "Please try again",
                "If the issue persists, contact support",
                "Check that all required parameters are provided"
            ],
            "error_type": "unexpected_error",
            "context": context
        }
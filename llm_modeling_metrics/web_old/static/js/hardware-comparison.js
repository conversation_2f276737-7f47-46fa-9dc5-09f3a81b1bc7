/**
 * Multi-Hardware Comparison Interface
 * Provides side-by-side hardware comparison functionality
 */

class HardwareComparison {
    constructor() {
        this.selectedHardware = new Set();
        this.comparisonData = null;
        this.charts = {};

        this.initializeEventListeners();
        this.loadAvailableHardware();
    }

    initializeEventListeners() {
        // Hardware selection events
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('hardware-checkbox')) {
                this.handleHardwareSelection(e.target);
            }
        });

        // Compare button
        const compareBtn = document.getElementById('compare-hardware-btn');
        if (compareBtn) {
            compareBtn.addEventListener('click', () => this.performComparison());
        }

        // Clear selection button
        const clearBtn = document.getElementById('clear-selection-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearSelection());
        }

        // Export results button
        const exportBtn = document.getElementById('export-comparison-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportResults());
        }

        // Show timing analysis button
        const showTimingBtn = document.getElementById('show-timing-analysis-btn');
        if (showTimingBtn) {
            showTimingBtn.addEventListener('click', () => this.showTimingAnalysis());
        }
    }

    async loadAvailableHardware() {
        try {
            const response = await fetch('/api/hardware/list');
            const data = await response.json();

            this.renderHardwareSelector(data);
        } catch (error) {
            console.error('Error loading hardware:', error);
            this.showError('Failed to load available hardware');
        }
    }

    renderHardwareSelector(hardwareData) {
        const container = document.getElementById('hardware-selector-container');
        if (!container) return;

        const html = `
            <div class="hardware-categories">
                <div class="hardware-category">
                    <h3>GPUs</h3>
                    <div class="hardware-grid">
                        ${hardwareData.gpu.map(gpu => this.renderHardwareCard(gpu)).join('')}
                    </div>
                </div>
                <div class="hardware-category">
                    <h3>NPUs</h3>
                    <div class="hardware-grid">
                        ${hardwareData.npu.map(npu => this.renderHardwareCard(npu)).join('')}
                    </div>
                </div>
            </div>
            <div class="selection-controls">
                <button id="compare-hardware-btn" class="btn btn-primary" disabled>
                    Compare Selected Hardware
                </button>
                <button id="clear-selection-btn" class="btn btn-secondary">
                    Clear Selection
                </button>
                <span class="selection-count">0 hardware selected</span>
            </div>
        `;

        container.innerHTML = html;
    }

    renderHardwareCard(hardware) {
        return `
            <div class="hardware-card" data-hardware-id="${hardware.id}">
                <div class="hardware-card-header">
                    <input type="checkbox" class="hardware-checkbox"
                           id="hw-${hardware.id}" value="${hardware.id}">
                    <label for="hw-${hardware.id}" class="hardware-name">${hardware.name}</label>
                </div>
                <div class="hardware-specs">
                    <div class="spec-item">
                        <span class="spec-label">Memory:</span>
                        <span class="spec-value">${hardware.memory_size_gb} GB</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Bandwidth:</span>
                        <span class="spec-value">${hardware.memory_bandwidth_gbps} GB/s</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Peak Performance:</span>
                        <span class="spec-value">${this.formatPeakPerformance(hardware.peak_flops)} TFLOPS</span>
                    </div>
                    <div class="spec-item">
                        <span class="spec-label">Architecture:</span>
                        <span class="spec-value">${hardware.architecture}</span>
                    </div>
                </div>
            </div>
        `;
    }

    formatPeakPerformance(peakFlops) {
        if (!peakFlops || Object.keys(peakFlops).length === 0) return 'N/A';
        const maxPerf = Math.max(...Object.values(peakFlops));
        return maxPerf.toFixed(0);
    }

    handleHardwareSelection(checkbox) {
        const hardwareId = checkbox.value;

        if (checkbox.checked) {
            this.selectedHardware.add(hardwareId);
        } else {
            this.selectedHardware.delete(hardwareId);
        }

        this.updateSelectionUI();
    }

    updateSelectionUI() {
        const count = this.selectedHardware.size;
        const compareBtn = document.getElementById('compare-hardware-btn');
        const countSpan = document.querySelector('.selection-count');

        if (countSpan) {
            countSpan.textContent = `${count} hardware selected`;
        }

        if (compareBtn) {
            compareBtn.disabled = count < 2;
        }

        // Update card styling
        document.querySelectorAll('.hardware-card').forEach(card => {
            const hardwareId = card.dataset.hardwareId;
            if (this.selectedHardware.has(hardwareId)) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });
    }

    clearSelection() {
        this.selectedHardware.clear();
        document.querySelectorAll('.hardware-checkbox').forEach(cb => {
            cb.checked = false;
        });
        this.updateSelectionUI();
        this.clearResults();
    }

    async performComparison() {
        if (this.selectedHardware.size < 2) {
            this.showError('Please select at least 2 hardware platforms for comparison');
            return;
        }

        this.showLoading(true);

        try {
            const request = {
                hardware_ids: Array.from(this.selectedHardware),
                include_cost_analysis: true,
                workload_profiles: this.getWorkloadProfiles(),
                operators: this.getSelectedOperators()
            };

            const response = await fetch('/api/hardware/compare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(request)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            this.comparisonData = await response.json();
            this.renderComparisonResults();

        } catch (error) {
            console.error('Error performing comparison:', error);
            this.showError('Failed to perform hardware comparison: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    getWorkloadProfiles() {
        // Get workload profiles from UI if available
        const profiles = [];

        // Check for workload configuration in the UI
        const workloadConfig = document.getElementById('workload-config');
        if (workloadConfig && workloadConfig.style.display !== 'none') {
            const modelType = document.getElementById('model-type')?.value || 'dense';
            const batchSize = parseInt(document.getElementById('batch-size')?.value) || 1;
            const sequenceLength = parseInt(document.getElementById('sequence-length')?.value) || 2048;
            const precisions = this.getSelectedPrecisions();

            profiles.push({
                model_type: modelType,
                batch_size: batchSize,
                sequence_length: sequenceLength,
                precision_requirements: precisions,
                memory_constraints: null,
                latency_requirements: null,
                throughput_requirements: null
            });
        }

        return profiles.length > 0 ? profiles : null;
    }

    getSelectedOperators() {
        // Get operators from the current analysis if available
        const operators = [];

        // Check if there are operators from a previous analysis
        if (window.currentAnalysisData && window.currentAnalysisData.operators) {
            return window.currentAnalysisData.operators;
        }

        return operators.length > 0 ? operators : null;
    }

    getSelectedPrecisions() {
        const precisions = [];
        document.querySelectorAll('input[name="precision"]:checked').forEach(input => {
            precisions.push(input.value);
        });
        return precisions.length > 0 ? precisions : ['fp16'];
    }

    renderComparisonResults() {
        const container = document.getElementById('comparison-results');
        if (!container) return;

        const html = `
            <div class="comparison-header">
                <h2>Hardware Comparison Results</h2>
                <div class="comparison-actions">
                    <button id="show-timing-analysis-btn" class="btn btn-primary me-2">
                        <i class="fas fa-stopwatch me-1"></i>
                        Detailed Timing Analysis
                    </button>
                    <button id="export-comparison-btn" class="btn btn-secondary">
                        <i class="fas fa-download me-1"></i>
                        Export Results
                    </button>
                </div>
            </div>

            <div class="comparison-content">
                ${this.renderSummaryInsights()}
                ${this.renderComparisonTable()}
                ${this.renderPerformanceCharts()}
                ${this.renderRecommendations()}
                ${this.renderRooflineComparison()}
            </div>
        `;

        container.innerHTML = html;
        container.style.display = 'block';

        // Initialize charts
        this.initializeCharts();
    }

    renderSummaryInsights() {
        if (!this.comparisonData.summary_insights) return '';

        return `
            <div class="summary-insights">
                <h3>Key Insights</h3>
                <ul class="insights-list">
                    ${this.comparisonData.summary_insights.map(insight =>
                        `<li class="insight-item">${insight}</li>`
                    ).join('')}
                </ul>
            </div>
        `;
    }

    renderComparisonTable() {
        const metrics = this.comparisonData.comparison_metrics;
        const hardwareIds = this.comparisonData.hardware_platforms;

        return `
            <div class="comparison-table-container">
                <h3>Hardware Specifications Comparison</h3>
                <div class="table-responsive">
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Specification</th>
                                ${hardwareIds.map(id =>
                                    `<th class="hardware-column">${metrics[id].hardware_name}</th>`
                                ).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="spec-name">Peak Performance (TFLOPS)</td>
                                ${hardwareIds.map(id =>
                                    `<td class="spec-value">${metrics[id].peak_performance_tflops.toFixed(0)}</td>`
                                ).join('')}
                            </tr>
                            <tr>
                                <td class="spec-name">Memory Size (GB)</td>
                                ${hardwareIds.map(id =>
                                    `<td class="spec-value">${metrics[id].memory_size_gb}</td>`
                                ).join('')}
                            </tr>
                            <tr>
                                <td class="spec-name">Memory Bandwidth (GB/s)</td>
                                ${hardwareIds.map(id =>
                                    `<td class="spec-value">${metrics[id].memory_bandwidth_gbps.toFixed(0)}</td>`
                                ).join('')}
                            </tr>
                            <tr>
                                <td class="spec-name">Tensor Cores</td>
                                ${hardwareIds.map(id =>
                                    `<td class="spec-value">${metrics[id].tensor_core_capable ? 'Yes' : 'No'}</td>`
                                ).join('')}
                            </tr>
                            <tr>
                                <td class="spec-name">Power Efficiency (TFLOPS/W)</td>
                                ${hardwareIds.map(id =>
                                    `<td class="spec-value">${metrics[id].power_efficiency ? metrics[id].power_efficiency.toFixed(2) : 'N/A'}</td>`
                                ).join('')}
                            </tr>
                            <tr>
                                <td class="spec-name">Cost/Performance ($/TFLOP)</td>
                                ${hardwareIds.map(id =>
                                    `<td class="spec-value">${metrics[id].cost_performance_ratio ? '$' + metrics[id].cost_performance_ratio.toFixed(0) : 'N/A'}</td>`
                                ).join('')}
                            </tr>
                            <tr>
                                <td class="spec-name">Supported Precisions</td>
                                ${hardwareIds.map(id =>
                                    `<td class="spec-value">${metrics[id].supported_precisions.join(', ')}</td>`
                                ).join('')}
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    renderPerformanceCharts() {
        return `
            <div class="performance-charts">
                <h3>Performance Comparison</h3>
                <div class="charts-grid">
                    <div class="chart-container">
                        <canvas id="performance-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="memory-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="efficiency-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="cost-chart"></canvas>
                    </div>
                </div>
            </div>
        `;
    }

    renderRecommendations() {
        const recommendations = this.comparisonData.recommendation_engine;

        return `
            <div class="recommendations-section">
                <h3>Hardware Recommendations</h3>

                <div class="recommended-hardware">
                    <h4>Recommended Order</h4>
                    <ol class="recommendation-list">
                        ${recommendations.recommended_hardware.map((hwId, index) => {
                            const metrics = this.comparisonData.comparison_metrics[hwId];
                            const reasons = recommendations.recommendation_reasons[hwId] || [];
                            return `
                                <li class="recommendation-item">
                                    <div class="recommendation-header">
                                        <span class="hardware-name">${metrics.hardware_name}</span>
                                        <span class="recommendation-rank">#${index + 1}</span>
                                    </div>
                                    <div class="recommendation-reasons">
                                        ${reasons.map(reason => `<span class="reason-tag">${reason}</span>`).join('')}
                                    </div>
                                </li>
                            `;
                        }).join('')}
                    </ol>
                </div>

                <div class="use-case-suitability">
                    <h4>Use Case Suitability</h4>
                    <div class="suitability-grid">
                        ${this.renderUseCaseSuitability(recommendations.use_case_suitability)}
                    </div>
                </div>

                <div class="migration-recommendations">
                    <h4>Migration Recommendations</h4>
                    <ul class="migration-list">
                        ${recommendations.migration_recommendations.map(rec =>
                            `<li class="migration-item">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    renderUseCaseSuitability(suitability) {
        const useCases = ['training', 'inference', 'research', 'production'];
        const hardwareIds = this.comparisonData.hardware_platforms;

        return `
            <table class="suitability-table">
                <thead>
                    <tr>
                        <th>Hardware</th>
                        ${useCases.map(useCase => `<th>${useCase.charAt(0).toUpperCase() + useCase.slice(1)}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${hardwareIds.map(hwId => {
                        const metrics = this.comparisonData.comparison_metrics[hwId];
                        return `
                            <tr>
                                <td class="hardware-name">${metrics.hardware_name}</td>
                                ${useCases.map(useCase => {
                                    const score = suitability[hwId][useCase];
                                    const className = this.getSuitabilityClass(score);
                                    return `<td class="suitability-score ${className}">${score.toFixed(0)}%</td>`;
                                }).join('')}
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;
    }

    getSuitabilityClass(score) {
        if (score >= 80) return 'excellent';
        if (score >= 60) return 'good';
        if (score >= 40) return 'fair';
        return 'poor';
    }

    renderRooflineComparison() {
        if (!this.comparisonData.roofline_comparison) return '';

        return `
            <div class="roofline-comparison">
                <h3>Roofline Comparison</h3>
                <div class="roofline-container">
                    <canvas id="roofline-comparison-chart"></canvas>
                </div>
                <div class="roofline-controls">
                    <label for="precision-select">Precision:</label>
                    <select id="precision-select">
                        <option value="fp32">FP32</option>
                        <option value="fp16" selected>FP16</option>
                        <option value="bf16">BF16</option>
                        <option value="fp8">FP8</option>
                        <option value="int8">INT8</option>
                    </select>
                </div>
            </div>
        `;
    }

    initializeCharts() {
        this.initializePerformanceChart();
        this.initializeMemoryChart();
        this.initializeEfficiencyChart();
        this.initializeCostChart();
        this.initializeRooflineChart();
    }

    initializePerformanceChart() {
        const ctx = document.getElementById('performance-chart');
        if (!ctx) return;

        const metrics = this.comparisonData.comparison_metrics;
        const hardwareIds = this.comparisonData.hardware_platforms;

        const data = {
            labels: hardwareIds.map(id => metrics[id].hardware_name),
            datasets: [{
                label: 'Peak Performance (TFLOPS)',
                data: hardwareIds.map(id => metrics[id].peak_performance_tflops),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };

        this.charts.performance = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Peak Performance Comparison'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'TFLOPS'
                        }
                    }
                }
            }
        });
    }

    initializeMemoryChart() {
        const ctx = document.getElementById('memory-chart');
        if (!ctx) return;

        const metrics = this.comparisonData.comparison_metrics;
        const hardwareIds = this.comparisonData.hardware_platforms;

        const data = {
            labels: hardwareIds.map(id => metrics[id].hardware_name),
            datasets: [
                {
                    label: 'Memory Size (GB)',
                    data: hardwareIds.map(id => metrics[id].memory_size_gb),
                    backgroundColor: 'rgba(255, 99, 132, 0.8)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1,
                    yAxisID: 'y'
                },
                {
                    label: 'Memory Bandwidth (GB/s)',
                    data: hardwareIds.map(id => metrics[id].memory_bandwidth_gbps),
                    backgroundColor: 'rgba(75, 192, 192, 0.8)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1,
                    yAxisID: 'y1'
                }
            ]
        };

        this.charts.memory = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Memory Specifications'
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Memory Size (GB)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Bandwidth (GB/s)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    initializeEfficiencyChart() {
        const ctx = document.getElementById('efficiency-chart');
        if (!ctx) return;

        const metrics = this.comparisonData.comparison_metrics;
        const hardwareIds = this.comparisonData.hardware_platforms;

        const efficiencyData = hardwareIds
            .filter(id => metrics[id].power_efficiency)
            .map(id => ({
                name: metrics[id].hardware_name,
                efficiency: metrics[id].power_efficiency
            }));

        if (efficiencyData.length === 0) return;

        const data = {
            labels: efficiencyData.map(d => d.name),
            datasets: [{
                label: 'Power Efficiency (TFLOPS/W)',
                data: efficiencyData.map(d => d.efficiency),
                backgroundColor: 'rgba(153, 102, 255, 0.8)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1
            }]
        };

        this.charts.efficiency = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Power Efficiency Comparison'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'TFLOPS/Watt'
                        }
                    }
                }
            }
        });
    }

    initializeCostChart() {
        const ctx = document.getElementById('cost-chart');
        if (!ctx) return;

        const metrics = this.comparisonData.comparison_metrics;
        const hardwareIds = this.comparisonData.hardware_platforms;

        const costData = hardwareIds
            .filter(id => metrics[id].cost_performance_ratio)
            .map(id => ({
                name: metrics[id].hardware_name,
                ratio: metrics[id].cost_performance_ratio
            }));

        if (costData.length === 0) return;

        const data = {
            labels: costData.map(d => d.name),
            datasets: [{
                label: 'Cost per TFLOP ($)',
                data: costData.map(d => d.ratio),
                backgroundColor: 'rgba(255, 159, 64, 0.8)',
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 1
            }]
        };

        this.charts.cost = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Cost-Performance Ratio (Lower is Better)'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '$ per TFLOP'
                        }
                    }
                }
            }
        });
    }

    initializeRooflineChart() {
        const ctx = document.getElementById('roofline-comparison-chart');
        if (!ctx) return;

        // Initialize roofline comparison chart
        // This would integrate with the existing roofline visualizer
        if (window.RooflineVisualizer) {
            this.rooflineVisualizer = new window.RooflineVisualizer(ctx);
            this.rooflineVisualizer.plotComparison(this.comparisonData.roofline_comparison);
        }
    }

    exportResults() {
        if (!this.comparisonData) {
            this.showError('No comparison data to export');
            return;
        }

        const exportData = {
            timestamp: new Date().toISOString(),
            hardware_platforms: this.comparisonData.hardware_platforms,
            comparison_metrics: this.comparisonData.comparison_metrics,
            summary_insights: this.comparisonData.summary_insights,
            recommendations: this.comparisonData.recommendation_engine
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `hardware-comparison-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    clearResults() {
        const container = document.getElementById('comparison-results');
        if (container) {
            container.style.display = 'none';
            container.innerHTML = '';
        }

        // Clear charts
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};

        this.comparisonData = null;
    }

    showTimingAnalysis() {
        if (!this.comparisonData) {
            this.showError('No comparison data available for timing analysis');
            return;
        }

        // Show the cross-platform timing container
        const timingContainer = document.getElementById('cross-platform-timing-container');
        if (timingContainer) {
            timingContainer.style.display = 'block';

            // Populate hardware selector for timing analysis
            this.populateTimingHardwareSelector();

            // Scroll to timing analysis section
            timingContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }

    populateTimingHardwareSelector() {
        const container = document.getElementById('timing-hardware-selector');
        if (!container || !this.comparisonData) return;

        const hardwareMetrics = this.comparisonData.comparison_metrics;

        const html = `
            <div class="timing-hardware-selection">
                ${Object.entries(hardwareMetrics).map(([hwId, metrics]) => `
                    <div class="form-check">
                        <input class="form-check-input timing-hardware-checkbox"
                               type="checkbox" value="${hwId}" id="timing-hw-${hwId}"
                               ${this.selectedHardware.has(hwId) ? 'checked' : ''}>
                        <label class="form-check-label" for="timing-hw-${hwId}">
                            ${metrics.hardware_name}
                            <small class="text-muted d-block">
                                ${metrics.peak_performance_tflops.toFixed(0)} TFLOPS,
                                ${metrics.memory_size_gb} GB
                            </small>
                        </label>
                    </div>
                `).join('')}
            </div>
        `;

        container.innerHTML = html;

        // Initialize cross-platform timing if not already done
        if (!window.crossPlatformTiming) {
            window.crossPlatformTiming = new CrossPlatformTiming();
            window.crossPlatformTiming.loadOperatorsFromAnalysis();
        }

        // Update selected hardware in timing analysis
        if (window.crossPlatformTiming) {
            window.crossPlatformTiming.selectedHardware = new Set(this.selectedHardware);
            window.crossPlatformTiming.updateTimingAnalysisUI();
        }
    }

    showLoading(show) {
        const loader = document.getElementById('comparison-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }

        const compareBtn = document.getElementById('compare-hardware-btn');
        if (compareBtn) {
            compareBtn.disabled = show;
            compareBtn.textContent = show ? 'Comparing...' : 'Compare Selected Hardware';
        }
    }

    showError(message) {
        const errorContainer = document.getElementById('error-container');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-error">
                    <span class="error-message">${message}</span>
                    <button class="close-btn" onclick="this.parentElement.style.display='none'">&times;</button>
                </div>
            `;
            errorContainer.style.display = 'block';
        } else {
            alert(message);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('hardware-comparison-container')) {
        window.hardwareComparison = new HardwareComparison();
    }
});

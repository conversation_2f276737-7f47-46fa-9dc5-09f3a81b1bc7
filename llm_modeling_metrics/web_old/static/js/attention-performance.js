/**
 * Attention Performance Visualization Component for LLM Modeling Metrics Dashboard
 *
 * This component provides interactive attention performance visualization with:
 * - Memory access (GB) vs compute (GFLOPS) plot using Chart.js
 * - Hardware roofline curves for different GPU platforms
 * - Model trajectory lines showing performance from 8K to 32K context
 * - Interactive controls for sequence length, FlashAttention, and model filtering
 */

class AttentionPerformanceVisualizer {
    constructor(containerId, options = {}) {
        console.log('AttentionPerformanceVisualizer v1.0 - Initializing');
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            apiBaseUrl: '/api',
            showControls: true,
            showLegend: true,
            enableZoom: true,
            enablePan: true,
            onModelClick: null,
            onParameterChange: null,
            ...options
        };

        // State
        this.chart = null;
        this.attentionData = null;
        this.selectedHardware = [];
        this.selectedModels = [];
        this.sequenceLengthRange = { min: 8192, max: 32768 };
        this.flashAttentionEnabled = true;
        this.selectedAttentionTypes = ['MHA', 'GQA', 'MLA'];
        this.isLoading = false;

        this.enableWheelZoom = false;

        // Real-time update state
        this.updateDebounceTimer = null;
        this.updateDebounceDelay = 500; // 500ms debounce delay
        this.savedZoomState = null;
        this.autoUpdateEnabled = true;
        this.pendingUpdates = {
            models: false,
            hardware: false,
            parameters: false
        };

        // Initialize component
        this.init();
    }

    getChartConfig() {
        return {
            type: 'scatter',
            data: {
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'linear',
                        position: 'bottom',
                        title: {
                            display: true,
                            text: 'Memory Access (GB)',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        min: 0,
                        max: this.getXAxisMax(),
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y: {
                        type: 'linear',
                        title: {
                            display: true,
                            text: 'Compute (GFLOPS)',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        min: 0,
                        max: this.getYAxisMax(),
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        borderWidth: 1,
                        callbacks: {
                            title: (context) => {
                                const point = context[0];
                                if (point.dataset.datasetType === 'roofline') {
                                    return `${point.dataset.label} Hardware Limit`;
                                } else if (point.dataset.datasetType === 'model') {
                                    return point.dataset.modelName || 'Model';
                                }
                                return '';
                            },
                            label: (context) => {
                                const point = context.parsed;
                                if (context.dataset.datasetType === 'roofline') {
                                    return [
                                        `Memory Access: ${point.x.toFixed(2)} GB`,
                                        `Compute: ${point.y.toFixed(2)} GFLOPS`,
                                        `Hardware: ${context.dataset.hardwareName || 'Unknown'}`
                                    ];
                                } else if (context.dataset.datasetType === 'model') {
                                    const dataset = context.dataset;
                                    const dataPoint = dataset.rawData ? dataset.rawData[context.dataIndex] : null;
                                    return [
                                        `Memory Access: ${point.x.toFixed(2)} GB`,
                                        `Compute: ${point.y.toFixed(2)} GFLOPS`,
                                        `Model: ${dataset.modelName || 'Unknown'}`,
                                        `Attention: ${dataset.attentionType || 'Unknown'}`,
                                        `Sequence Length: ${dataPoint ? dataPoint.sequence_length : 'N/A'}`,
                                        `FlashAttention: ${dataset.flashAttention ? 'Enabled' : 'Disabled'}`,
                                        `Operational Intensity: ${dataPoint ? dataPoint.operational_intensity.toFixed(3) : 'N/A'} FLOP/Byte`
                                    ];
                                }
                                return '';
                            }
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 11
                            },
                            filter: (legendItem, chartData) => {
                                // Only show legend for rooflines and model categories
                                return legendItem.text !== '';
                            }
                        }
                    },
                    zoom: this.options.enableZoom ? {
                        zoom: {
                            wheel: {
                                enabled: this.enableWheelZoom,
                            },
                            pinch: {
                                enabled: true
                            },
                            mode: 'xy',
                        },
                        pan: {
                            enabled: this.options.enablePan,
                            mode: 'xy',
                        }
                    } : undefined
                },
                interaction: {
                    intersect: false,
                    mode: 'point'
                },
                onClick: (event, elements) => {
                    if (elements.length > 0 && this.options.onModelClick) {
                        const element = elements[0];
                        const dataset = this.chart.data.datasets[element.datasetIndex];
                        if (dataset.datasetType === 'model') {
                            this.options.onModelClick(dataset, element.index);
                        }
                    }
                }
            }
        };
    }

    getXAxisMax() {
        if (!this.attentionData || !this.attentionData.model_trajectories) {
            return 50; // Default fallback
        }

        let maxMemoryAccess = 0;
        Object.values(this.attentionData.model_trajectories).forEach(trajectory => {
            trajectory.forEach(point => {
                if (point.memory_access_gb > maxMemoryAccess) {
                    maxMemoryAccess = point.memory_access_gb;
                }
            });
        });

        return Math.max(maxMemoryAccess * 1.2, 50);
    }

    getYAxisMax() {
        if (!this.attentionData || !this.attentionData.model_trajectories) {
            return 1000; // Default fallback
        }

        let maxCompute = 0;
        Object.values(this.attentionData.model_trajectories).forEach(trajectory => {
            trajectory.forEach(point => {
                if (point.compute_gflops > maxCompute) {
                    maxCompute = point.compute_gflops;
                }
            });
        });

        return Math.max(maxCompute * 1.2, 1000);
    }

    async init() {
        try {
            console.log('Initializing Attention Performance Visualizer...');
            this.render();
            this.bindEvents();
            console.log('Attention Performance Visualizer initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Attention Performance Visualizer:', error);
            this.showError('Failed to initialize attention performance visualizer. Please refresh the page.');
        }
    }

    render() {
        if (!this.container) {
            console.error(`Container with ID '${this.containerId}' not found`);
            return;
        }

        this.container.innerHTML = this.getAttentionVisualizerHTML();
        this.initializeChart();
    }

    getAttentionVisualizerHTML() {
        return `
            <style>
                /* Enhanced Attention Performance Controls Styles */
                .attention-performance-visualizer-component .dual-range-slider {
                    position: relative;
                    height: 20px;
                    margin: 10px 0;
                }
                
                .attention-performance-visualizer-component .dual-range-slider input[type="range"] {
                    position: absolute;
                    width: 100%;
                    height: 20px;
                    background: transparent;
                    pointer-events: none;
                    -webkit-appearance: none;
                }
                
                .attention-performance-visualizer-component .dual-range-slider input[type="range"]::-webkit-slider-thumb {
                    -webkit-appearance: none;
                    height: 20px;
                    width: 20px;
                    border-radius: 50%;
                    background: #0d6efd;
                    cursor: pointer;
                    pointer-events: all;
                    border: 2px solid #fff;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                }
                
                .attention-performance-visualizer-component .dual-range-slider input[type="range"]::-moz-range-thumb {
                    height: 20px;
                    width: 20px;
                    border-radius: 50%;
                    background: #0d6efd;
                    cursor: pointer;
                    pointer-events: all;
                    border: 2px solid #fff;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                }
                
                .attention-performance-visualizer-component .slider-track {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 100%;
                    height: 4px;
                    background: #e9ecef;
                    border-radius: 2px;
                }
                
                .attention-performance-visualizer-component .slider-range {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    height: 4px;
                    background: #0d6efd;
                    border-radius: 2px;
                }
                
                .attention-performance-visualizer-component .attention-type-color {
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    margin-right: 8px;
                    border: 1px solid rgba(0,0,0,0.1);
                }
                
                .attention-performance-visualizer-component .attention-type-checkboxes .form-check {
                    margin-bottom: 8px;
                }
                
                .attention-performance-visualizer-component .attention-filter-actions {
                    margin-top: 8px;
                }
                
                .attention-performance-visualizer-component #flashAttentionIcon {
                    color: #ffc107;
                    transition: color 0.3s ease;
                }
                
                .attention-performance-visualizer-component #enableFlashAttention:checked + label #flashAttentionIcon {
                    color: #28a745;
                }
                
                .attention-performance-visualizer-component #flashAttentionBadge {
                    transition: all 0.3s ease;
                }
            </style>
            <div class="attention-performance-visualizer-component">
                <!-- Controls Panel -->
                ${this.options.showControls ? `
                    <div class="attention-controls mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-brain me-1"></i>
                                    Attention Performance Controls
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <!-- Sequence Length Range -->
                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <i class="fas fa-arrows-alt-h me-1"></i>
                                            Sequence Length Range (K tokens)
                                        </label>
                                        <!-- Dual Range Slider -->
                                        <div class="sequence-length-slider-container mb-2">
                                            <div class="dual-range-slider">
                                                <input type="range" class="form-range" 
                                                       id="attentionMinSeqLengthSlider" 
                                                       min="1" max="64" step="1" value="8">
                                                <input type="range" class="form-range" 
                                                       id="attentionMaxSeqLengthSlider" 
                                                       min="1" max="64" step="1" value="32">
                                                <div class="slider-track"></div>
                                                <div class="slider-range" id="attentionSliderRange"></div>
                                            </div>
                                        </div>
                                        <!-- Number Inputs for Precise Control -->
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <input type="number" class="form-control form-control-sm"
                                                       id="attentionMinSeqLength" value="8"
                                                       min="1" max="64" step="1">
                                                <div class="form-text">Min (K)</div>
                                            </div>
                                            <div class="col-6">
                                                <input type="number" class="form-control form-control-sm"
                                                       id="attentionMaxSeqLength" value="32"
                                                       min="1" max="64" step="1">
                                                <div class="form-text">Max (K)</div>
                                            </div>
                                        </div>
                                        <!-- Range Display -->
                                        <div class="mt-1">
                                            <small class="text-muted">
                                                <span id="attentionRangeDisplay">8K - 32K tokens</span>
                                                <span class="ms-2" id="attentionDataPointsEstimate">(~25 points)</span>
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Attention Type Filter -->
                                    <div class="col-md-4">
                                        <label for="attentionTypeFilter" class="form-label">
                                            <i class="fas fa-filter me-1"></i>
                                            Attention Mechanisms
                                            <span class="badge bg-primary ms-1" id="attentionTypeCount">3 selected</span>
                                        </label>
                                        
                                        <!-- Checkbox-based filter for better UX -->
                                        <div class="attention-type-checkboxes mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input attention-type-checkbox" 
                                                       type="checkbox" value="MHA" id="attentionTypeMHA" checked>
                                                <label class="form-check-label" for="attentionTypeMHA">
                                                    <span class="attention-type-color" style="background-color: #FF6384;"></span>
                                                    Multi-Head Attention (MHA)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input attention-type-checkbox" 
                                                       type="checkbox" value="GQA" id="attentionTypeGQA" checked>
                                                <label class="form-check-label" for="attentionTypeGQA">
                                                    <span class="attention-type-color" style="background-color: #36A2EB;"></span>
                                                    Grouped Query Attention (GQA)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input attention-type-checkbox" 
                                                       type="checkbox" value="MLA" id="attentionTypeMLA" checked>
                                                <label class="form-check-label" for="attentionTypeMLA">
                                                    <span class="attention-type-color" style="background-color: #FFCE56;"></span>
                                                    Multi-Latent Attention (MLA)
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <!-- Quick Actions -->
                                        <div class="attention-filter-actions">
                                            <button type="button" class="btn btn-outline-secondary btn-sm me-1" 
                                                    id="selectAllAttentionTypes">
                                                <i class="fas fa-check-double me-1"></i>All
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                    id="clearAllAttentionTypes">
                                                <i class="fas fa-times me-1"></i>None
                                            </button>
                                        </div>
                                        
                                        <div class="form-text">
                                            <i class="fas fa-lightbulb me-1"></i>
                                            Colors match chart legend
                                        </div>
                                    </div>

                                    <!-- Display Options -->
                                    <div class="col-md-4">
                                        <label class="form-label">
                                            <i class="fas fa-eye me-1"></i>
                                            Display Options
                                        </label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox"
                                                   id="enableFlashAttention" checked>
                                            <label class="form-check-label" for="enableFlashAttention">
                                                <i class="fas fa-bolt me-1" id="flashAttentionIcon"></i>
                                                <span id="flashAttentionLabel">FlashAttention optimization</span>
                                                <span class="badge bg-success ms-2" id="flashAttentionBadge">Enabled</span>
                                            </label>
                                        </div>
                                        <div class="form-text" id="flashAttentionHelp">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Reduces memory access by ~2-4x for long sequences
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   id="showTrajectoryLines" checked>
                                            <label class="form-check-label" for="showTrajectoryLines">
                                                Show trajectory lines
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   id="enableAttentionWheelZoom">
                                            <label class="form-check-label" for="enableAttentionWheelZoom">
                                                Enable mouse wheel zoom
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   id="enableAttentionAutoUpdate" checked>
                                            <label class="form-check-label" for="enableAttentionAutoUpdate">
                                                <i class="fas fa-sync me-1"></i>
                                                Auto-update visualization
                                            </label>
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Auto-update refreshes the plot when parameters change (debounced 500ms)
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="row g-2 mt-2">
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-primary btn-sm" id="updateAttentionPlotBtn">
                                            <i class="fas fa-sync me-1"></i>
                                            Update Plot
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="resetAttentionZoomBtn">
                                            <i class="fas fa-search-minus me-1"></i>
                                            Reset Zoom
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-info btn-sm" id="exportAttentionPlotBtn">
                                            <i class="fas fa-download me-1"></i>
                                            Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                ` : ''}

                <!-- Chart Container -->
                <div class="attention-chart-container">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-chart-scatter me-1"></i>
                                Attention Performance Analysis (Memory Access vs Compute)
                            </h6>
                            <div class="chart-status">
                                <span id="attentionStatus" class="badge bg-secondary">Ready</span>
                                <span id="attentionUpdateStatus" class="badge bg-info ms-1" style="display: none;">
                                    <i class="fas fa-clock me-1"></i>Update pending...
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-wrapper" style="height: 500px; position: relative;">
                                <canvas id="attentionPerformanceChart"></canvas>

                                <!-- Loading Overlay -->
                                <div id="attentionLoadingOverlay" class="loading-overlay" style="display: none;">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary mb-2" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <div>Analyzing attention performance...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chart Information Panel -->
                <div class="attention-info mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                Analysis Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="attentionInfoContent">
                                <div class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Select models and hardware platforms to generate attention performance analysis.
                                    This visualization shows compute vs memory access characteristics for different attention mechanisms.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    initializeChart() {
        const canvas = document.getElementById('attentionPerformanceChart');
        if (!canvas) {
            console.error('Attention performance chart canvas not found');
            return;
        }

        const ctx = canvas.getContext('2d');

        // Import Chart.js zoom plugin if available
        if (typeof Chart !== 'undefined' && Chart.register && typeof ChartZoom !== 'undefined') {
            Chart.register(ChartZoom.default || ChartZoom);
        }

        this.chart = new Chart(ctx, this.getChartConfig());
        console.log('Attention performance chart initialized');
    }

    bindEvents() {
        // Enhanced sequence length range controls
        this.bindSequenceLengthControls();
        
        // Enhanced attention type filter controls
        this.bindAttentionTypeControls();



        // Enhanced display option checkboxes
        this.bindDisplayOptionControls();



        // Action buttons
        const updateBtn = document.getElementById('updateAttentionPlotBtn');
        const resetZoomBtn = document.getElementById('resetAttentionZoomBtn');
        const exportBtn = document.getElementById('exportAttentionPlotBtn');

        if (updateBtn) {
            updateBtn.addEventListener('click', () => {
                this.updateAttentionPlot();
            });
        }

        if (resetZoomBtn) {
            resetZoomBtn.addEventListener('click', () => {
                this.resetZoom();
            });
        }

        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportChart();
            });
        }
    }

    validateSequenceLengthRange() {
        const min = this.sequenceLengthRange.min;
        const max = this.sequenceLengthRange.max;

        if (min >= max) {
            console.warn('Invalid sequence length range: min >= max');
            return false;
        }

        if (min < 1000 || max > 64000) {
            console.warn('Sequence length range outside recommended bounds (1K-64K)');
            return false;
        }

        return true;
    }

    /**
     * Bind enhanced sequence length controls (sliders + number inputs)
     */
    bindSequenceLengthControls() {
        // Number inputs
        const minSeqLength = document.getElementById('attentionMinSeqLength');
        const maxSeqLength = document.getElementById('attentionMaxSeqLength');
        
        // Range sliders
        const minSeqLengthSlider = document.getElementById('attentionMinSeqLengthSlider');
        const maxSeqLengthSlider = document.getElementById('attentionMaxSeqLengthSlider');

        // Sync number inputs with sliders with debounced updates
        if (minSeqLength && minSeqLengthSlider) {
            minSeqLength.addEventListener('input', () => {
                const value = parseInt(minSeqLength.value);
                minSeqLengthSlider.value = value;
                this.sequenceLengthRange.min = value * 1000;
                this.updateSequenceLengthDisplay();
                this.validateSequenceLengthRange();
                this.onParameterChange();
            });

            minSeqLengthSlider.addEventListener('input', () => {
                const value = parseInt(minSeqLengthSlider.value);
                minSeqLength.value = value;
                this.sequenceLengthRange.min = value * 1000;
                this.updateSequenceLengthDisplay();
                this.updateSliderRange();
                this.validateSequenceLengthRange();
                this.onParameterChange();
            });
        }

        if (maxSeqLength && maxSeqLengthSlider) {
            maxSeqLength.addEventListener('input', () => {
                const value = parseInt(maxSeqLength.value);
                maxSeqLengthSlider.value = value;
                this.sequenceLengthRange.max = value * 1000;
                this.updateSequenceLengthDisplay();
                this.validateSequenceLengthRange();
                this.onParameterChange();
            });

            maxSeqLengthSlider.addEventListener('input', () => {
                const value = parseInt(maxSeqLengthSlider.value);
                maxSeqLength.value = value;
                this.sequenceLengthRange.max = value * 1000;
                this.updateSequenceLengthDisplay();
                this.updateSliderRange();
                this.validateSequenceLengthRange();
                this.onParameterChange();
            });
        }

        // Initialize slider range display
        this.updateSliderRange();
        this.updateSequenceLengthDisplay();
    }

    /**
     * Bind enhanced attention type filter controls
     */
    bindAttentionTypeControls() {
        // Checkbox-based attention type selection
        const checkboxes = document.querySelectorAll('.attention-type-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedAttentionTypes();
                this.updateAttentionTypeCount();
                this.onParameterChange();
            });
        });

        // Select all button
        const selectAllBtn = document.getElementById('selectAllAttentionTypes');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });
                this.updateSelectedAttentionTypes();
                this.updateAttentionTypeCount();
                this.onParameterChange();
            });
        }

        // Clear all button
        const clearAllBtn = document.getElementById('clearAllAttentionTypes');
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', () => {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                this.updateSelectedAttentionTypes();
                this.updateAttentionTypeCount();
                this.onParameterChange();
            });
        }

        // Initialize count
        this.updateAttentionTypeCount();
    }

    /**
     * Bind enhanced display option controls
     */
    bindDisplayOptionControls() {
        // Enhanced FlashAttention toggle
        const flashAttentionToggle = document.getElementById('enableFlashAttention');
        if (flashAttentionToggle) {
            flashAttentionToggle.addEventListener('change', () => {
                this.flashAttentionEnabled = flashAttentionToggle.checked;
                this.updateFlashAttentionVisualFeedback();
                this.onParameterChange();
            });
            
            // Initialize visual feedback
            this.updateFlashAttentionVisualFeedback();
        }

        // Trajectory lines toggle
        const trajectoryLinesToggle = document.getElementById('showTrajectoryLines');
        if (trajectoryLinesToggle) {
            trajectoryLinesToggle.addEventListener('change', () => {
                this.updateDisplayOptions();
            });
        }

        // Wheel zoom toggle
        const enableWheelZoom = document.getElementById('enableAttentionWheelZoom');
        if (enableWheelZoom) {
            enableWheelZoom.addEventListener('change', () => {
                this.enableWheelZoom = enableWheelZoom.checked;
                this.updateZoomSettings();
            });
        }

        // Auto-update toggle
        const enableAutoUpdate = document.getElementById('enableAttentionAutoUpdate');
        if (enableAutoUpdate) {
            enableAutoUpdate.addEventListener('change', () => {
                this.setAutoUpdateEnabled(enableAutoUpdate.checked);
            });
        }
    }

    /**
     * Update slider range visual indicator
     */
    updateSliderRange() {
        const sliderRange = document.getElementById('attentionSliderRange');
        const minSlider = document.getElementById('attentionMinSeqLengthSlider');
        const maxSlider = document.getElementById('attentionMaxSeqLengthSlider');
        
        if (!sliderRange || !minSlider || !maxSlider) return;

        const min = parseInt(minSlider.value);
        const max = parseInt(maxSlider.value);
        const sliderMin = parseInt(minSlider.min);
        const sliderMax = parseInt(minSlider.max);
        
        const leftPercent = ((min - sliderMin) / (sliderMax - sliderMin)) * 100;
        const rightPercent = ((max - sliderMin) / (sliderMax - sliderMin)) * 100;
        
        sliderRange.style.left = leftPercent + '%';
        sliderRange.style.width = (rightPercent - leftPercent) + '%';
    }

    /**
     * Update sequence length display text
     */
    updateSequenceLengthDisplay() {
        const display = document.getElementById('attentionRangeDisplay');
        const estimate = document.getElementById('attentionDataPointsEstimate');
        
        if (display) {
            const minK = Math.round(this.sequenceLengthRange.min / 1000);
            const maxK = Math.round(this.sequenceLengthRange.max / 1000);
            display.textContent = `${minK}K - ${maxK}K tokens`;
        }
        
        if (estimate) {
            const dataPoints = Math.ceil((this.sequenceLengthRange.max - this.sequenceLengthRange.min) / 1000);
            estimate.textContent = `(~${dataPoints} points)`;
        }
    }

    /**
     * Update selected attention types from checkboxes
     */
    updateSelectedAttentionTypes() {
        const checkboxes = document.querySelectorAll('.attention-type-checkbox:checked');
        this.selectedAttentionTypes = Array.from(checkboxes).map(cb => cb.value);
    }

    /**
     * Update attention type count badge
     */
    updateAttentionTypeCount() {
        const countBadge = document.getElementById('attentionTypeCount');
        if (countBadge) {
            const count = this.selectedAttentionTypes.length;
            countBadge.textContent = `${count} selected`;
            countBadge.className = `badge ms-1 ${count > 0 ? 'bg-primary' : 'bg-secondary'}`;
        }
    }

    /**
     * Update FlashAttention visual feedback
     */
    updateFlashAttentionVisualFeedback() {
        const badge = document.getElementById('flashAttentionBadge');
        const icon = document.getElementById('flashAttentionIcon');
        const help = document.getElementById('flashAttentionHelp');
        
        if (badge) {
            if (this.flashAttentionEnabled) {
                badge.textContent = 'Enabled';
                badge.className = 'badge bg-success ms-2';
            } else {
                badge.textContent = 'Disabled';
                badge.className = 'badge bg-secondary ms-2';
            }
        }
        
        if (icon) {
            icon.className = this.flashAttentionEnabled ? 
                'fas fa-bolt me-1 text-success' : 
                'fas fa-bolt me-1 text-warning';
        }
        
        if (help) {
            help.innerHTML = this.flashAttentionEnabled ?
                '<i class="fas fa-info-circle me-1"></i>Reduces memory access by ~2-4x for long sequences' :
                '<i class="fas fa-exclamation-triangle me-1"></i>Standard attention - higher memory usage for long sequences';
        }
    }

    onParameterChange() {
        if (this.options.onParameterChange) {
            this.options.onParameterChange({
                sequenceLengthRange: this.sequenceLengthRange,
                flashAttentionEnabled: this.flashAttentionEnabled,
                selectedAttentionTypes: this.selectedAttentionTypes
            });
        }

        // Mark parameters as changed and trigger debounced update
        this.pendingUpdates.parameters = true;
        this.scheduleRealTimeUpdate();
    }

    /**
     * Schedule a debounced real-time update
     */
    scheduleRealTimeUpdate() {
        if (!this.autoUpdateEnabled) {
            console.log('Auto-update disabled, skipping real-time update');
            return;
        }

        // Clear existing timer
        if (this.updateDebounceTimer) {
            clearTimeout(this.updateDebounceTimer);
        }

        // Show pending update status
        this.showUpdatePendingStatus();

        // Schedule new update with debouncing
        this.updateDebounceTimer = setTimeout(() => {
            this.performRealTimeUpdate();
        }, this.updateDebounceDelay);

        console.log(`Real-time update scheduled in ${this.updateDebounceDelay}ms`);
    }

    /**
     * Perform the actual real-time update
     */
    async performRealTimeUpdate() {
        try {
            console.log('Performing real-time update with pending changes:', this.pendingUpdates);

            // Check if we have the minimum required data
            if (this.selectedModels.length === 0 || this.selectedHardware.length === 0) {
                console.log('Insufficient data for real-time update (models or hardware missing)');
                this.resetPendingUpdates();
                return;
            }

            // Save current zoom state before update
            this.saveZoomState();

            // Perform the update
            await this.updateAttentionPlot();

            // Restore zoom state after update
            this.restoreZoomState();

            // Reset pending updates
            this.resetPendingUpdates();

            // Hide pending status
            this.hideUpdatePendingStatus();

            console.log('Real-time update completed successfully');
        } catch (error) {
            console.error('Real-time update failed:', error);
            this.showError(`Real-time update failed: ${error.message}`);
            this.resetPendingUpdates();
            this.hideUpdatePendingStatus();
        }
    }

    /**
     * Save current zoom and pan state
     */
    saveZoomState() {
        if (!this.chart || !this.chart.options.plugins?.zoom) {
            return;
        }

        try {
            const xScale = this.chart.scales.x;
            const yScale = this.chart.scales.y;

            this.savedZoomState = {
                x: {
                    min: xScale.min,
                    max: xScale.max
                },
                y: {
                    min: yScale.min,
                    max: yScale.max
                }
            };

            console.log('Zoom state saved:', this.savedZoomState);
        } catch (error) {
            console.warn('Failed to save zoom state:', error);
            this.savedZoomState = null;
        }
    }

    /**
     * Restore previously saved zoom and pan state
     */
    restoreZoomState() {
        if (!this.chart || !this.savedZoomState) {
            return;
        }

        try {
            // Apply saved zoom state to chart options
            this.chart.options.scales.x.min = this.savedZoomState.x.min;
            this.chart.options.scales.x.max = this.savedZoomState.x.max;
            this.chart.options.scales.y.min = this.savedZoomState.y.min;
            this.chart.options.scales.y.max = this.savedZoomState.y.max;

            // Update chart to apply the zoom state
            this.chart.update('none'); // Use 'none' mode for instant update without animation

            console.log('Zoom state restored:', this.savedZoomState);
        } catch (error) {
            console.warn('Failed to restore zoom state:', error);
        }
    }

    /**
     * Reset pending updates flags
     */
    resetPendingUpdates() {
        this.pendingUpdates = {
            models: false,
            hardware: false,
            parameters: false
        };
    }

    /**
     * Enable or disable auto-update functionality
     */
    setAutoUpdateEnabled(enabled) {
        this.autoUpdateEnabled = enabled;
        console.log(`Auto-update ${enabled ? 'enabled' : 'disabled'}`);

        if (!enabled && this.updateDebounceTimer) {
            clearTimeout(this.updateDebounceTimer);
            this.updateDebounceTimer = null;
        }
    }

    /**
     * Set the debounce delay for real-time updates
     */
    setUpdateDebounceDelay(delay) {
        this.updateDebounceDelay = Math.max(100, delay); // Minimum 100ms
        console.log(`Update debounce delay set to ${this.updateDebounceDelay}ms`);
    }

    /**
     * Get current real-time update configuration
     */
    getRealTimeUpdateConfig() {
        return {
            autoUpdateEnabled: this.autoUpdateEnabled,
            debounceDelay: this.updateDebounceDelay,
            hasPendingUpdates: Object.values(this.pendingUpdates).some(pending => pending),
            savedZoomState: this.savedZoomState !== null
        };
    }

    /**
     * Show update pending status
     */
    showUpdatePendingStatus() {
        const updateStatus = document.getElementById('attentionUpdateStatus');
        if (updateStatus) {
            updateStatus.style.display = 'inline-block';
        }
    }

    /**
     * Hide update pending status
     */
    hideUpdatePendingStatus() {
        const updateStatus = document.getElementById('attentionUpdateStatus');
        if (updateStatus) {
            updateStatus.style.display = 'none';
        }
    }

    updateDisplayOptions() {
        // This would update chart display options
        if (this.chart) {
            this.updateChartData();
        }
    }

    updateZoomSettings() {
        if (!this.chart || !this.chart.options.plugins || !this.chart.options.plugins.zoom) return;

        // Update wheel zoom setting
        this.chart.options.plugins.zoom.zoom.wheel.enabled = this.enableWheelZoom;

        // Update the chart
        this.chart.update();
    }

    async updateAttentionPlot() {
        // Enhanced input validation with user guidance
        const validationResult = this.validateInputs();
        if (!validationResult.isValid) {
            this.showError(validationResult.message, {
                error_type: 'validation_error',
                suggestions: validationResult.suggestions
            });
            return;
        }

        try {
            this.setLoading(true);
            this.setStatus('Analyzing attention performance...');

            await this.generateAttentionData();
            this.updateChartData();

            this.setStatus('Ready');
            this.showSuccess('Attention performance analysis updated successfully');
        } catch (error) {
            console.error('Failed to update attention performance plot:', error);
            
            // Enhanced error handling with user guidance
            let errorMessage = error.message || 'Unknown error occurred';
            let errorDetails = error.details || null;
            
            // Provide specific guidance based on error type
            if (error.status === 400) {
                errorMessage = 'Invalid request parameters. ' + errorMessage;
            } else if (error.status === 404) {
                errorMessage = 'Requested models or hardware not found. ' + errorMessage;
            } else if (error.status === 504) {
                errorMessage = 'Analysis timed out. Try reducing the complexity of your request.';
            } else if (error.status === 507) {
                errorMessage = 'Insufficient memory. Try smaller batch sizes or fewer models.';
            } else if (error.status >= 500) {
                errorMessage = 'Server error occurred. Please try again later.';
            }
            
            this.showError(errorMessage, errorDetails);
            this.setStatus('Error');
        } finally {
            this.setLoading(false);
        }
    }

    async generateAttentionData() {
        // Check if progressive loader is available and use optimized endpoint
        const useProgressiveLoading = window.progressiveAttentionLoader && this.options.enableProgressiveLoading !== false;
        
        if (useProgressiveLoading) {
            return this.generateAttentionDataProgressive();
        } else {
            return this.generateAttentionDataStandard();
        }
    }
    
    async generateAttentionDataProgressive() {
        console.log('Using progressive loading for attention analysis');
        
        try {
            // Prepare data for progressive loader
            const models = this.selectedModels;
            const hardware = this.selectedHardware.map(hw => hw.id || hw.name);
            const sequenceLengths = this.generateSequenceLengthArray();
            
            const options = {
                useOptimized: true,
                batchSize: 1,
                precision: 'fp16',
                flashAttention: this.flashAttentionEnabled
            };
            
            // Use progressive loader
            const result = await window.progressiveAttentionLoader.loadAttentionAnalysis(
                models, 
                sequenceLengths, 
                hardware, 
                options
            );
            
            // Convert result to expected format
            this.attentionData = this.convertProgressiveResult(result);
            console.log('Progressive attention data generated:', this.attentionData);
            
        } catch (error) {
            console.error('Progressive loading failed, falling back to standard method:', error);
            // Fallback to standard method
            return this.generateAttentionDataStandard();
        }
    }
    
    async generateAttentionDataStandard() {
        console.log('Using standard loading for attention analysis');
        
        const requestData = {
            model_names: this.selectedModels,
            hardware_ids: this.selectedHardware.map(hw => hw.id),
            sequence_length_range: this.sequenceLengthRange,
            flash_attention: this.flashAttentionEnabled,
            attention_types: this.selectedAttentionTypes
        };

        const response = await fetch(`${this.options.apiBaseUrl}/attention/performance-plot`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            let errorDetails = null;
            let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
            
            try {
                errorDetails = await response.json();
                
                // Handle structured error responses
                if (errorDetails.error) {
                    errorMessage = errorDetails.error;
                } else if (errorDetails.detail) {
                    // Handle both string and object detail formats
                    if (typeof errorDetails.detail === 'string') {
                        errorMessage = errorDetails.detail;
                    } else if (errorDetails.detail.error) {
                        errorMessage = errorDetails.detail.error;
                        errorDetails = errorDetails.detail; // Use nested error details
                    }
                }
            } catch (parseError) {
                console.warn('Failed to parse error response:', parseError);
                // Use default error message if JSON parsing fails
            }
            
            // Create enhanced error with details for better user experience
            const enhancedError = new Error(errorMessage);
            enhancedError.details = errorDetails;
            enhancedError.status = response.status;
            throw enhancedError;
        }

        this.attentionData = await response.json();
        console.log('Standard attention performance data generated:', this.attentionData);
    }
    
    generateSequenceLengthArray() {
        // Generate array of sequence lengths from range
        const min = this.sequenceLengthRange.min;
        const max = this.sequenceLengthRange.max;
        
        // For progressive loading, use fewer points to optimize performance
        if (max - min <= 8192) {
            return [min, max];
        } else {
            const mid = Math.floor((min + max) / 2);
            return [min, mid, max];
        }
    }
    
    convertProgressiveResult(result) {
        // Convert progressive loading result to expected format
        if (!result || !result.metrics) {
            return null;
        }
        
        // Group metrics by model and hardware
        const groupedData = {};
        
        result.metrics.forEach(metric => {
            const key = `${metric.model_name}_${metric.hardware_name}`;
            if (!groupedData[key]) {
                groupedData[key] = {
                    model_name: metric.model_name,
                    hardware_name: metric.hardware_name,
                    attention_type: metric.mechanism_type,
                    points: []
                };
            }
            
            groupedData[key].points.push({
                x: metric.memory_movement_bytes / 1e9, // Convert to GB
                y: metric.total_flops / 1e9, // Convert to GFLOPS
                sequence_length: metric.sequence_length,
                operational_intensity: metric.operational_intensity,
                flash_attention: metric.flash_attention_enabled
            });
        });
        
        // Convert to expected format
        const convertedData = {
            model_data: Object.values(groupedData),
            hardware_limits: result.hardware_limits || {},
            roofline_points: result.roofline_points || [],
            performance_stats: result.performance_stats
        };
        
        return convertedData;
    }

    updateChartData() {
        if (!this.chart || !this.attentionData) return;

        const datasets = [];

        // Add hardware roofline curves
        this.addHardwareRooflineDatasets(datasets);

        // Add model trajectory datasets
        this.addModelTrajectoryDatasets(datasets);

        // Check if we should preserve zoom state (for real-time updates)
        const shouldPreserveZoom = this.savedZoomState !== null;

        // Update axis ranges only if not preserving zoom
        if (!shouldPreserveZoom) {
            this.chart.options.scales.x.max = this.getXAxisMax();
            this.chart.options.scales.y.max = this.getYAxisMax();
        }

        // Update chart
        this.chart.data.datasets = datasets;
        
        // Use different update modes based on whether we're preserving zoom
        if (shouldPreserveZoom) {
            this.chart.update('none'); // No animation for real-time updates
        } else {
            this.chart.update(); // Normal update with animation
        }

        // Update info panel
        this.updateInfoPanel();
    }

    addHardwareRooflineDatasets(datasets) {
        if (!this.attentionData.hardware_rooflines) return;

        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];

        let colorIndex = 0;

        Object.entries(this.attentionData.hardware_rooflines).forEach(([hardwareId, rooflineData]) => {
            const hardware = this.selectedHardware.find(hw => hw.id === hardwareId);
            const hardwareName = hardware ? hardware.name : hardwareId;
            const color = colors[colorIndex % colors.length];

            datasets.push({
                label: `${hardwareName} Roofline`,
                data: rooflineData.map(point => ({
                    x: point.memory_access_gb,
                    y: point.compute_gflops
                })),
                borderColor: color,
                backgroundColor: color + '20',
                fill: false,
                showLine: true,
                pointRadius: 0,
                pointHoverRadius: 4,
                borderWidth: 2,
                borderDash: [5, 5],
                type: 'line',
                datasetType: 'roofline',
                hardwareName: hardwareName,
                tension: 0.1
            });

            colorIndex++;
        });
    }

    addModelTrajectoryDatasets(datasets) {
        if (!this.attentionData.model_trajectories) return;

        const modelColors = {
            'MHA': '#FF6384',
            'GQA': '#36A2EB', 
            'MLA': '#FFCE56'
        };

        const showTrajectoryLines = document.getElementById('showTrajectoryLines')?.checked !== false;

        Object.entries(this.attentionData.model_trajectories).forEach(([modelName, trajectory]) => {
            // Handle both array and object trajectory formats
            let trajectoryPoints = [];
            let attentionType = 'Unknown';
            
            if (Array.isArray(trajectory)) {
                // Old format: array of points
                if (trajectory.length === 0) return;
                trajectoryPoints = trajectory;
                attentionType = trajectory[0].attention_type || 'Unknown';
            } else if (trajectory && typeof trajectory === 'object') {
                // New format: object with arrays
                if (!trajectory.memory_access_gb || trajectory.memory_access_gb.length === 0) return;
                
                // Convert to array of points
                trajectoryPoints = trajectory.memory_access_gb.map((memory, i) => ({
                    memory_access_gb: memory,
                    compute_gflops: trajectory.compute_gflops[i],
                    sequence_length: trajectory.sequence_lengths[i],
                    arithmetic_intensity: trajectory.arithmetic_intensity[i]
                }));
                
                attentionType = trajectory.attention_mechanism || 'Unknown';
            } else {
                return; // Skip invalid trajectory
            }

            const color = modelColors[attentionType] || '#9966FF';

            // Filter trajectory based on selected attention types
            if (!this.selectedAttentionTypes.includes(attentionType)) return;

            const shortModelName = modelName.split('/').pop();

            datasets.push({
                label: `${shortModelName} (${attentionType})`,
                data: trajectoryPoints.map(point => ({
                    x: point.memory_access_gb,
                    y: point.compute_gflops
                })),
                backgroundColor: color,
                borderColor: color,
                pointRadius: 4,
                pointHoverRadius: 6,
                showLine: showTrajectoryLines,
                borderWidth: showTrajectoryLines ? 2 : 0,
                type: 'scatter',
                datasetType: 'model',
                modelName: shortModelName,
                attentionType: attentionType,
                flashAttention: this.flashAttentionEnabled,
                rawData: trajectoryPoints
            });
        });
    }

    updateInfoPanel() {
        const infoContent = document.getElementById('attentionInfoContent');
        if (!infoContent) return;

        let infoHTML = '';

        if (this.selectedModels.length > 0) {
            infoHTML += `
                <div class="mb-3">
                    <strong>Models:</strong>
                    <div class="mt-1">
                        ${this.selectedModels.map(model =>
                            `<span class="badge bg-primary me-1">${model.split('/').pop()}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }

        if (this.selectedHardware.length > 0) {
            infoHTML += `
                <div class="mb-3">
                    <strong>Hardware Platforms:</strong>
                    <div class="mt-1">
                        ${this.selectedHardware.map(hw =>
                            `<span class="badge bg-secondary me-1">${hw.name}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }

        if (this.selectedAttentionTypes.length > 0) {
            infoHTML += `
                <div class="mb-3">
                    <strong>Attention Types:</strong>
                    <div class="mt-1">
                        ${this.selectedAttentionTypes.map(type =>
                            `<span class="badge bg-info me-1">${type}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }

        infoHTML += `
            <div class="mb-3">
                <strong>Sequence Length Range:</strong> ${this.sequenceLengthRange.min / 1000}K - ${this.sequenceLengthRange.max / 1000}K tokens
            </div>
            <div class="mb-3">
                <strong>FlashAttention:</strong> ${this.flashAttentionEnabled ? 'Enabled' : 'Disabled'}
            </div>
        `;

        if (this.attentionData && this.attentionData.model_trajectories) {
            const trajectoryCount = Object.keys(this.attentionData.model_trajectories).length;
            infoHTML += `
                <div class="mb-3">
                    <strong>Model Trajectories:</strong> ${trajectoryCount} plotted
                </div>
            `;
        }

        if (!infoHTML) {
            infoHTML = `
                <div class="text-muted">
                    <i class="fas fa-lightbulb me-1"></i>
                    Select models and hardware platforms to generate attention performance analysis.
                    This visualization shows compute vs memory access characteristics for different attention mechanisms.
                </div>
            `;
        }

        infoContent.innerHTML = infoHTML;
    }

    resetZoom() {
        if (this.chart && this.chart.resetZoom) {
            this.chart.resetZoom();
        }
    }

    exportChart() {
        if (!this.chart) return;

        try {
            const url = this.chart.toBase64Image();
            const link = document.createElement('a');
            link.download = 'attention-performance-chart.png';
            link.href = url;
            link.click();

            this.showSuccess('Chart exported successfully');
        } catch (error) {
            console.error('Failed to export chart:', error);
            this.showError('Failed to export chart');
        }
    }

    setLoading(loading) {
        const overlay = document.getElementById('attentionLoadingOverlay');
        if (overlay) {
            overlay.style.display = loading ? 'flex' : 'none';
        }
        this.isLoading = loading;
    }

    setStatus(status) {
        const statusElement = document.getElementById('attentionStatus');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `badge bg-${this.getStatusColor(status)}`;
        }
    }

    getStatusColor(status) {
        switch (status.toLowerCase()) {
            case 'ready': return 'success';
            case 'loading': case 'analyzing attention performance...': return 'primary';
            case 'error': return 'danger';
            default: return 'secondary';
        }
    }

    showError(message, errorDetails = null) {
        console.error('Attention Performance Visualizer Error:', message);
        
        // Enhanced error display with user guidance
        let displayMessage = message;
        let suggestions = [];
        
        if (errorDetails) {
            // Extract structured error information
            if (errorDetails.suggestions && Array.isArray(errorDetails.suggestions)) {
                suggestions = errorDetails.suggestions;
            }
            
            if (errorDetails.error_type) {
                displayMessage = this.formatErrorByType(errorDetails.error_type, message, errorDetails);
            }
        }
        
        // Show error with suggestions
        if (window.dashboard && window.dashboard.showToast) {
            window.dashboard.showToast(displayMessage, 'error', {
                duration: 8000,
                suggestions: suggestions
            });
        } else {
            // Fallback error display
            this.showErrorInContainer(displayMessage, suggestions);
        }
        
        // Update status to show error state
        this.setStatus('Error');
    }
    
    formatErrorByType(errorType, message, errorDetails) {
        const errorTypeMessages = {
            'validation_error': 'Input Validation Error',
            'model_not_found': 'Model Not Available',
            'hardware_error': 'Hardware Configuration Issue',
            'timeout_error': 'Analysis Timeout',
            'memory_error': 'Memory Limitation',
            'analysis_error': 'Analysis Failed',
            'network_error': 'Connection Problem',
            'internal_error': 'System Error'
        };
        
        const prefix = errorTypeMessages[errorType] || 'Error';
        return `${prefix}: ${message}`;
    }
    
    showErrorInContainer(message, suggestions = []) {
        // Create error display in the chart container if toast system is not available
        const container = this.container;
        if (!container) return;
        
        // Remove existing error displays
        const existingErrors = container.querySelectorAll('.attention-error-display');
        existingErrors.forEach(el => el.remove());
        
        // Create error display element
        const errorDiv = document.createElement('div');
        errorDiv.className = 'attention-error-display alert alert-danger';
        errorDiv.style.cssText = `
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        `;
        
        let errorHtml = `<strong>Error:</strong> ${message}`;
        
        if (suggestions.length > 0) {
            errorHtml += '<br><br><strong>Suggestions:</strong><ul>';
            suggestions.forEach(suggestion => {
                errorHtml += `<li>${suggestion}</li>`;
            });
            errorHtml += '</ul>';
        }
        
        errorDiv.innerHTML = errorHtml;
        
        // Add close button
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            float: right;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #721c24;
        `;
        closeBtn.onclick = () => errorDiv.remove();
        errorDiv.appendChild(closeBtn);
        
        // Insert at the top of the container
        container.insertBefore(errorDiv, container.firstChild);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 10000);
    }
    
    validateInputs() {
        const errors = [];
        const suggestions = [];
        
        // Validate models
        if (!this.selectedModels || this.selectedModels.length === 0) {
            errors.push('No models selected');
            suggestions.push('Select at least one model from the model dropdown');
            suggestions.push('Try popular models like Llama-2-7b or DeepSeek-V3');
        } else if (this.selectedModels.length > 10) {
            errors.push('Too many models selected');
            suggestions.push('Reduce the number of models to 10 or fewer for better performance');
        }
        
        // Validate hardware
        if (!this.selectedHardware || this.selectedHardware.length === 0) {
            errors.push('No hardware platforms selected');
            suggestions.push('Select at least one hardware platform (H100, A800, etc.)');
        } else if (this.selectedHardware.length > 5) {
            errors.push('Too many hardware platforms selected');
            suggestions.push('Reduce the number of hardware platforms to 5 or fewer');
        }
        
        // Validate sequence length range
        if (this.sequenceLengthRange) {
            if (this.sequenceLengthRange.min < 1) {
                errors.push('Minimum sequence length is too small');
                suggestions.push('Use minimum sequence length of at least 1');
            }
            if (this.sequenceLengthRange.max > 131072) {
                errors.push('Maximum sequence length is too large');
                suggestions.push('Use maximum sequence length of 131072 or less');
            }
            if (this.sequenceLengthRange.min >= this.sequenceLengthRange.max) {
                errors.push('Invalid sequence length range');
                suggestions.push('Ensure minimum sequence length is less than maximum');
            }
        }
        
        // Validate attention types
        if (!this.selectedAttentionTypes || this.selectedAttentionTypes.length === 0) {
            errors.push('No attention types selected');
            suggestions.push('Select at least one attention type (MHA, GQA, MLA)');
        }
        
        // Check for reasonable analysis complexity
        const totalCombinations = (this.selectedModels?.length || 0) * 
                                 (this.selectedHardware?.length || 0) * 
                                 (this.selectedAttentionTypes?.length || 0);
        
        if (totalCombinations > 100) {
            errors.push('Analysis complexity is too high');
            suggestions.push('Reduce the number of models, hardware platforms, or attention types');
            suggestions.push('Consider analyzing in smaller batches');
        }
        
        return {
            isValid: errors.length === 0,
            message: errors.length > 0 ? errors.join('; ') : '',
            suggestions: suggestions
        };
    }

    showSuccess(message) {
        console.log('Attention Performance Visualizer Success:', message);
        if (window.dashboard && window.dashboard.showToast) {
            window.dashboard.showToast(message, 'success');
        }
    }

    // Public API methods for integration with main dashboard
    updateModels(models) {
        const previousModels = [...this.selectedModels];
        this.selectedModels = models || [];
        
        console.log('Attention Performance Visualizer: Models updated', {
            previous: previousModels,
            current: this.selectedModels
        });

        // Check if models actually changed
        const modelsChanged = JSON.stringify(previousModels.sort()) !== JSON.stringify(this.selectedModels.sort());
        
        if (modelsChanged) {
            this.pendingUpdates.models = true;
            this.scheduleRealTimeUpdate();
        }
    }

    updateHardware(hardware) {
        const previousHardware = [...this.selectedHardware];
        this.selectedHardware = hardware || [];
        
        console.log('Attention Performance Visualizer: Hardware updated', {
            previous: previousHardware,
            current: this.selectedHardware
        });

        // Check if hardware actually changed
        const hardwareChanged = JSON.stringify(previousHardware) !== JSON.stringify(this.selectedHardware);
        
        if (hardwareChanged) {
            this.pendingUpdates.hardware = true;
            this.scheduleRealTimeUpdate();
        }
    }

    /**
     * Set selected hardware for attention analysis
     * @param {Array} hardwareArray - Array of hardware objects
     */
    setSelectedHardware(hardwareArray) {
        console.log('Setting selected hardware for attention analysis:', hardwareArray);
        
        this.selectedHardware = hardwareArray || [];
        
        // Update status
        this.setStatus(this.selectedHardware.length > 0 ? 'Hardware selected' : 'No hardware selected');
        
        // Validate hardware for attention analysis
        const validationResults = this.validateHardwareForAttention(this.selectedHardware);
        this.displayHardwareValidation(validationResults);
        
        // Update plot if we have both models and hardware
        if (this.selectedModels.length > 0 && this.selectedHardware.length > 0) {
            this.updateAttentionPlot();
        } else if (this.selectedHardware.length === 0) {
            // Clear the plot if no hardware is selected
            this.clearChart();
        }
        
        // Update info panel
        this.updateInfoPanel();
    }

    /**
     * Validate hardware compatibility for attention analysis
     * @param {Array} hardwareArray - Array of hardware objects to validate
     * @returns {Object} Validation results
     */
    validateHardwareForAttention(hardwareArray) {
        const results = {
            isValid: true,
            warnings: [],
            errors: [],
            compatibleHardware: [],
            incompatibleHardware: []
        };
        
        if (!hardwareArray || hardwareArray.length === 0) {
            results.isValid = false;
            results.errors.push('No hardware selected for attention analysis');
            return results;
        }
        
        hardwareArray.forEach(hardware => {
            const hardwareValidation = this.validateSingleHardwareForAttention(hardware);
            
            if (hardwareValidation.isValid) {
                results.compatibleHardware.push(hardware);
            } else {
                results.incompatibleHardware.push(hardware);
                results.errors.push(...hardwareValidation.errors.map(err => `${hardware.name}: ${err}`));
            }
            
            results.warnings.push(...hardwareValidation.warnings.map(warn => `${hardware.name}: ${warn}`));
        });
        
        // Overall validation is valid if at least one hardware is compatible
        results.isValid = results.compatibleHardware.length > 0;
        
        return results;
    }

    /**
     * Validate a single hardware for attention analysis
     * @param {Object} hardware - Hardware object to validate
     * @returns {Object} Validation results for single hardware
     */
    validateSingleHardwareForAttention(hardware) {
        const results = {
            isValid: true,
            warnings: [],
            errors: []
        };
        
        // Check required properties
        if (!hardware.memory_bandwidth_gbps || hardware.memory_bandwidth_gbps <= 0) {
            results.isValid = false;
            results.errors.push('Memory bandwidth information missing or invalid');
        }
        
        if (!hardware.memory_size_gb || hardware.memory_size_gb <= 0) {
            results.isValid = false;
            results.errors.push('Memory size information missing or invalid');
        }
        
        // Check hardware type
        const supportedTypes = ['gpu', 'npu'];
        if (!supportedTypes.includes(hardware.type)) {
            results.isValid = false;
            results.errors.push(`Unsupported hardware type: ${hardware.type}`);
        }
        
        // Performance warnings
        if (hardware.memory_size_gb < 8) {
            results.warnings.push('Limited memory may restrict analysis of large models');
        }
        
        if (hardware.memory_bandwidth_gbps < 500) {
            results.warnings.push('Low memory bandwidth may affect attention performance analysis accuracy');
        }
        
        // Check tensor performance availability
        if (!hardware.tensor_performance || Object.keys(hardware.tensor_performance).length === 0) {
            results.warnings.push('Tensor performance information not available - using estimated values');
        }
        
        // Check precision support
        if (hardware.supported_precisions && !hardware.supported_precisions.includes('fp16')) {
            results.warnings.push('FP16 precision not explicitly supported - may affect analysis accuracy');
        }
        
        return results;
    }

    /**
     * Display hardware validation results in the UI
     * @param {Object} validationResults - Validation results to display
     */
    displayHardwareValidation(validationResults) {
        const infoContent = document.getElementById('attentionInfoContent');
        if (!infoContent) return;
        
        let validationHTML = '';
        
        if (!validationResults.isValid) {
            validationHTML = `
                <div class="alert alert-danger py-2 mb-2">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    <strong>Hardware Compatibility Issues:</strong>
                    <ul class="mb-0 mt-1">
                        ${validationResults.errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `;
        } else {
            // Show success with any warnings
            validationHTML = `
                <div class="alert alert-success py-2 mb-2">
                    <i class="fas fa-check-circle me-1"></i>
                    <strong>Hardware Compatible:</strong> ${validationResults.compatibleHardware.length} hardware platform(s) ready for attention analysis
                </div>
            `;
            
            if (validationResults.warnings.length > 0) {
                validationHTML += `
                    <div class="alert alert-warning py-2 mb-2">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>Compatibility Warnings:</strong>
                        <ul class="mb-0 mt-1">
                            ${validationResults.warnings.slice(0, 5).map(warning => `<li>${warning}</li>`).join('')}
                            ${validationResults.warnings.length > 5 ? `<li>... and ${validationResults.warnings.length - 5} more warnings</li>` : ''}
                        </ul>
                    </div>
                `;
            }
        }
        
        // Update the info content with validation results
        const existingContent = infoContent.innerHTML;
        if (existingContent.includes('Select models and hardware')) {
            infoContent.innerHTML = validationHTML;
        } else {
            // Prepend validation to existing content
            infoContent.innerHTML = validationHTML + existingContent;
        }
    }

    /**
     * Clear the chart data and display
     */
    clearChart() {
        if (!this.chart) return;
        
        this.chart.data.datasets = [];
        this.chart.update();
        
        // Reset axis ranges to defaults
        this.chart.options.scales.x.max = 50;
        this.chart.options.scales.y.max = 1000;
        this.chart.update();
        
        console.log('Attention performance chart cleared');
    }

    /**
     * Set status message in the UI
     */
    setStatus(status) {
        const statusElement = document.getElementById('attentionStatus');
        if (statusElement) {
            statusElement.textContent = status;
            
            // Update badge color based on status
            statusElement.className = 'badge';
            if (status.toLowerCase().includes('error')) {
                statusElement.classList.add('bg-danger');
            } else if (status.toLowerCase().includes('ready')) {
                statusElement.classList.add('bg-success');
            } else if (status.toLowerCase().includes('analyzing') || status.toLowerCase().includes('loading')) {
                statusElement.classList.add('bg-warning');
            } else {
                statusElement.classList.add('bg-secondary');
            }
        }
    }

    /**
     * Set loading state for the visualizer
     */
    setLoading(isLoading) {
        this.isLoading = isLoading;
        
        const loadingOverlay = document.getElementById('attentionLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = isLoading ? 'flex' : 'none';
        }
        
        // Disable/enable controls during loading
        const controls = document.querySelectorAll('#updateAttentionPlotBtn, #resetAttentionZoomBtn, #exportAttentionPlotBtn');
        controls.forEach(control => {
            if (control) {
                control.disabled = isLoading;
            }
        });
    }

    /**
     * Show error message to user
     */
    showError(message) {
        console.error('Attention Performance Visualizer Error:', message);
        
        // Update info panel with error
        const infoContent = document.getElementById('attentionInfoContent');
        if (infoContent) {
            infoContent.innerHTML = `
                <div class="alert alert-danger py-2 mb-0">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    <strong>Error:</strong> ${message}
                </div>
            `;
        }
        
        this.setStatus('Error');
    }

    /**
     * Show success message to user
     */
    showSuccess(message) {
        console.log('Attention Performance Visualizer Success:', message);
        
        // Could show a temporary success message in the info panel
        const infoContent = document.getElementById('attentionInfoContent');
        if (infoContent) {
            const existingContent = infoContent.innerHTML;
            infoContent.innerHTML = `
                <div class="alert alert-success py-2 mb-2">
                    <i class="fas fa-check-circle me-1"></i>
                    ${message}
                </div>
                ${existingContent}
            `;
            
            // Remove success message after 3 seconds
            setTimeout(() => {
                const successAlert = infoContent.querySelector('.alert-success');
                if (successAlert) {
                    successAlert.remove();
                }
            }, 3000);
        }
    }

    show() {
        if (this.container) {
            this.container.style.display = 'block';
        }
    }

    hide() {
        if (this.container) {
            this.container.style.display = 'none';
        }
    }

    destroy() {
        // Clear any pending timers
        if (this.updateDebounceTimer) {
            clearTimeout(this.updateDebounceTimer);
            this.updateDebounceTimer = null;
        }

        // Destroy chart
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }

        // Clear state
        this.savedZoomState = null;
        this.resetPendingUpdates();
        
        console.log('AttentionPerformanceVisualizer destroyed');
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AttentionPerformanceVisualizer;
}
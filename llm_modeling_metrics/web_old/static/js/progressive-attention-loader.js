/**
 * Progressive loading component for attention performance analysis.
 * 
 * This component provides:
 * - Progressive loading of attention analysis data
 * - Real-time progress updates
 * - Performance statistics display
 * - Optimized visualization rendering
 */

class ProgressiveAttentionLoader {
    constructor(options = {}) {
        this.options = {
            maxConcurrentRequests: 4,
            batchSize: 2,
            progressUpdateInterval: 100,
            enableCaching: true,
            showPerformanceStats: true,
            ...options
        };
        
        this.isLoading = false;
        this.currentProgress = 0;
        this.loadingTasks = new Map();
        this.cache = new Map();
        this.performanceStats = {
            totalRequests: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageLoadTime: 0,
            totalLoadTime: 0
        };
        
        this.initializeUI();
        this.setupEventListeners();
    }
    
    initializeUI() {
        // Create progress container if it doesn't exist
        if (!document.getElementById('progressive-loading-container')) {
            const container = document.createElement('div');
            container.id = 'progressive-loading-container';
            container.className = 'progressive-loading-container';
            container.innerHTML = `
                <div class="progress-header">
                    <h4>Loading Attention Analysis</h4>
                    <button id="cancel-loading-btn" class="btn btn-sm btn-outline-secondary" style="display: none;">
                        Cancel
                    </button>
                </div>
                <div class="progress-wrapper">
                    <div class="progress mb-2">
                        <div id="loading-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="loading-status" class="loading-status text-muted">Ready to load...</div>
                </div>
                <div id="loading-details" class="loading-details" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <div>Models: <span id="models-count">0</span></div>
                                <div>Hardware: <span id="hardware-count">0</span></div>
                                <div>Combinations: <span id="combinations-count">0</span></div>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <div>Completed: <span id="completed-count">0</span></div>
                                <div>Cache Hits: <span id="cache-hits">0</span></div>
                                <div>Avg Time: <span id="avg-time">0ms</span></div>
                            </small>
                        </div>
                    </div>
                </div>
                <div id="performance-stats" class="performance-stats mt-2" style="display: none;">
                    <div class="card card-body bg-light">
                        <h6>Performance Statistics</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <div class="stat-value" id="cache-hit-rate">0%</div>
                                    <div class="stat-label">Cache Hit Rate</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <div class="stat-value" id="total-load-time">0s</div>
                                    <div class="stat-label">Total Load Time</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-item">
                                    <div class="stat-value" id="optimization-factor">1.0x</div>
                                    <div class="stat-label">Optimization Factor</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Insert before the main analysis container
            const analysisContainer = document.getElementById('analysis-container') || document.body;
            analysisContainer.parentNode.insertBefore(container, analysisContainer);
        }
        
        this.progressContainer = document.getElementById('progressive-loading-container');
        this.progressBar = document.getElementById('loading-progress-bar');
        this.statusElement = document.getElementById('loading-status');
        this.detailsElement = document.getElementById('loading-details');
        this.performanceElement = document.getElementById('performance-stats');
        
        // Hide container initially
        this.progressContainer.style.display = 'none';
    }
    
    setupEventListeners() {
        // Cancel button
        const cancelBtn = document.getElementById('cancel-loading-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.cancelLoading());
        }
        
        // Performance stats toggle
        if (this.options.showPerformanceStats) {
            const statsToggle = document.createElement('button');
            statsToggle.className = 'btn btn-sm btn-link';
            statsToggle.textContent = 'Show Performance Stats';
            statsToggle.addEventListener('click', () => this.togglePerformanceStats());
            
            const progressHeader = this.progressContainer.querySelector('.progress-header');
            progressHeader.appendChild(statsToggle);
        }
    }
    
    async loadAttentionAnalysis(models, sequenceLengths, hardware, options = {}) {
        if (this.isLoading) {
            console.warn('Loading already in progress');
            return null;
        }
        
        this.isLoading = true;
        this.currentProgress = 0;
        const startTime = performance.now();
        
        try {
            // Show loading UI
            this.showLoadingUI();
            this.updateLoadingStats(models, hardware, sequenceLengths);
            
            // Check if we should use optimized endpoint
            const useOptimized = options.useOptimized !== false;
            const endpoint = useOptimized ? '/api/attention/analyze-optimized' : '/api/attention/analyze';
            
            // Prepare request data
            const requestData = {
                model_names: models,
                sequence_lengths: sequenceLengths,
                hardware_names: hardware,
                batch_size: options.batchSize || 1,
                precision: options.precision || 'fp16',
                flash_attention: options.flashAttention !== false
            };
            
            // Generate cache key
            const cacheKey = this.generateCacheKey(requestData);
            
            // Check cache first
            if (this.options.enableCaching && this.cache.has(cacheKey)) {
                this.performanceStats.cacheHits++;
                this.updateStatus('Loading from cache...');
                this.setProgress(100);
                
                const cachedResult = this.cache.get(cacheKey);
                this.hideLoadingUI();
                return cachedResult;
            }
            
            this.performanceStats.cacheMisses++;
            
            // Setup WebSocket for progress updates if using optimized endpoint
            let wsConnection = null;
            if (useOptimized) {
                wsConnection = this.setupProgressWebSocket();
            }
            
            // Make the request
            this.updateStatus('Sending analysis request...');
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `HTTP ${response.status}`);
            }
            
            // Handle progressive loading for optimized endpoint
            if (useOptimized) {
                this.updateStatus('Processing with optimizations...');
            } else {
                this.updateStatus('Processing analysis...');
                // Simulate progress for non-optimized endpoint
                this.simulateProgress();
            }
            
            const result = await response.json();
            
            // Cache the result
            if (this.options.enableCaching) {
                this.cache.set(cacheKey, result);
            }
            
            // Update performance stats
            const loadTime = performance.now() - startTime;
            this.updatePerformanceStats(loadTime);
            
            // Close WebSocket if opened
            if (wsConnection) {
                wsConnection.close();
            }
            
            this.setProgress(100);
            this.updateStatus('Analysis completed successfully!');
            
            // Show final stats
            if (this.options.showPerformanceStats) {
                this.showPerformanceStats(result);
            }
            
            // Hide loading UI after a short delay
            setTimeout(() => this.hideLoadingUI(), 2000);
            
            return result;
            
        } catch (error) {
            console.error('Error in progressive loading:', error);
            this.updateStatus(`Error: ${error.message}`);
            this.hideLoadingUI();
            throw error;
        } finally {
            this.isLoading = false;
        }
    }
    
    setupProgressWebSocket() {
        try {
            const wsUrl = `ws://${window.location.host}/ws/attention-progress-${Date.now()}`;
            const ws = new WebSocket(wsUrl);
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'analysis_progress') {
                        this.setProgress(data.progress * 100);
                        this.updateStatus(data.message);
                    }
                } catch (e) {
                    console.warn('Error parsing WebSocket message:', e);
                }
            };
            
            ws.onerror = (error) => {
                console.warn('WebSocket error:', error);
            };
            
            return ws;
        } catch (error) {
            console.warn('Failed to setup WebSocket:', error);
            return null;
        }
    }
    
    simulateProgress() {
        // Simulate progress for non-optimized endpoints
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress >= 90) {
                clearInterval(interval);
                progress = 90; // Leave room for completion
            }
            this.setProgress(progress);
        }, 500);
        
        return interval;
    }
    
    generateCacheKey(requestData) {
        // Generate a cache key from request parameters
        const keyData = {
            models: requestData.model_names.sort(),
            sequences: requestData.sequence_lengths.sort(),
            hardware: requestData.hardware_names.sort(),
            batch_size: requestData.batch_size,
            precision: requestData.precision,
            flash_attention: requestData.flash_attention
        };
        
        return btoa(JSON.stringify(keyData));
    }
    
    showLoadingUI() {
        this.progressContainer.style.display = 'block';
        this.detailsElement.style.display = 'block';
        
        const cancelBtn = document.getElementById('cancel-loading-btn');
        if (cancelBtn) {
            cancelBtn.style.display = 'inline-block';
        }
    }
    
    hideLoadingUI() {
        setTimeout(() => {
            this.progressContainer.style.display = 'none';
            this.detailsElement.style.display = 'none';
            
            const cancelBtn = document.getElementById('cancel-loading-btn');
            if (cancelBtn) {
                cancelBtn.style.display = 'none';
            }
        }, 1000);
    }
    
    setProgress(percentage) {
        this.currentProgress = Math.min(100, Math.max(0, percentage));
        this.progressBar.style.width = `${this.currentProgress}%`;
        this.progressBar.setAttribute('aria-valuenow', this.currentProgress);
    }
    
    updateStatus(message) {
        this.statusElement.textContent = message;
    }
    
    updateLoadingStats(models, hardware, sequenceLengths) {
        const modelsCount = models.length;
        const hardwareCount = hardware.length;
        const combinationsCount = modelsCount * hardwareCount * sequenceLengths.length;
        
        document.getElementById('models-count').textContent = modelsCount;
        document.getElementById('hardware-count').textContent = hardwareCount;
        document.getElementById('combinations-count').textContent = combinationsCount;
        document.getElementById('completed-count').textContent = '0';
    }
    
    updatePerformanceStats(loadTime) {
        this.performanceStats.totalRequests++;
        this.performanceStats.totalLoadTime += loadTime;
        this.performanceStats.averageLoadTime = this.performanceStats.totalLoadTime / this.performanceStats.totalRequests;
        
        // Update UI
        document.getElementById('cache-hits').textContent = this.performanceStats.cacheHits;
        document.getElementById('avg-time').textContent = `${Math.round(this.performanceStats.averageLoadTime)}ms`;
        
        const cacheHitRate = this.performanceStats.totalRequests > 0 
            ? (this.performanceStats.cacheHits / this.performanceStats.totalRequests * 100).toFixed(1)
            : 0;
        document.getElementById('cache-hit-rate').textContent = `${cacheHitRate}%`;
        
        document.getElementById('total-load-time').textContent = `${(this.performanceStats.totalLoadTime / 1000).toFixed(1)}s`;
        
        // Calculate optimization factor (compared to baseline)
        const baselineTime = 5000; // Assume 5s baseline
        const optimizationFactor = baselineTime / this.performanceStats.averageLoadTime;
        document.getElementById('optimization-factor').textContent = `${optimizationFactor.toFixed(1)}x`;
    }
    
    showPerformanceStats(result) {
        if (result.performance_stats) {
            this.performanceElement.style.display = 'block';
            
            // Update with server-side stats if available
            const serverStats = result.performance_stats;
            if (serverStats.analyzer_stats) {
                const analyzerStats = serverStats.analyzer_stats;
                if (analyzerStats.cache_hit_rate !== undefined) {
                    document.getElementById('cache-hit-rate').textContent = 
                        `${(analyzerStats.cache_hit_rate * 100).toFixed(1)}%`;
                }
                if (analyzerStats.average_analysis_time !== undefined) {
                    document.getElementById('avg-time').textContent = 
                        `${(analyzerStats.average_analysis_time * 1000).toFixed(0)}ms`;
                }
            }
        }
    }
    
    togglePerformanceStats() {
        const isVisible = this.performanceElement.style.display !== 'none';
        this.performanceElement.style.display = isVisible ? 'none' : 'block';
        
        const toggleBtn = this.progressContainer.querySelector('.btn-link');
        if (toggleBtn) {
            toggleBtn.textContent = isVisible ? 'Show Performance Stats' : 'Hide Performance Stats';
        }
    }
    
    cancelLoading() {
        if (this.isLoading) {
            this.isLoading = false;
            this.updateStatus('Loading cancelled by user');
            this.hideLoadingUI();
            
            // Cancel any ongoing requests
            this.loadingTasks.forEach((task, key) => {
                if (task.abort) {
                    task.abort();
                }
            });
            this.loadingTasks.clear();
        }
    }
    
    clearCache() {
        this.cache.clear();
        this.performanceStats = {
            totalRequests: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageLoadTime: 0,
            totalLoadTime: 0
        };
        console.log('Progressive loading cache cleared');
    }
    
    getPerformanceStats() {
        return { ...this.performanceStats };
    }
}

// Global instance
window.progressiveAttentionLoader = new ProgressiveAttentionLoader();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProgressiveAttentionLoader;
}
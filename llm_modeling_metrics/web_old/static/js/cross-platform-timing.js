/**
 * Cross-Platform Timing Analysis Component
 * Provides detailed timing analysis across multiple hardware platforms
 */

class CrossPlatformTiming {
    constructor() {
        this.selectedHardware = new Set();
        this.selectedOperators = [];
        this.timingData = null;
        this.charts = {};

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Hardware selection for timing analysis
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('timing-hardware-checkbox')) {
                this.handleHardwareSelection(e.target);
            }
        });

        // Operator selection
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('timing-operator-checkbox')) {
                this.handleOperatorSelection(e.target);
            }
        });

        // Analyze timing button
        const analyzeBtn = document.getElementById('analyze-timing-btn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => this.performTimingAnalysis());
        }

        // Export timing results
        const exportBtn = document.getElementById('export-timing-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportTimingResults());
        }

        // Performance scaling view toggle
        const scalingToggle = document.getElementById('scaling-view-toggle');
        if (scalingToggle) {
            scalingToggle.addEventListener('change', (e) => {
                this.toggleScalingView(e.target.checked);
            });
        }
    }

    async loadOperatorsFromAnalysis() {
        // Load operators from current analysis if available
        if (window.currentAnalysisData && window.currentAnalysisData.operators) {
            this.renderOperatorSelector(window.currentAnalysisData.operators);
        } else {
            // Load default operators for demonstration
            const defaultOperators = [
                {
                    name: 'Attention',
                    type: 'attention',
                    parameters: {
                        hidden_size: 4096,
                        num_heads: 32,
                        sequence_length: 2048,
                        batch_size: 1
                    }
                },
                {
                    name: 'MLP',
                    type: 'mlp',
                    parameters: {
                        hidden_size: 4096,
                        intermediate_size: 11008,
                        batch_size: 1,
                        sequence_length: 2048
                    }
                },
                {
                    name: 'Embedding',
                    type: 'embedding',
                    parameters: {
                        vocab_size: 32000,
                        hidden_size: 4096,
                        batch_size: 1,
                        sequence_length: 2048
                    }
                }
            ];
            this.renderOperatorSelector(defaultOperators);
        }
    }

    renderOperatorSelector(operators) {
        const container = document.getElementById('timing-operator-selector');
        if (!container) return;

        const html = `
            <div class="operator-selection">
                <h5>Select Operators for Analysis</h5>
                <div class="operator-grid">
                    ${operators.map((op, index) => `
                        <div class="operator-card">
                            <div class="operator-header">
                                <input type="checkbox" class="timing-operator-checkbox"
                                       id="timing-op-${index}" value="${index}" checked>
                                <label for="timing-op-${index}" class="operator-name">${op.name}</label>
                            </div>
                            <div class="operator-details">
                                <div class="detail-item">
                                    <span class="detail-label">Type:</span>
                                    <span class="detail-value">${op.type}</span>
                                </div>
                                ${this.renderOperatorParameters(op.parameters)}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        container.innerHTML = html;

        // Initialize with all operators selected
        this.selectedOperators = operators.map((op, index) => ({
            ...op,
            index: index
        }));
    }

    renderOperatorParameters(parameters) {
        return Object.entries(parameters)
            .slice(0, 3) // Show only first 3 parameters
            .map(([key, value]) => `
                <div class="detail-item">
                    <span class="detail-label">${key.replace(/_/g, ' ')}:</span>
                    <span class="detail-value">${value}</span>
                </div>
            `).join('');
    }

    handleHardwareSelection(checkbox) {
        const hardwareId = checkbox.value;

        if (checkbox.checked) {
            this.selectedHardware.add(hardwareId);
        } else {
            this.selectedHardware.delete(hardwareId);
        }

        this.updateTimingAnalysisUI();
    }

    handleOperatorSelection(checkbox) {
        const operatorIndex = parseInt(checkbox.value);

        if (checkbox.checked) {
            // Add operator if not already selected
            const operator = this.getAllOperators()[operatorIndex];
            if (operator && !this.selectedOperators.find(op => op.index === operatorIndex)) {
                this.selectedOperators.push({
                    ...operator,
                    index: operatorIndex
                });
            }
        } else {
            // Remove operator
            this.selectedOperators = this.selectedOperators.filter(op => op.index !== operatorIndex);
        }

        this.updateTimingAnalysisUI();
    }

    getAllOperators() {
        // Get all available operators
        if (window.currentAnalysisData && window.currentAnalysisData.operators) {
            return window.currentAnalysisData.operators;
        }

        // Return default operators
        return [
            {
                name: 'Attention',
                type: 'attention',
                parameters: {
                    hidden_size: 4096,
                    num_heads: 32,
                    sequence_length: 2048,
                    batch_size: 1
                }
            },
            {
                name: 'MLP',
                type: 'mlp',
                parameters: {
                    hidden_size: 4096,
                    intermediate_size: 11008,
                    batch_size: 1,
                    sequence_length: 2048
                }
            },
            {
                name: 'Embedding',
                type: 'embedding',
                parameters: {
                    vocab_size: 32000,
                    hidden_size: 4096,
                    batch_size: 1,
                    sequence_length: 2048
                }
            }
        ];
    }

    updateTimingAnalysisUI() {
        const analyzeBtn = document.getElementById('analyze-timing-btn');
        const hardwareCount = this.selectedHardware.size;
        const operatorCount = this.selectedOperators.length;

        if (analyzeBtn) {
            analyzeBtn.disabled = hardwareCount < 2 || operatorCount === 0;
            analyzeBtn.textContent = `Analyze Timing (${hardwareCount} hardware, ${operatorCount} operators)`;
        }
    }

    async performTimingAnalysis() {
        if (this.selectedHardware.size < 2) {
            this.showError('Please select at least 2 hardware platforms for comparison');
            return;
        }

        if (this.selectedOperators.length === 0) {
            this.showError('Please select at least 1 operator for analysis');
            return;
        }

        this.showLoading(true);

        try {
            const request = {
                hardware_ids: Array.from(this.selectedHardware),
                operators: this.selectedOperators.map(op => ({
                    name: op.name,
                    type: op.type,
                    parameters: op.parameters,
                    precision: 'fp16' // Default precision
                }))
            };

            const response = await fetch('/api/hardware/cross-platform-timing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(request)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            this.timingData = await response.json();
            this.renderTimingResults();

        } catch (error) {
            console.error('Error performing timing analysis:', error);
            this.showError('Failed to perform timing analysis: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    renderTimingResults() {
        const container = document.getElementById('timing-analysis-results');
        if (!container) return;

        const html = `
            <div class="timing-results-header">
                <h3>Cross-Platform Timing Analysis Results</h3>
                <div class="results-controls">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="scaling-view-toggle">
                        <label class="form-check-label" for="scaling-view-toggle">
                            Show Performance Scaling
                        </label>
                    </div>
                    <button id="export-timing-btn" class="btn btn-secondary btn-sm">
                        <i class="fas fa-download me-1"></i>
                        Export Results
                    </button>
                </div>
            </div>

            <div class="timing-results-content">
                ${this.renderTimingMatrix()}
                ${this.renderPerformanceScaling()}
                ${this.renderBottleneckAnalysis()}
                ${this.renderMigrationRecommendations()}
                ${this.renderTimingCharts()}
            </div>
        `;

        container.innerHTML = html;
        container.style.display = 'block';

        // Initialize charts
        this.initializeTimingCharts();
    }

    renderTimingMatrix() {
        const timingMatrix = this.timingData.timing_comparison.timing_matrix;
        const operators = this.timingData.timing_comparison.operators;
        const hardwarePlatforms = this.timingData.hardware_platforms;

        return `
            <div class="timing-matrix-section">
                <h4>Timing Matrix</h4>
                <div class="table-responsive">
                    <table class="timing-matrix-table">
                        <thead>
                            <tr>
                                <th>Operator</th>
                                ${hardwarePlatforms.map(hw => `<th class="hardware-column">${hw}</th>`).join('')}
                                <th>Best Hardware</th>
                                <th>Performance Range</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${operators.map(opName => {
                                const opTimings = timingMatrix[opName];
                                const times = Object.values(opTimings).map(t => t.execution_time_ms);
                                const minTime = Math.min(...times);
                                const maxTime = Math.max(...times);
                                const bestHw = Object.entries(opTimings)
                                    .reduce((best, [hw, timing]) =>
                                        timing.execution_time_ms < best.time ?
                                        {hw, time: timing.execution_time_ms} : best,
                                        {hw: '', time: Infinity}
                                    ).hw;

                                return `
                                    <tr>
                                        <td class="operator-name">${opName}</td>
                                        ${hardwarePlatforms.map(hw => {
                                            const timing = opTimings[hw];
                                            const isBottleneck = timing.bottleneck_type;
                                            const isBest = hw === bestHw;
                                            return `
                                                <td class="timing-cell ${isBest ? 'best-timing' : ''} ${isBottleneck}-bound">
                                                    <div class="timing-value">${timing.execution_time_ms.toFixed(2)} ms</div>
                                                    <div class="timing-details">
                                                        <span class="bottleneck-type">${isBottleneck}</span>
                                                        <span class="utilization">${timing.utilization_percent.toFixed(0)}%</span>
                                                    </div>
                                                </td>
                                            `;
                                        }).join('')}
                                        <td class="best-hardware">${bestHw}</td>
                                        <td class="performance-range">
                                            ${minTime.toFixed(2)} - ${maxTime.toFixed(2)} ms
                                            <div class="range-ratio">${(maxTime/minTime).toFixed(1)}x</div>
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    renderPerformanceScaling() {
        const scalingAnalysis = this.timingData.scaling_analysis;

        return `
            <div class="performance-scaling-section" id="scaling-section" style="display: none;">
                <h4>Performance Scaling Analysis</h4>
                <div class="scaling-grid">
                    ${Object.entries(scalingAnalysis).map(([opName, analysis]) => `
                        <div class="scaling-card">
                            <h5>${opName}</h5>
                            <div class="scaling-info">
                                <div class="fastest-hardware">
                                    <strong>Fastest:</strong> ${analysis.fastest_hardware}
                                    <span class="fastest-time">${analysis.performance_range.min_time_ms.toFixed(2)} ms</span>
                                </div>
                                <div class="scaling-factors">
                                    <strong>Scaling Factors:</strong>
                                    <div class="factors-list">
                                        ${Object.entries(analysis.scaling_factors).map(([hw, factor]) => `
                                            <div class="factor-item">
                                                <span class="hardware-name">${hw}:</span>
                                                <span class="factor-value ${factor === 1 ? 'best-factor' : ''}">${factor.toFixed(2)}x</span>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    renderBottleneckAnalysis() {
        const timingMatrix = this.timingData.timing_comparison.timing_matrix;
        const hardwarePlatforms = this.timingData.hardware_platforms;

        // Analyze bottlenecks by hardware
        const bottlenecksByHardware = {};
        hardwarePlatforms.forEach(hw => {
            const computeBound = [];
            const memoryBound = [];

            Object.entries(timingMatrix).forEach(([opName, opTimings]) => {
                const timing = opTimings[hw];
                if (timing.bottleneck_type === 'compute') {
                    computeBound.push(opName);
                } else {
                    memoryBound.push(opName);
                }
            });

            bottlenecksByHardware[hw] = {
                compute_bound: computeBound,
                memory_bound: memoryBound,
                overall: computeBound.length > memoryBound.length ? 'compute' : 'memory'
            };
        });

        return `
            <div class="bottleneck-analysis-section">
                <h4>Bottleneck Analysis by Hardware</h4>
                <div class="bottleneck-grid">
                    ${hardwarePlatforms.map(hw => {
                        const analysis = bottlenecksByHardware[hw];
                        return `
                            <div class="bottleneck-card">
                                <h5>${hw}</h5>
                                <div class="bottleneck-summary">
                                    <div class="overall-bottleneck ${analysis.overall}-bottleneck">
                                        Overall: ${analysis.overall.charAt(0).toUpperCase() + analysis.overall.slice(1)}-bound
                                    </div>
                                </div>
                                <div class="bottleneck-details">
                                    <div class="compute-bound-ops">
                                        <strong>Compute-bound (${analysis.compute_bound.length}):</strong>
                                        <div class="ops-list">
                                            ${analysis.compute_bound.map(op => `<span class="op-tag compute-tag">${op}</span>`).join('')}
                                        </div>
                                    </div>
                                    <div class="memory-bound-ops">
                                        <strong>Memory-bound (${analysis.memory_bound.length}):</strong>
                                        <div class="ops-list">
                                            ${analysis.memory_bound.map(op => `<span class="op-tag memory-tag">${op}</span>`).join('')}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }

    renderMigrationRecommendations() {
        const recommendations = this.timingData.migration_recommendations;
        const summaryStats = this.timingData.summary_statistics;

        return `
            <div class="migration-recommendations-section">
                <h4>Hardware Migration Recommendations</h4>

                <div class="summary-statistics">
                    <div class="stat-card">
                        <div class="stat-value">${summaryStats.total_operators}</div>
                        <div class="stat-label">Operators Analyzed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${summaryStats.total_platforms}</div>
                        <div class="stat-label">Hardware Platforms</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${summaryStats.best_overall_platform}</div>
                        <div class="stat-label">Best Overall Platform</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${summaryStats.average_performance_variance.toFixed(1)}x</div>
                        <div class="stat-label">Avg Performance Variance</div>
                    </div>
                </div>

                <div class="recommendations-list">
                    <h5>Migration Recommendations</h5>
                    <ul class="recommendation-items">
                        ${recommendations.map(rec => `
                            <li class="recommendation-item">
                                <i class="fas fa-arrow-right me-2"></i>
                                ${rec}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    renderTimingCharts() {
        return `
            <div class="timing-charts-section">
                <h4>Performance Visualization</h4>
                <div class="charts-grid">
                    <div class="chart-container">
                        <canvas id="timing-comparison-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="utilization-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="bottleneck-distribution-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="scaling-factors-chart"></canvas>
                    </div>
                </div>
            </div>
        `;
    }

    initializeTimingCharts() {
        this.initializeTimingComparisonChart();
        this.initializeUtilizationChart();
        this.initializeBottleneckDistributionChart();
        this.initializeScalingFactorsChart();
    }

    initializeTimingComparisonChart() {
        const ctx = document.getElementById('timing-comparison-chart');
        if (!ctx) return;

        const timingMatrix = this.timingData.timing_comparison.timing_matrix;
        const operators = this.timingData.timing_comparison.operators;
        const hardwarePlatforms = this.timingData.hardware_platforms;

        const datasets = hardwarePlatforms.map((hw, index) => ({
            label: hw,
            data: operators.map(op => timingMatrix[op][hw].execution_time_ms),
            backgroundColor: this.getChartColor(index, 0.8),
            borderColor: this.getChartColor(index, 1),
            borderWidth: 1
        }));

        this.charts.timingComparison = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: operators,
                datasets: datasets
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Execution Time Comparison by Operator'
                    },
                    legend: {
                        display: true
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Execution Time (ms)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Operators'
                        }
                    }
                }
            }
        });
    }

    initializeUtilizationChart() {
        const ctx = document.getElementById('utilization-chart');
        if (!ctx) return;

        const timingMatrix = this.timingData.timing_comparison.timing_matrix;
        const operators = this.timingData.timing_comparison.operators;
        const hardwarePlatforms = this.timingData.hardware_platforms;

        const datasets = hardwarePlatforms.map((hw, index) => ({
            label: hw,
            data: operators.map(op => timingMatrix[op][hw].utilization_percent),
            backgroundColor: this.getChartColor(index, 0.8),
            borderColor: this.getChartColor(index, 1),
            borderWidth: 1
        }));

        this.charts.utilization = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: operators,
                datasets: datasets
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Hardware Utilization by Operator'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Utilization (%)'
                        }
                    }
                }
            }
        });
    }

    initializeBottleneckDistributionChart() {
        const ctx = document.getElementById('bottleneck-distribution-chart');
        if (!ctx) return;

        const timingMatrix = this.timingData.timing_comparison.timing_matrix;
        const hardwarePlatforms = this.timingData.hardware_platforms;

        const bottleneckData = hardwarePlatforms.map(hw => {
            let computeCount = 0;
            let memoryCount = 0;

            Object.values(timingMatrix).forEach(opTimings => {
                if (opTimings[hw].bottleneck_type === 'compute') {
                    computeCount++;
                } else {
                    memoryCount++;
                }
            });

            return { hw, compute: computeCount, memory: memoryCount };
        });

        this.charts.bottleneckDistribution = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: hardwarePlatforms,
                datasets: [
                    {
                        label: 'Compute-bound',
                        data: bottleneckData.map(d => d.compute),
                        backgroundColor: 'rgba(255, 99, 132, 0.8)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Memory-bound',
                        data: bottleneckData.map(d => d.memory),
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Bottleneck Distribution by Hardware'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Operators'
                        }
                    }
                }
            }
        });
    }

    initializeScalingFactorsChart() {
        const ctx = document.getElementById('scaling-factors-chart');
        if (!ctx) return;

        const scalingAnalysis = this.timingData.scaling_analysis;
        const operators = Object.keys(scalingAnalysis);
        const hardwarePlatforms = this.timingData.hardware_platforms;

        const datasets = hardwarePlatforms.map((hw, index) => ({
            label: hw,
            data: operators.map(op => scalingAnalysis[op].scaling_factors[hw]),
            backgroundColor: this.getChartColor(index, 0.8),
            borderColor: this.getChartColor(index, 1),
            borderWidth: 1
        }));

        this.charts.scalingFactors = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: operators,
                datasets: datasets
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Performance Scaling Factors (Lower is Better)'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Scaling Factor (relative to fastest)'
                        }
                    }
                }
            }
        });
    }

    getChartColor(index, alpha = 1) {
        const colors = [
            `rgba(255, 99, 132, ${alpha})`,
            `rgba(54, 162, 235, ${alpha})`,
            `rgba(255, 205, 86, ${alpha})`,
            `rgba(75, 192, 192, ${alpha})`,
            `rgba(153, 102, 255, ${alpha})`,
            `rgba(255, 159, 64, ${alpha})`,
            `rgba(199, 199, 199, ${alpha})`,
            `rgba(83, 102, 255, ${alpha})`
        ];
        return colors[index % colors.length];
    }

    toggleScalingView(show) {
        const scalingSection = document.getElementById('scaling-section');
        if (scalingSection) {
            scalingSection.style.display = show ? 'block' : 'none';
        }
    }

    exportTimingResults() {
        if (!this.timingData) {
            this.showError('No timing data to export');
            return;
        }

        const exportData = {
            timestamp: new Date().toISOString(),
            hardware_platforms: this.timingData.hardware_platforms,
            timing_comparison: this.timingData.timing_comparison,
            scaling_analysis: this.timingData.scaling_analysis,
            migration_recommendations: this.timingData.migration_recommendations,
            summary_statistics: this.timingData.summary_statistics
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cross-platform-timing-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    showLoading(show) {
        const loader = document.getElementById('timing-analysis-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }

        const analyzeBtn = document.getElementById('analyze-timing-btn');
        if (analyzeBtn) {
            analyzeBtn.disabled = show;
            analyzeBtn.textContent = show ? 'Analyzing...' : 'Analyze Timing';
        }
    }

    showError(message) {
        const errorContainer = document.getElementById('timing-error-container');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-error">
                    <span class="error-message">${message}</span>
                    <button class="close-btn" onclick="this.parentElement.style.display='none'">&times;</button>
                </div>
            `;
            errorContainer.style.display = 'block';
        } else {
            alert(message);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('cross-platform-timing-container')) {
        window.crossPlatformTiming = new CrossPlatformTiming();
        window.crossPlatformTiming.loadOperatorsFromAnalysis();
    }
});

<!DOCTYPE html>
<html>
<head>
    <title>Simple Select2 Test</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Simple Select2 Test</h1>

    <div class="test-section">
        <h3>1. Regular HTML Select (should work)</h3>
        <select id="regularSelect" multiple style="width: 300px; height: 100px;">
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
            <option value="option3">Option 3</option>
        </select>
        <div id="regularResult"></div>
    </div>

    <div class="test-section">
        <h3>2. Select2 Basic (should work if libraries load)</h3>
        <select id="select2Basic" multiple style="width: 300px;">
            <option value="basic1">Basic Option 1</option>
            <option value="basic2">Basic Option 2</option>
            <option value="basic3">Basic Option 3</option>
        </select>
        <div id="select2Result"></div>
    </div>

    <div class="test-section">
        <h3>3. API-populated Select2 (like your app)</h3>
        <select id="apiSelect" multiple style="width: 300px;">
            <!-- Will be populated from API -->
        </select>
        <div id="apiResult"></div>
    </div>

    <div class="test-section">
        <h3>Debug Info</h3>
        <div id="debugInfo"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            console.log('Page loaded');

            // Debug info
            $('#debugInfo').html(`
                jQuery: ${typeof $ !== 'undefined' ? 'Loaded' : 'Not loaded'}<br>
                Select2: ${typeof $.fn.select2 !== 'undefined' ? 'Loaded' : 'Not loaded'}<br>
                User Agent: ${navigator.userAgent}<br>
                Screen: ${screen.width}x${screen.height}<br>
            `);

            // Test 1: Regular select
            $('#regularSelect').on('change', function() {
                const selected = $(this).val() || [];
                $('#regularResult').html('Regular select: ' + selected.join(', '));
            });

            // Test 2: Basic Select2
            try {
                $('#select2Basic').select2({
                    placeholder: 'Select options...',
                    allowClear: true
                });

                $('#select2Basic').on('change', function() {
                    const selected = $(this).val() || [];
                    $('#select2Result').html('Select2 basic: ' + selected.join(', '));
                });

                $('#debugInfo').append('<br>Select2 basic: Initialized successfully');
            } catch (error) {
                $('#debugInfo').append('<br>Select2 basic: Failed - ' + error.message);
            }

            // Test 3: API-populated Select2
            fetch('/api/models/supported')
                .then(response => response.json())
                .then(data => {
                    console.log('API data:', data);

                    const apiSelect = $('#apiSelect');
                    apiSelect.empty();

                    // Add options
                    Object.entries(data.architecture_info).forEach(([arch, info]) => {
                        if (info.examples && info.examples.length > 0) {
                            const optgroup = $(`<optgroup label="${arch.toUpperCase()}">`);
                            info.examples.forEach(model => {
                                optgroup.append(`<option value="${model}">${model}</option>`);
                            });
                            apiSelect.append(optgroup);
                        }
                    });

                    // Initialize Select2
                    apiSelect.select2({
                        placeholder: 'Select models...',
                        allowClear: true
                    });

                    apiSelect.on('change', function() {
                        const selected = $(this).val() || [];
                        $('#apiResult').html('API Select2: ' + selected.join(', '));
                    });

                    $('#debugInfo').append('<br>API Select2: Initialized with ' + Object.keys(data.architecture_info).length + ' architectures');
                })
                .catch(error => {
                    $('#debugInfo').append('<br>API Select2: Failed - ' + error.message);
                });
        });
    </script>
</body>
</html>

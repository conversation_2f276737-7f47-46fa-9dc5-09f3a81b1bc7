/**
 * Multi-Hardware Comparison Interface Styles
 */

/* Hardware Selector Styles */
.hardware-categories {
    margin-bottom: 2rem;
}

.hardware-category {
    margin-bottom: 2rem;
}

.hardware-category h3 {
    color: #333;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e0e0e0;
}

.hardware-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.hardware-card {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    background: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.hardware-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.hardware-card.selected {
    border-color: #007bff;
    background: #f8f9ff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.hardware-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.hardware-checkbox {
    margin-right: 0.5rem;
    transform: scale(1.2);
}

.hardware-name {
    font-weight: 600;
    color: #333;
    margin: 0;
    cursor: pointer;
}

.hardware-specs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.spec-label {
    color: #666;
    font-weight: 500;
}

.spec-value {
    color: #333;
    font-weight: 600;
}

/* Selection Controls */
.selection-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.selection-count {
    color: #666;
    font-weight: 500;
}

/* Comparison Results Styles */
.comparison-results {
    margin-top: 2rem;
}

.comparison-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
}

.comparison-header h2 {
    color: #333;
    margin: 0;
}

/* Summary Insights */
.summary-insights {
    background: #e8f4fd;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.summary-insights h3 {
    color: #0056b3;
    margin-top: 0;
    margin-bottom: 1rem;
}

.insights-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.insight-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #b3d9ff;
    color: #333;
}

.insight-item:last-child {
    border-bottom: none;
}

.insight-item::before {
    content: "💡";
    margin-right: 0.5rem;
}

/* Comparison Table */
.comparison-table-container {
    margin-bottom: 2rem;
}

.comparison-table-container h3 {
    color: #333;
    margin-bottom: 1rem;
}

.table-responsive {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.comparison-table th,
.comparison-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.comparison-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.comparison-table .spec-name {
    font-weight: 600;
    color: #555;
    background: #f8f9fa;
}

.comparison-table .spec-value {
    text-align: center;
    font-weight: 500;
}

.hardware-column {
    background: #007bff !important;
    color: white !important;
    text-align: center;
}

/* Performance Charts */
.performance-charts {
    margin-bottom: 2rem;
}

.performance-charts h3 {
    color: #333;
    margin-bottom: 1rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.chart-container {
    background: #fff;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container canvas {
    max-height: 300px;
}

/* Recommendations Section */
.recommendations-section {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.recommendations-section h3 {
    color: #333;
    margin-top: 0;
    margin-bottom: 1.5rem;
}

.recommendations-section h4 {
    color: #555;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.recommendation-list {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.recommendation-item {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.hardware-name {
    font-weight: 600;
    color: #333;
}

.recommendation-rank {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 600;
}

.recommendation-reasons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.reason-tag {
    background: #e8f4fd;
    color: #0056b3;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Use Case Suitability */
.use-case-suitability {
    margin-bottom: 2rem;
}

.suitability-grid {
    overflow-x: auto;
}

.suitability-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.suitability-table th,
.suitability-table td {
    padding: 0.75rem;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
}

.suitability-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.suitability-table .hardware-name {
    text-align: left;
    font-weight: 600;
}

.suitability-score {
    font-weight: 600;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
}

.suitability-score.excellent {
    background: #d4edda;
    color: #155724;
}

.suitability-score.good {
    background: #d1ecf1;
    color: #0c5460;
}

.suitability-score.fair {
    background: #fff3cd;
    color: #856404;
}

.suitability-score.poor {
    background: #f8d7da;
    color: #721c24;
}

/* Migration Recommendations */
.migration-recommendations {
    margin-bottom: 2rem;
}

.migration-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.migration-item {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    color: #856404;
}

.migration-item::before {
    content: "➡️";
    margin-right: 0.5rem;
}

/* Roofline Comparison */
.roofline-comparison {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.roofline-comparison h3 {
    color: #333;
    margin-top: 0;
    margin-bottom: 1rem;
}

.roofline-container {
    height: 500px;
    margin-bottom: 1rem;
}

.roofline-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.roofline-controls label {
    font-weight: 600;
    color: #333;
}

.roofline-controls select {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #fff;
}

/* Hardware Selection Wizard Styles */
.hardware-wizard-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 1000;
    overflow-y: auto;
}

.wizard-container {
    max-width: 800px;
    margin: 2rem auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.wizard-header {
    background: #007bff;
    color: white;
    padding: 1.5rem;
    border-radius: 8px 8px 0 0;
}

.wizard-header h2 {
    margin: 0;
}

.wizard-progress {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.wizard-progress-container {
    background: #e0e0e0;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.wizard-progress-bar {
    background: #007bff;
    height: 100%;
    transition: width 0.3s ease;
}

.step-indicators {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step-indicator {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.step-indicator.active {
    background: #007bff;
    color: white;
}

.step-indicator.completed {
    background: #28a745;
    color: white;
}

.wizard-content {
    padding: 2rem;
}

.wizard-step {
    display: none;
}

.wizard-step.active {
    display: block;
}

.wizard-step h3 {
    color: #333;
    margin-top: 0;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group input.error,
.form-group select.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.form-group-inline {
    display: flex;
    gap: 1rem;
}

.form-group-inline .form-group {
    flex: 1;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
    width: auto;
}

.requirements-summary {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 1rem;
    margin-top: 1rem;
}

.requirements-summary h4 {
    margin-top: 0;
    color: #333;
}

.wizard-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.wizard-navigation .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-outline {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Recommendation Cards */
.recommendations-list {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.recommendation-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;
    overflow: hidden;
    transition: all 0.3s ease;
}

.recommendation-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recommendation-card.top-recommendation {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.recommendation-card .recommendation-header {
    background: #f8f9fa;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rank-badge {
    background: #007bff;
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.hardware-info h4 {
    margin: 0;
    color: #333;
}

.hardware-score {
    color: #666;
    font-size: 0.9rem;
}

.hardware-cost {
    font-weight: 600;
    color: #28a745;
    font-size: 1.1rem;
}

.recommendation-content {
    padding: 1rem;
}

.reasons h5 {
    color: #333;
    margin-bottom: 0.5rem;
}

.reasons-list {
    list-style: none;
    padding: 0;
    margin: 0 0 1rem 0;
}

.reasons-list li {
    padding: 0.25rem 0;
    color: #555;
}

.reasons-list li::before {
    content: "✓";
    color: #28a745;
    font-weight: 600;
    margin-right: 0.5rem;
}

.pros-cons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.pros h6,
.cons h6 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.pros ul,
.cons ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pro-item {
    color: #28a745;
    padding: 0.25rem 0;
}

.pro-item::before {
    content: "👍";
    margin-right: 0.5rem;
}

.con-item {
    color: #dc3545;
    padding: 0.25rem 0;
}

.con-item::before {
    content: "⚠️";
    margin-right: 0.5rem;
}

.recommendation-actions {
    padding: 1rem;
    background: #f8f9fa;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.no-recommendations {
    text-align: center;
    padding: 2rem;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
}

.no-recommendations h3 {
    color: #856404;
    margin-bottom: 1rem;
}

.alternatives {
    margin: 1.5rem 0;
    text-align: left;
}

.alternatives h4 {
    color: #856404;
    margin-bottom: 0.5rem;
}

.alternatives ul {
    color: #856404;
}

/* Loading and Error States */
.comparison-loader,
.wizard-loader {
    text-align: center;
    padding: 2rem;
}

.loader-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    position: relative;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.close-btn {
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: inherit;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hardware-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .comparison-table {
        font-size: 0.9rem;
    }

    .wizard-container {
        margin: 1rem;
        max-width: none;
    }

    .form-group-inline {
        flex-direction: column;
    }

    .pros-cons {
        grid-template-columns: 1fr;
    }

    .recommendation-actions {
        flex-direction: column;
    }

    .wizard-navigation {
        flex-direction: column;
        gap: 1rem;
    }
}
/
* Cross-Platform Timing Analysis Styles */
.operator-selection {
    margin-bottom: 1.5rem;
}

.operator-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.operator-card {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 1rem;
    background: #fff;
    transition: all 0.3s ease;
}

.operator-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.operator-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.operator-header input[type="checkbox"] {
    margin-right: 0.5rem;
    transform: scale(1.1);
}

.operator-name {
    font-weight: 600;
    color: #333;
    margin: 0;
    cursor: pointer;
}

.operator-details {
    font-size: 0.9rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.detail-label {
    color: #666;
    text-transform: capitalize;
}

.detail-value {
    color: #333;
    font-weight: 500;
}

/* Timing Results Styles */
.timing-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
}

.timing-results-header h3 {
    color: #333;
    margin: 0;
}

.results-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.timing-results-content {
    display: grid;
    gap: 2rem;
}

/* Timing Matrix Styles */
.timing-matrix-section {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timing-matrix-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.timing-matrix-table th,
.timing-matrix-table td {
    padding: 0.75rem;
    text-align: center;
    border: 1px solid #e0e0e0;
}

.timing-matrix-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.timing-matrix-table .operator-name {
    text-align: left;
    font-weight: 600;
    background: #f8f9fa;
}

.timing-cell {
    position: relative;
}

.timing-cell.best-timing {
    background: #d4edda;
    border-color: #28a745;
}

.timing-cell.compute-bound {
    border-left: 4px solid #ff6b6b;
}

.timing-cell.memory-bound {
    border-left: 4px solid #4ecdc4;
}

.timing-value {
    font-weight: 600;
    color: #333;
}

.timing-details {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.bottleneck-type {
    display: inline-block;
    padding: 0.1rem 0.3rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.timing-cell.compute-bound .bottleneck-type {
    background: #ffe6e6;
    color: #d63031;
}

.timing-cell.memory-bound .bottleneck-type {
    background: #e6f7f7;
    color: #00b894;
}

.utilization {
    margin-left: 0.5rem;
    font-weight: 500;
}

.best-hardware {
    font-weight: 600;
    color: #28a745;
}

.performance-range {
    font-size: 0.9rem;
}

.range-ratio {
    font-size: 0.8rem;
    color: #666;
    font-weight: 600;
}

/* Performance Scaling Styles */
.performance-scaling-section {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scaling-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.scaling-card {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 1rem;
    background: #f8f9fa;
}

.scaling-card h5 {
    color: #333;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.scaling-info {
    display: grid;
    gap: 1rem;
}

.fastest-hardware {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #d4edda;
    border-radius: 4px;
    color: #155724;
}

.fastest-time {
    font-weight: 600;
}

.scaling-factors {
    font-size: 0.9rem;
}

.factors-list {
    margin-top: 0.5rem;
    display: grid;
    gap: 0.25rem;
}

.factor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background: #fff;
    border-radius: 3px;
    border: 1px solid #e0e0e0;
}

.factor-item.best-factor {
    background: #d4edda;
    border-color: #28a745;
}

.factor-value {
    font-weight: 600;
}

.factor-value.best-factor {
    color: #28a745;
}

/* Bottleneck Analysis Styles */
.bottleneck-analysis-section {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bottleneck-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.bottleneck-card {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 1rem;
    background: #f8f9fa;
}

.bottleneck-card h5 {
    color: #333;
    margin-bottom: 1rem;
    text-align: center;
}

.bottleneck-summary {
    text-align: center;
    margin-bottom: 1rem;
}

.overall-bottleneck {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.overall-bottleneck.compute-bottleneck {
    background: #ffe6e6;
    color: #d63031;
    border: 2px solid #ff6b6b;
}

.overall-bottleneck.memory-bottleneck {
    background: #e6f7f7;
    color: #00b894;
    border: 2px solid #4ecdc4;
}

.bottleneck-details {
    display: grid;
    gap: 1rem;
}

.compute-bound-ops,
.memory-bound-ops {
    padding: 0.75rem;
    border-radius: 4px;
}

.compute-bound-ops {
    background: #fff5f5;
    border: 1px solid #ffe6e6;
}

.memory-bound-ops {
    background: #f0fffe;
    border: 1px solid #e6f7f7;
}

.ops-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.op-tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.op-tag.compute-tag {
    background: #ff6b6b;
    color: white;
}

.op-tag.memory-tag {
    background: #4ecdc4;
    color: white;
}

/* Migration Recommendations Styles */
.migration-recommendations-section {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-statistics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.recommendations-list h5 {
    color: #333;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.recommendation-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recommendation-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: #e8f4fd;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    color: #0056b3;
    display: flex;
    align-items: center;
}

.recommendation-item i {
    color: #007bff;
}

/* Timing Charts Styles */
.timing-charts-section {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timing-charts-section h4 {
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.timing-charts-section .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.timing-charts-section .chart-container {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 1rem;
}

.timing-charts-section .chart-container canvas {
    max-height: 300px;
}

/* Responsive Design for Timing Analysis */
@media (max-width: 768px) {
    .operator-grid {
        grid-template-columns: 1fr;
    }

    .scaling-grid,
    .bottleneck-grid {
        grid-template-columns: 1fr;
    }

    .summary-statistics {
        grid-template-columns: repeat(2, 1fr);
    }

    .timing-results-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .results-controls {
        justify-content: center;
    }

    .timing-charts-section .charts-grid {
        grid-template-columns: 1fr;
    }

    .timing-matrix-table {
        font-size: 0.8rem;
    }

    .timing-matrix-table th,
    .timing-matrix-table td {
        padding: 0.5rem;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preset Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h3>Preset Button Test</h3>

        <!-- Test preset buttons -->
        <div class="mb-4">
            <button type="button" class="btn btn-outline-primary btn-sm" data-preset="dense-comparison">
                Dense Models Comparison
            </button>
            <button type="button" class="btn btn-outline-primary btn-sm" data-preset="moe-comparison">
                MoE Models Comparison
            </button>
        </div>

        <!-- Model select for testing -->
        <select id="modelSelect" class="form-select" multiple="multiple">
            <option value="meta-llama/Meta-Llama-3-8B-Instruct">Meta-Llama-3-8B-Instruct</option>
            <option value="meta-llama/Meta-Llama-3-70B-Instruct">Meta-Llama-3-70B-Instruct</option>
            <option value="deepseek-ai/DeepSeek-V3">DeepSeek-V3</option>
        </select>

        <div id="output" class="mt-3"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        // Simple test script
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded');

            // Initialize Select2
            $('#modelSelect').select2();

            // Test preset button binding
            const presetButtons = document.querySelectorAll('[data-preset]');
            console.log('Found preset buttons:', presetButtons.length);

            presetButtons.forEach((btn, index) => {
                console.log(`Preset button ${index}:`, btn.dataset.preset);
                btn.addEventListener('click', (e) => {
                    console.log('Preset button clicked:', e.target.dataset.preset);
                    const output = document.getElementById('output');
                    output.innerHTML = `<div class="alert alert-success">Clicked preset: ${e.target.dataset.preset}</div>`;
                });
            });
        });
    </script>
</body>
</html>

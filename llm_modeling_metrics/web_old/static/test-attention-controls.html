<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attention Performance Controls Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Chart.js Zoom Plugin -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>Attention Performance Controls Test</h1>
        <p class="text-muted">Testing the enhanced interactive parameter controls for attention performance visualization.</p>
        
        <!-- Test Container -->
        <div id="attentionPerformanceContainer"></div>
        
        <!-- Test Results -->
        <div class="mt-4">
            <h3>Test Results</h3>
            <div id="testResults" class="alert alert-info">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                Initializing controls...
            </div>
        </div>
        
        <!-- Control Values Display -->
        <div class="mt-3">
            <h4>Current Control Values</h4>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Sequence Length Range:</strong>
                            <div id="currentSequenceRange">-</div>
                        </div>
                        <div class="col-md-4">
                            <strong>FlashAttention:</strong>
                            <div id="currentFlashAttention">-</div>
                        </div>
                        <div class="col-md-4">
                            <strong>Attention Types:</strong>
                            <div id="currentAttentionTypes">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Attention Performance Visualizer -->
    <script src="js/attention-performance.js"></script>
    
    <script>
        // Test the attention performance controls
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Initialize the attention performance visualizer
                const visualizer = new AttentionPerformanceVisualizer('attentionPerformanceContainer', {
                    apiBaseUrl: '/api',
                    showControls: true,
                    showLegend: true,
                    enableZoom: true,
                    enablePan: true,
                    onParameterChange: function(parameters) {
                        console.log('Parameter change:', parameters);
                        updateControlValues(parameters);
                    }
                });
                
                // Set some test hardware and models
                visualizer.selectedHardware = [
                    { id: 'h100', name: 'NVIDIA H100' },
                    { id: 'a800', name: 'NVIDIA A800' }
                ];
                visualizer.selectedModels = [
                    'meta-llama/Meta-Llama-3-8B-Instruct',
                    'deepseek-ai/DeepSeek-V3'
                ];
                
                // Update test results
                document.getElementById('testResults').innerHTML = `
                    <i class="fas fa-check-circle text-success me-2"></i>
                    Controls initialized successfully! Try adjusting the parameters above.
                `;
                document.getElementById('testResults').className = 'alert alert-success';
                
                // Initial values display
                updateControlValues({
                    sequenceLengthRange: visualizer.sequenceLengthRange,
                    flashAttentionEnabled: visualizer.flashAttentionEnabled,
                    selectedAttentionTypes: visualizer.selectedAttentionTypes
                });
                
            } catch (error) {
                console.error('Failed to initialize controls:', error);
                document.getElementById('testResults').innerHTML = `
                    <i class="fas fa-exclamation-circle text-danger me-2"></i>
                    Failed to initialize controls: ${error.message}
                `;
                document.getElementById('testResults').className = 'alert alert-danger';
            }
        });
        
        function updateControlValues(parameters) {
            if (parameters.sequenceLengthRange) {
                const minK = Math.round(parameters.sequenceLengthRange.min / 1000);
                const maxK = Math.round(parameters.sequenceLengthRange.max / 1000);
                document.getElementById('currentSequenceRange').textContent = `${minK}K - ${maxK}K tokens`;
            }
            
            if (typeof parameters.flashAttentionEnabled !== 'undefined') {
                document.getElementById('currentFlashAttention').innerHTML = 
                    parameters.flashAttentionEnabled ? 
                    '<span class="badge bg-success">Enabled</span>' : 
                    '<span class="badge bg-secondary">Disabled</span>';
            }
            
            if (parameters.selectedAttentionTypes) {
                document.getElementById('currentAttentionTypes').textContent = 
                    parameters.selectedAttentionTypes.join(', ') || 'None selected';
            }
        }
    </script>
</body>
</html>
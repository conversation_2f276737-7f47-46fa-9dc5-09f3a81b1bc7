"""
Performance monitoring and optimization utilities for LLM modeling metrics.

This module provides tools for monitoring performance, memory usage, and
implementing async processing for concurrent model analysis.
"""

import asyncio
import functools
import gc
import logging
import threading
import time
import weakref
from concurrent.futures import Process<PERSON>oolExecutor, ThreadPoolExecutor, as_completed
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Awaitable, Callable, Dict, List, Optional, TypeVar, Union

import psutil

T = TypeVar("T")
F = TypeVar("F", bound=Callable[..., Any])


@dataclass
class PerformanceMetrics:
    """Performance metrics for a computation or operation."""

    operation_name: str
    start_time: float
    end_time: float
    duration: float
    memory_before: int
    memory_after: int
    memory_peak: int
    cpu_percent: float
    thread_count: int
    success: bool = True
    error_message: Optional[str] = None

    @property
    def memory_delta(self) -> int:
        """Memory change during operation."""
        return self.memory_after - self.memory_before

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "operation_name": self.operation_name,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "memory_before": self.memory_before,
            "memory_after": self.memory_after,
            "memory_peak": self.memory_peak,
            "memory_delta": self.memory_delta,
            "cpu_percent": self.cpu_percent,
            "thread_count": self.thread_count,
            "success": self.success,
            "error_message": self.error_message,
        }


@dataclass
class SystemMetrics:
    """System-wide performance metrics."""

    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_available: int
    memory_used: int
    disk_usage_percent: float
    active_threads: int
    process_count: int

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_available": self.memory_available,
            "memory_used": self.memory_used,
            "disk_usage_percent": self.disk_usage_percent,
            "active_threads": self.active_threads,
            "process_count": self.process_count,
        }


class PerformanceMonitor:
    """Monitor and track performance metrics for operations."""

    def __init__(self, max_history: int = 1000):
        """
        Initialize performance monitor.

        Args:
            max_history: Maximum number of performance records to keep
        """
        self.max_history = max_history
        self._metrics_history: List[PerformanceMetrics] = []
        self._system_metrics_history: List[SystemMetrics] = []
        self._lock = threading.RLock()
        self._monitoring_active = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()

        # Setup logging
        self.logger = logging.getLogger(__name__)

    def start_system_monitoring(self, interval: float = 5.0) -> None:
        """
        Start continuous system monitoring.

        Args:
            interval: Monitoring interval in seconds
        """
        if self._monitoring_active:
            return

        self._monitoring_active = True
        self._stop_event.clear()

        def monitor_worker():
            while not self._stop_event.wait(interval):
                try:
                    metrics = self._collect_system_metrics()
                    with self._lock:
                        self._system_metrics_history.append(metrics)
                        if len(self._system_metrics_history) > self.max_history:
                            self._system_metrics_history.pop(0)
                except Exception as e:
                    self.logger.error(f"Error collecting system metrics: {e}")

        self._monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        self._monitor_thread.start()

    def stop_system_monitoring(self) -> None:
        """Stop continuous system monitoring."""
        if not self._monitoring_active:
            return

        self._monitoring_active = False
        self._stop_event.set()

        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)

    @contextmanager
    def monitor_operation(self, operation_name: str):
        """
        Context manager to monitor an operation's performance.

        Args:
            operation_name: Name of the operation being monitored
        """
        # Collect initial metrics
        start_time = time.time()
        memory_before = self._get_memory_usage()
        cpu_before = psutil.cpu_percent()
        thread_count = threading.active_count()

        # Track peak memory during operation
        peak_memory = memory_before

        def update_peak_memory():
            nonlocal peak_memory
            current_memory = self._get_memory_usage()
            peak_memory = max(peak_memory, current_memory)

        # Start memory tracking thread
        memory_tracker = threading.Thread(
            target=lambda: [
                update_peak_memory() or time.sleep(0.1) for _ in range(100)
            ],
            daemon=True,
        )
        memory_tracker.start()

        success = True
        error_message = None

        try:
            yield
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            # Collect final metrics
            end_time = time.time()
            memory_after = self._get_memory_usage()
            cpu_after = psutil.cpu_percent()

            # Wait for memory tracker to finish
            memory_tracker.join(timeout=1)

            metrics = PerformanceMetrics(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                memory_before=memory_before,
                memory_after=memory_after,
                memory_peak=peak_memory,
                cpu_percent=(cpu_before + cpu_after) / 2,
                thread_count=thread_count,
                success=success,
                error_message=error_message,
            )

            with self._lock:
                self._metrics_history.append(metrics)
                if len(self._metrics_history) > self.max_history:
                    self._metrics_history.pop(0)

            # Log performance metrics
            self.logger.info(
                f"Operation '{operation_name}' completed in {metrics.duration:.3f}s, "
                f"memory delta: {metrics.memory_delta / 1024 / 1024:.1f}MB"
            )

    def get_operation_metrics(
        self, operation_name: Optional[str] = None
    ) -> List[PerformanceMetrics]:
        """
        Get performance metrics for operations.

        Args:
            operation_name: Filter by operation name, or None for all

        Returns:
            List of performance metrics
        """
        with self._lock:
            if operation_name is None:
                return self._metrics_history.copy()
            return [
                m for m in self._metrics_history if m.operation_name == operation_name
            ]

    def get_system_metrics(
        self, since: Optional[datetime] = None
    ) -> List[SystemMetrics]:
        """
        Get system metrics.

        Args:
            since: Only return metrics after this timestamp

        Returns:
            List of system metrics
        """
        with self._lock:
            if since is None:
                return self._system_metrics_history.copy()
            return [m for m in self._system_metrics_history if m.timestamp >= since]

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get summary of performance metrics."""
        with self._lock:
            if not self._metrics_history:
                return {"total_operations": 0}

            # Group by operation name
            operations = {}
            for metric in self._metrics_history:
                name = metric.operation_name
                if name not in operations:
                    operations[name] = []
                operations[name].append(metric)

            # Calculate statistics for each operation
            summary = {"operations": {}}
            for name, metrics in operations.items():
                durations = [m.duration for m in metrics]
                memory_deltas = [m.memory_delta for m in metrics]
                success_count = sum(1 for m in metrics if m.success)

                summary["operations"][name] = {
                    "count": len(metrics),
                    "success_rate": success_count / len(metrics),
                    "avg_duration": sum(durations) / len(durations),
                    "min_duration": min(durations),
                    "max_duration": max(durations),
                    "avg_memory_delta": sum(memory_deltas) / len(memory_deltas),
                    "max_memory_delta": max(memory_deltas),
                }

            summary["total_operations"] = len(self._metrics_history)
            summary["system_monitoring_active"] = self._monitoring_active

            return summary

    def clear_history(self) -> None:
        """Clear all performance history."""
        with self._lock:
            self._metrics_history.clear()
            self._system_metrics_history.clear()

    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect current system metrics."""
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage("/")

        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=psutil.cpu_percent(),
            memory_percent=memory.percent,
            memory_available=memory.available,
            memory_used=memory.used,
            disk_usage_percent=disk.percent,
            active_threads=threading.active_count(),
            process_count=len(psutil.pids()),
        )

    def _get_memory_usage(self) -> int:
        """Get current process memory usage in bytes."""
        process = psutil.Process()
        return process.memory_info().rss

    def __del__(self):
        """Cleanup when monitor is destroyed."""
        self.stop_system_monitoring()


class AsyncModelProcessor:
    """Async processor for concurrent model analysis."""

    def __init__(self, max_workers: int = 4, use_processes: bool = False):
        """
        Initialize async processor.

        Args:
            max_workers: Maximum number of concurrent workers
            use_processes: Whether to use processes instead of threads
        """
        self.max_workers = max_workers
        self.use_processes = use_processes
        self._executor = None
        self._monitor = PerformanceMonitor()

    async def process_models_async(
        self, model_names: List[str], analysis_func: Callable[[str], T], **kwargs
    ) -> Dict[str, Union[T, Exception]]:
        """
        Process multiple models concurrently.

        Args:
            model_names: List of model names to process
            analysis_func: Function to analyze each model
            **kwargs: Additional arguments for analysis_func

        Returns:
            Dictionary mapping model names to results or exceptions
        """
        with self._monitor.monitor_operation(
            f"async_process_{len(model_names)}_models"
        ):
            loop = asyncio.get_event_loop()

            # Create executor if needed
            if self._executor is None:
                if self.use_processes:
                    self._executor = ProcessPoolExecutor(max_workers=self.max_workers)
                else:
                    self._executor = ThreadPoolExecutor(max_workers=self.max_workers)

            # Submit all tasks
            futures = {}
            for model_name in model_names:
                future = loop.run_in_executor(
                    self._executor,
                    functools.partial(analysis_func, model_name, **kwargs),
                )
                futures[model_name] = future

            # Collect results
            results = {}
            for model_name, future in futures.items():
                try:
                    result = await future
                    results[model_name] = result
                except Exception as e:
                    results[model_name] = e

            return results

    def process_models_sync(
        self, model_names: List[str], analysis_func: Callable[[str], T], **kwargs
    ) -> Dict[str, Union[T, Exception]]:
        """
        Process multiple models concurrently (synchronous interface).

        Args:
            model_names: List of model names to process
            analysis_func: Function to analyze each model
            **kwargs: Additional arguments for analysis_func

        Returns:
            Dictionary mapping model names to results or exceptions
        """
        with self._monitor.monitor_operation(f"sync_process_{len(model_names)}_models"):
            # Create executor if needed
            if self._executor is None:
                if self.use_processes:
                    self._executor = ProcessPoolExecutor(max_workers=self.max_workers)
                else:
                    self._executor = ThreadPoolExecutor(max_workers=self.max_workers)

            # Submit all tasks
            future_to_model = {}
            for model_name in model_names:
                future = self._executor.submit(analysis_func, model_name, **kwargs)
                future_to_model[future] = model_name

            # Collect results
            results = {}
            for future in as_completed(future_to_model):
                model_name = future_to_model[future]
                try:
                    result = future.result()
                    results[model_name] = result
                except Exception as e:
                    results[model_name] = e

            return results

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for async processing."""
        return self._monitor.get_performance_summary()

    def cleanup(self) -> None:
        """Cleanup executor and resources."""
        if self._executor:
            self._executor.shutdown(wait=True)
            self._executor = None
        self._monitor.stop_system_monitoring()

    def __del__(self):
        """Cleanup when processor is destroyed."""
        self.cleanup()


class MemoryManager:
    """Memory management utilities for large computations."""

    def __init__(self, memory_threshold_mb: int = 1000):
        """
        Initialize memory manager.

        Args:
            memory_threshold_mb: Memory threshold in MB for triggering cleanup
        """
        self.memory_threshold_mb = memory_threshold_mb
        self.memory_threshold_bytes = memory_threshold_mb * 1024 * 1024
        self._weak_refs: List[weakref.ref] = []
        self._lock = threading.RLock()

    def register_object(self, obj: Any) -> None:
        """Register an object for memory management."""
        with self._lock:
            self._weak_refs.append(weakref.ref(obj))

    def check_memory_usage(self) -> Dict[str, Any]:
        """Check current memory usage and return statistics."""
        process = psutil.Process()
        memory_info = process.memory_info()

        return {
            "rss_mb": memory_info.rss / 1024 / 1024,
            "vms_mb": memory_info.vms / 1024 / 1024,
            "percent": process.memory_percent(),
            "threshold_mb": self.memory_threshold_mb,
            "above_threshold": memory_info.rss > self.memory_threshold_bytes,
        }

    def force_garbage_collection(self) -> Dict[str, int]:
        """Force garbage collection and return statistics."""
        # Clean up weak references
        with self._lock:
            self._weak_refs = [ref for ref in self._weak_refs if ref() is not None]

        # Force garbage collection
        collected = {}
        for generation in range(3):
            collected[f"generation_{generation}"] = gc.collect(generation)

        # Additional cleanup
        gc.collect()

        return collected

    @contextmanager
    def memory_limit_context(self, operation_name: str = "operation"):
        """
        Context manager that monitors memory usage and triggers cleanup if needed.

        Args:
            operation_name: Name of the operation for logging
        """
        initial_memory = self.check_memory_usage()

        try:
            yield
        finally:
            final_memory = self.check_memory_usage()

            # Check if we exceeded threshold
            if final_memory["above_threshold"]:
                logging.warning(
                    f"Memory usage after {operation_name}: {final_memory['rss_mb']:.1f}MB "
                    f"(threshold: {self.memory_threshold_mb}MB)"
                )

                # Force cleanup
                collected = self.force_garbage_collection()
                after_cleanup = self.check_memory_usage()

                logging.info(
                    f"Garbage collection freed objects: {sum(collected.values())}, "
                    f"memory after cleanup: {after_cleanup['rss_mb']:.1f}MB"
                )


def performance_profiler(func: F) -> F:
    """
    Decorator to profile function performance.

    Args:
        func: Function to profile

    Returns:
        Wrapped function with performance monitoring
    """
    monitor = PerformanceMonitor()

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        operation_name = f"{func.__module__}.{func.__name__}"
        with monitor.monitor_operation(operation_name):
            return func(*args, **kwargs)

    # Attach monitor to function for access to metrics
    wrapper._performance_monitor = monitor
    return wrapper


def memory_optimized(memory_threshold_mb: int = 500):
    """
    Decorator to add memory optimization to functions.

    Args:
        memory_threshold_mb: Memory threshold for triggering cleanup
    """

    def decorator(func: F) -> F:
        memory_manager = MemoryManager(memory_threshold_mb)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation_name = f"{func.__module__}.{func.__name__}"
            with memory_manager.memory_limit_context(operation_name):
                return func(*args, **kwargs)

        # Attach memory manager to function
        wrapper._memory_manager = memory_manager
        return wrapper

    return decorator


# Global instances
_global_monitor = None
_global_processor = None
_global_memory_manager = None
_global_lock = threading.Lock()


def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance."""
    global _global_monitor

    if _global_monitor is None:
        with _global_lock:
            if _global_monitor is None:
                _global_monitor = PerformanceMonitor()
                _global_monitor.start_system_monitoring()

    return _global_monitor


def get_async_processor() -> AsyncModelProcessor:
    """Get global async processor instance."""
    global _global_processor

    if _global_processor is None:
        with _global_lock:
            if _global_processor is None:
                _global_processor = AsyncModelProcessor()

    return _global_processor


def get_memory_manager() -> MemoryManager:
    """Get global memory manager instance."""
    global _global_memory_manager

    if _global_memory_manager is None:
        with _global_lock:
            if _global_memory_manager is None:
                _global_memory_manager = MemoryManager()

    return _global_memory_manager

"""
Table generation utilities for creating operator analysis tables similar to Table 2 in the paper.
"""

from typing import Any, Dict, List, Optional

import pandas as pd

from ..core.base_model import ParallelConfig
from ..models.dense_model import DenseModel
from ..models.moe_model import MoEModel


def generate_operator_table(
    model,
    batch_size: int = 1,
    sequence_length: int = 2048,
    hardware_name: str = "nvidia_h100_sxm5",
    parallel_config: Optional[ParallelConfig] = None,
    format_type: str = "pandas",
) -> Any:
    """
    Generate an operator analysis table similar to Table 2 in the paper.

    Args:
        model: Model instance (DenseModel or MoEModel)
        batch_size: Batch size for analysis
        sequence_length: Sequence length
        hardware_name: Hardware specification name
        parallel_config: Parallel configuration (optional)
        format_type: Output format ('pandas', 'dict', 'markdown')

    Returns:
        Formatted table based on format_type
    """
    # Get operator breakdown
    if hasattr(model, "get_operator_breakdown"):
        if isinstance(model, MoEModel) and parallel_config:
            breakdown = model.get_operator_breakdown(
                batch_size, sequence_length, hardware_name, parallel_config
            )
        else:
            breakdown = model.get_operator_breakdown(
                batch_size, sequence_length, hardware_name
            )
    else:
        raise ValueError("Model does not support operator breakdown analysis")

    # Extract data for table
    table_data = []

    for op_name, op_data in breakdown["operators"].items():
        metrics = op_data["metrics_total"]

        # Convert to more readable units
        flops_tflops = metrics.flops / 1e12
        memory_capacity_gb = metrics.memory_capacity_bytes / (1024**3)
        memory_movement_gbps = metrics.memory_movement_bytes / 1e9
        time_ms = metrics.execution_time_ms

        row = {
            "Operator": op_name.replace("_", " ").title(),
            "FLOPs (TFLOPS)": f"{flops_tflops:.3f}",
            "Memory Capacity (GB)": f"{memory_capacity_gb:.3f}",
            "Memory Movement (GB)": f"{memory_movement_gbps:.1f}",
            "Time (ms)": f"{time_ms:.2f}",
            "Arithmetic Intensity": f"{metrics.arithmetic_intensity:.2f}",
            "Utilization (%)": f"{metrics.utilization:.1f}",
        }

        # Add MoE-specific details if available
        if op_name == "moe" and "moe_details" in op_data:
            moe_details = op_data["moe_details"]
            row["Expert Util Rate"] = f"{moe_details['expert_utilization_rate']:.2f}"
            row["Active/Total Experts"] = (
                f"{moe_details['experts_per_token']}/{moe_details['num_experts']}"
            )

        table_data.append(row)

    # Add totals row
    totals = breakdown["totals"]
    total_row = {
        "Operator": "TOTAL",
        "FLOPs (TFLOPS)": f"{totals['flops'] / 1e12:.3f}",
        "Memory Capacity (GB)": f"{totals['memory_capacity_bytes'] / (1024**3):.3f}",
        "Memory Movement (GB)": f"{totals['memory_movement_bytes'] / 1e9:.1f}",
        "Time (ms)": f"{totals['execution_time_ms']:.2f}",
        "Arithmetic Intensity": f"{totals['arithmetic_intensity']:.2f}",
        "Utilization (%)": f"{totals.get('throughput_tokens_per_sec', 0) / 1000:.1f}",  # Throughput as utilization proxy
    }
    table_data.append(total_row)

    # Format output
    if format_type == "pandas":
        return pd.DataFrame(table_data)
    elif format_type == "dict":
        return {
            "table_data": table_data,
            "metadata": {
                "model_config": breakdown["model_config"],
                "hardware": breakdown["hardware"].name,
                "parallel_config": parallel_config,
            },
        }
    elif format_type == "markdown":
        return _format_as_markdown(table_data, breakdown)
    else:
        raise ValueError(f"Unsupported format_type: {format_type}")


def generate_roofline_table(
    model,
    batch_size: int = 1,
    sequence_length: int = 2048,
    hardware_name: str = "nvidia_h100_sxm5",
    parallel_config: Optional[ParallelConfig] = None,
    format_type: str = "pandas",
) -> Any:
    """
    Generate a roofline analysis table.

    Args:
        model: Model instance
        batch_size: Batch size
        sequence_length: Sequence length
        hardware_name: Hardware specification
        parallel_config: Parallel configuration
        format_type: Output format

    Returns:
        Formatted roofline analysis table
    """
    # Get roofline analysis
    if hasattr(model, "get_roofline_analysis"):
        if isinstance(model, MoEModel) and parallel_config:
            roofline = model.get_roofline_analysis(
                batch_size, sequence_length, hardware_name, parallel_config
            )
        else:
            roofline = model.get_roofline_analysis(
                batch_size, sequence_length, hardware_name
            )
    else:
        raise ValueError("Model does not support roofline analysis")

    # Extract data for table
    table_data = []

    for op_name, op_data in roofline["operators"].items():
        row = {
            "Operator": op_name.replace("_", " ").title(),
            "Arithmetic Intensity": f"{op_data['arithmetic_intensity']:.2f}",
            "Achieved GFLOPS": f"{op_data['achieved_gflops']:.1f}",
            "Memory BW Util (%)": f"{op_data['memory_bandwidth_utilization']:.1f}",
            "Compute Util (%)": f"{op_data['compute_utilization']:.1f}",
            "Bottleneck": op_data["bottleneck"].title(),
            "Compute Bound": "Yes" if op_data["is_compute_bound"] else "No",
        }
        table_data.append(row)

    # Add model point
    model_point = roofline["model_point"]
    model_row = {
        "Operator": "MODEL OVERALL",
        "Arithmetic Intensity": f"{model_point['arithmetic_intensity']:.2f}",
        "Achieved GFLOPS": f"{model_point['achieved_gflops']:.1f}",
        "Memory BW Util (%)": f"{model_point.get('memory_bandwidth_utilization', 0):.1f}",
        "Compute Util (%)": f"{model_point['efficiency']:.1f}",
        "Bottleneck": model_point["bottleneck"].title(),
        "Compute Bound": "Yes" if model_point["is_compute_bound"] else "No",
    }
    table_data.append(model_row)

    # Format output
    if format_type == "pandas":
        return pd.DataFrame(table_data)
    elif format_type == "dict":
        return {
            "table_data": table_data,
            "metadata": {
                "hardware": roofline["hardware"],
                "model_point": model_point,
                "moe_analysis": roofline.get("moe_analysis", {}),
            },
        }
    elif format_type == "markdown":
        return _format_roofline_as_markdown(table_data, roofline)
    else:
        raise ValueError(f"Unsupported format_type: {format_type}")


def compare_models_table(
    models: List[Any],
    model_names: List[str],
    batch_size: int = 1,
    sequence_length: int = 2048,
    hardware_name: str = "nvidia_h100_sxm5",
    format_type: str = "pandas",
) -> Any:
    """
    Generate a comparison table between multiple models.

    Args:
        models: List of model instances
        model_names: List of model names for comparison
        batch_size: Batch size
        sequence_length: Sequence length
        hardware_name: Hardware specification
        format_type: Output format

    Returns:
        Comparison table
    """
    comparison_data = []

    for model, name in zip(models, model_names):
        # Get basic metrics
        if hasattr(model, "get_operator_breakdown"):
            breakdown = model.get_operator_breakdown(
                batch_size, sequence_length, hardware_name
            )
            totals = breakdown["totals"]
            config = breakdown["model_config"]

            row = {
                "Model": name,
                "Parameters (B)": f"{model.get_total_params() / 1e9:.1f}",
                "Total FLOPs (TFLOPS)": f"{totals['flops'] / 1e12:.3f}",
                "Memory Capacity (GB)": f"{totals['memory_capacity_bytes'] / (1024**3):.1f}",
                "Memory Movement (GB)": f"{totals['memory_movement_bytes'] / (1024**3):.1f}",
                "Throughput (tokens/s)": f"{totals.get('throughput_tokens_per_sec', 0):.0f}",
                "Arithmetic Intensity": f"{totals['arithmetic_intensity']:.2f}",
                "Hidden Size": config["hidden_size"],
                "Layers": config["num_layers"],
                "Heads": config["num_heads"],
            }

            # Add MoE-specific metrics if available
            if isinstance(model, MoEModel):
                row["Experts"] = (
                    f"{config.get('n_routed_experts', 0)}+{config.get('n_shared_experts', 0)}"
                )
                row["Active Params (B)"] = (
                    f"{model.compute_active_params_per_token() / 1e9:.1f}"
                )
                row["Param Efficiency"] = (
                    f"{(model.compute_active_params_per_token() / model.get_total_params()) * 100:.1f}%"
                )

            comparison_data.append(row)

    # Format output
    if format_type == "pandas":
        return pd.DataFrame(comparison_data)
    elif format_type == "dict":
        return {"comparison_data": comparison_data}
    elif format_type == "markdown":
        return _format_comparison_as_markdown(comparison_data)
    else:
        raise ValueError(f"Unsupported format_type: {format_type}")


def _format_as_markdown(table_data: List[Dict], breakdown: Dict) -> str:
    """Format operator table as markdown."""
    if not table_data:
        return "No data available"

    # Get headers
    headers = list(table_data[0].keys())

    # Create markdown table
    md_lines = []
    md_lines.append("# Operator Analysis Table")
    md_lines.append("")
    md_lines.append(f"**Hardware:** {breakdown['hardware'].name}")
    md_lines.append(
        f"**Model Configuration:** {breakdown['model_config']['hidden_size']}H, {breakdown['model_config']['num_layers']}L"
    )
    md_lines.append(
        f"**Batch Size:** {breakdown['model_config']['batch_size']}, **Sequence Length:** {breakdown['model_config']['sequence_length']}"
    )
    md_lines.append("")

    # Table header
    header_line = "| " + " | ".join(headers) + " |"
    separator_line = "| " + " | ".join(["---"] * len(headers)) + " |"

    md_lines.append(header_line)
    md_lines.append(separator_line)

    # Table rows
    for row in table_data:
        row_line = "| " + " | ".join(str(row[h]) for h in headers) + " |"
        md_lines.append(row_line)

    return "\n".join(md_lines)


def _format_roofline_as_markdown(table_data: List[Dict], roofline: Dict) -> str:
    """Format roofline table as markdown."""
    if not table_data:
        return "No data available"

    headers = list(table_data[0].keys())

    md_lines = []
    md_lines.append("# Roofline Analysis Table")
    md_lines.append("")
    md_lines.append(f"**Hardware:** {roofline['hardware']['name']}")
    md_lines.append(
        f"**Peak Performance:** {roofline['hardware']['peak_flops'].get('fp16', 'N/A')} TFLOPS (FP16)"
    )
    md_lines.append(
        f"**Memory Bandwidth:** {roofline['hardware']['memory_bandwidth_gbps']} GB/s"
    )
    md_lines.append("")

    # Table
    header_line = "| " + " | ".join(headers) + " |"
    separator_line = "| " + " | ".join(["---"] * len(headers)) + " |"

    md_lines.append(header_line)
    md_lines.append(separator_line)

    for row in table_data:
        row_line = "| " + " | ".join(str(row[h]) for h in headers) + " |"
        md_lines.append(row_line)

    return "\n".join(md_lines)


def _format_comparison_as_markdown(comparison_data: List[Dict]) -> str:
    """Format comparison table as markdown."""
    if not comparison_data:
        return "No data available"

    headers = list(comparison_data[0].keys())

    md_lines = []
    md_lines.append("# Model Comparison Table")
    md_lines.append("")

    # Table
    header_line = "| " + " | ".join(headers) + " |"
    separator_line = "| " + " | ".join(["---"] * len(headers)) + " |"

    md_lines.append(header_line)
    md_lines.append(separator_line)

    for row in comparison_data:
        row_line = "| " + " | ".join(str(row[h]) for h in headers) + " |"
        md_lines.append(row_line)

    return "\n".join(md_lines)


def generate_paper_style_table(
    model,
    batch_size: int = 1,
    sequence_length: int = 2048,
    hardware_name: str = "nvidia_h100_sxm5",
    parallel_config: Optional[ParallelConfig] = None,
) -> str:
    """
    Generate a table in the exact style of Table 2 from the paper.

    Args:
        model: Model instance
        batch_size: Batch size
        sequence_length: Sequence length
        hardware_name: Hardware specification
        parallel_config: Parallel configuration

    Returns:
        LaTeX-style table string
    """
    # Get operator breakdown
    if isinstance(model, MoEModel) and parallel_config:
        breakdown = model.get_operator_breakdown(
            batch_size, sequence_length, hardware_name, parallel_config
        )
    else:
        breakdown = model.get_operator_breakdown(
            batch_size, sequence_length, hardware_name
        )

    # Generate LaTeX table
    latex_lines = []
    latex_lines.append("\\begin{table}[h]")
    latex_lines.append("\\centering")
    latex_lines.append("\\caption{Operator Analysis - Similar to Table 2}")
    latex_lines.append("\\begin{tabular}{|l|r|r|r|r|r|}")
    latex_lines.append("\\hline")
    latex_lines.append(
        "Operator & FLOPs (T) & Mem Cap (GB) & Mem Mov (GB) & Time (ms) & AI \\\\"
    )
    latex_lines.append("\\hline")

    for op_name, op_data in breakdown["operators"].items():
        metrics = op_data["metrics_total"]

        flops_t = metrics.flops / 1e12
        memory_capacity_gb = metrics.memory_capacity_bytes / (1024**3)
        memory_movement_gb = metrics.memory_movement_bytes / 1e9
        time_ms = metrics.execution_time_ms
        ai = metrics.arithmetic_intensity

        latex_lines.append(
            f"{op_name.replace('_', ' ').title()} & {flops_t:.2f} & {memory_capacity_gb:.2f} & {memory_movement_gb:.1f} & {time_ms:.2f} & {ai:.2f} \\\\"
        )

    # Add totals
    totals = breakdown["totals"]
    latex_lines.append("\\hline")
    latex_lines.append(
        f"TOTAL & {totals['flops']/1e12:.2f} & {totals['memory_capacity_bytes']/(1024**3):.2f} & {totals['memory_movement_bytes']/1e9:.1f} & {totals['execution_time_ms']:.2f} & {totals['arithmetic_intensity']:.2f} \\\\"
    )

    latex_lines.append("\\hline")
    latex_lines.append("\\end{tabular}")
    latex_lines.append("\\end{table}")

    return "\n".join(latex_lines)

"""
Model comparison engine for analyzing multiple LLM models.
"""

import asyncio
import json
import os
import pickle
from dataclasses import asdict, dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

from ..core.base_model import BaseModel, ModelMetrics, ParallelConfig
from ..core.model_factory import ModelFactory
from ..metrics import ShapeAnalyzer
from ..utils.performance import (
    get_async_processor,
    get_memory_manager,
    get_performance_monitor,
    memory_optimized,
    performance_profiler,
)


@dataclass
class ComparisonResult:
    """
    Results from comparing multiple models with comprehensive export and serialization capabilities.

    This class provides structured storage for model comparison results with support for
    data analysis through pandas DataFrames and various export formats for persistence.
    """

    models: List[str]
    metrics: Dict[str, List[Any]]  # metric_name -> [values for each model]
    parallel_configs: List[ParallelConfig]
    sequence_length: int
    batch_size: int
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Initialize metadata with default values."""
        if not self.metadata:
            self.metadata = {
                "package_version": "1.0.0",
                "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
                "analysis_type": "model_comparison",
                "num_models": len(self.models),
                "created_by": os.getenv("USER", "unknown"),
                "hostname": os.getenv("HOSTNAME", "unknown"),
            }

    def to_dataframe(self, include_metadata: bool = False) -> pd.DataFrame:
        """
        Convert comparison results to pandas DataFrame for data analysis.

        Args:
            include_metadata: Whether to include metadata as additional columns

        Returns:
            pandas DataFrame with model comparison data
        """
        # Start with model names
        data = {"model": self.models}

        # Add all metrics
        data.update(self.metrics)

        # Add parallel configuration details
        for i, config in enumerate(self.parallel_configs):
            if i == 0:  # Initialize columns on first iteration
                data["tensor_parallel_size"] = []
                data["pipeline_parallel_size"] = []
                data["data_parallel_size"] = []
                data["expert_parallel_size"] = []
                data["expert_data_parallel_size"] = []

            data["tensor_parallel_size"].append(config.tensor_parallel_size)
            data["pipeline_parallel_size"].append(config.pipeline_parallel_size)
            data["data_parallel_size"].append(config.data_parallel_size)
            data["expert_parallel_size"].append(config.expert_parallel_size)
            data["expert_data_parallel_size"].append(config.expert_data_parallel_size)

        # Add analysis parameters
        data["sequence_length"] = [self.sequence_length] * len(self.models)
        data["batch_size"] = [self.batch_size] * len(self.models)
        data["analysis_timestamp"] = [self.timestamp] * len(self.models)

        # Add metadata if requested
        if include_metadata:
            for key, value in self.metadata.items():
                data[f"meta_{key}"] = [value] * len(self.models)

        df = pd.DataFrame(data)

        # Set appropriate data types
        numeric_columns = [
            "total_params",
            "attention_params",
            "mlp_params",
            "embedding_params",
            "flops_forward",
            "flops_per_token",
            "memory_params",
            "memory_activations",
            "memory_total",
            "experts_per_token",
            "active_params_per_token",
            "param_efficiency",
            "flop_efficiency",
            "memory_efficiency",
            "tensor_parallel_size",
            "pipeline_parallel_size",
            "data_parallel_size",
            "expert_parallel_size",
            "expert_data_parallel_size",
            "sequence_length",
            "batch_size",
        ]

        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors="coerce")

        return df

    def to_dict(self, include_raw_data: bool = True) -> Dict[str, Any]:
        """
        Convert comparison results to dictionary with comprehensive metadata.

        Args:
            include_raw_data: Whether to include raw metrics data

        Returns:
            Dictionary representation of comparison results
        """
        result = {
            "metadata": {
                **self.metadata,
                "timestamp": self.timestamp.isoformat(),
                "sequence_length": self.sequence_length,
                "batch_size": self.batch_size,
                "models": self.models,
                "num_models": len(self.models),
            },
            "parallel_configs": [
                {
                    "tensor_parallel_size": pc.tensor_parallel_size,
                    "pipeline_parallel_size": pc.pipeline_parallel_size,
                    "data_parallel_size": pc.data_parallel_size,
                    "expert_parallel_size": pc.expert_parallel_size,
                    "expert_data_parallel_size": pc.expert_data_parallel_size,
                }
                for pc in self.parallel_configs
            ],
        }

        if include_raw_data:
            result["metrics"] = self.metrics

        return result

    def save_json(self, filepath: Union[str, Path], indent: int = 2) -> str:
        """
        Save comparison results to JSON file.

        Args:
            filepath: Path to save JSON file
            indent: JSON indentation level

        Returns:
            Path to saved file
        """
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)

        data = self.to_dict()

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=indent, default=str)

        return str(filepath)

    def save_pickle(self, filepath: Union[str, Path]) -> str:
        """
        Save comparison results to pickle file for complete object persistence.

        Args:
            filepath: Path to save pickle file

        Returns:
            Path to saved file
        """
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)

        with open(filepath, "wb") as f:
            pickle.dump(self, f)

        return str(filepath)

    def save_csv(
        self, filepath: Union[str, Path], include_metadata: bool = True
    ) -> str:
        """
        Save comparison results to CSV file.

        Args:
            filepath: Path to save CSV file
            include_metadata: Whether to include metadata columns

        Returns:
            Path to saved file
        """
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)

        df = self.to_dataframe(include_metadata=include_metadata)
        df.to_csv(filepath, index=False)

        return str(filepath)

    def save_excel(
        self, filepath: Union[str, Path], include_summary: bool = True
    ) -> str:
        """
        Save comparison results to Excel file with multiple sheets.

        Args:
            filepath: Path to save Excel file
            include_summary: Whether to include summary sheet

        Returns:
            Path to saved file
        """
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)

        with pd.ExcelWriter(filepath, engine="openpyxl") as writer:
            # Main data sheet
            df = self.to_dataframe(include_metadata=True)
            df.to_excel(writer, sheet_name="Model Comparison", index=False)

            # Metadata sheet
            metadata_df = pd.DataFrame([self.metadata])
            metadata_df.to_excel(writer, sheet_name="Metadata", index=False)

            # Parallel configs sheet
            config_data = []
            for i, (model, config) in enumerate(
                zip(self.models, self.parallel_configs)
            ):
                config_data.append(
                    {
                        "model": model,
                        "tensor_parallel_size": config.tensor_parallel_size,
                        "pipeline_parallel_size": config.pipeline_parallel_size,
                        "data_parallel_size": config.data_parallel_size,
                        "expert_parallel_size": config.expert_parallel_size,
                        "expert_data_parallel_size": config.expert_data_parallel_size,
                    }
                )

            config_df = pd.DataFrame(config_data)
            config_df.to_excel(writer, sheet_name="Parallel Configs", index=False)

            # Summary statistics sheet if requested
            if include_summary:
                summary_data = self._generate_summary_statistics()
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(
                    writer, sheet_name="Summary Statistics", index=False
                )

        return str(filepath)

    def _generate_summary_statistics(self) -> List[Dict[str, Any]]:
        """Generate summary statistics for the comparison results."""
        summary = []

        numeric_metrics = [
            "total_params",
            "flops_per_token",
            "memory_total",
            "param_efficiency",
            "flop_efficiency",
            "memory_efficiency",
        ]

        for metric in numeric_metrics:
            if metric in self.metrics:
                values = [v for v in self.metrics[metric] if v is not None]
                if values:
                    summary.append(
                        {
                            "metric": metric,
                            "min": min(values),
                            "max": max(values),
                            "mean": sum(values) / len(values),
                            "std": np.std(values) if len(values) > 1 else 0,
                            "count": len(values),
                        }
                    )

        return summary

    @classmethod
    def load_json(cls, filepath: Union[str, Path]) -> "ComparisonResult":
        """
        Load comparison results from JSON file.

        Args:
            filepath: Path to JSON file

        Returns:
            ComparisonResult instance
        """
        with open(filepath, "r", encoding="utf-8") as f:
            data = json.load(f)

        # Reconstruct parallel configs
        parallel_configs = [
            ParallelConfig(
                tensor_parallel_size=pc["tensor_parallel_size"],
                pipeline_parallel_size=pc["pipeline_parallel_size"],
                data_parallel_size=pc["data_parallel_size"],
                expert_parallel_size=pc["expert_parallel_size"],
                expert_data_parallel_size=pc["expert_data_parallel_size"],
            )
            for pc in data["parallel_configs"]
        ]

        # Parse timestamp
        timestamp = datetime.fromisoformat(data["metadata"]["timestamp"])

        return cls(
            models=data["metadata"]["models"],
            metrics=data.get("metrics", {}),
            parallel_configs=parallel_configs,
            sequence_length=data["metadata"]["sequence_length"],
            batch_size=data["metadata"]["batch_size"],
            timestamp=timestamp,
            metadata={
                k: v
                for k, v in data["metadata"].items()
                if k not in ["timestamp", "models", "sequence_length", "batch_size"]
            },
        )

    @classmethod
    def load_pickle(cls, filepath: Union[str, Path]) -> "ComparisonResult":
        """
        Load comparison results from pickle file.

        Args:
            filepath: Path to pickle file

        Returns:
            ComparisonResult instance
        """
        with open(filepath, "rb") as f:
            return pickle.load(f)

    def get_model_ranking(
        self, metric: str, ascending: bool = False
    ) -> List[Tuple[str, Any]]:
        """
        Get models ranked by a specific metric.

        Args:
            metric: Metric name to rank by
            ascending: Whether to sort in ascending order

        Returns:
            List of (model_name, metric_value) tuples sorted by metric
        """
        if metric not in self.metrics:
            raise ValueError(f"Metric '{metric}' not found in results")

        model_values = [
            (model, value)
            for model, value in zip(self.models, self.metrics[metric])
            if value is not None
        ]

        return sorted(model_values, key=lambda x: x[1], reverse=not ascending)

    def filter_models(self, condition: callable) -> "ComparisonResult":
        """
        Filter models based on a condition function.

        Args:
            condition: Function that takes (model_name, metrics_dict) and returns bool

        Returns:
            New ComparisonResult with filtered models
        """
        filtered_indices = []
        filtered_models = []

        for i, model in enumerate(self.models):
            model_metrics = {key: values[i] for key, values in self.metrics.items()}
            if condition(model, model_metrics):
                filtered_indices.append(i)
                filtered_models.append(model)

        # Filter all data
        filtered_metrics = {
            key: [values[i] for i in filtered_indices]
            for key, values in self.metrics.items()
        }

        filtered_configs = [self.parallel_configs[i] for i in filtered_indices]

        return ComparisonResult(
            models=filtered_models,
            metrics=filtered_metrics,
            parallel_configs=filtered_configs,
            sequence_length=self.sequence_length,
            batch_size=self.batch_size,
            timestamp=self.timestamp,
            metadata=self.metadata.copy(),
        )

    def add_custom_metric(self, metric_name: str, values: List[Any]) -> None:
        """
        Add a custom metric to the comparison results.

        Args:
            metric_name: Name of the new metric
            values: List of values (one per model)
        """
        if len(values) != len(self.models):
            raise ValueError(
                f"Number of values ({len(values)}) must match number of models ({len(self.models)})"
            )

        self.metrics[metric_name] = values


class Comparator:
    """
    Engine for comparing multiple LLM models across various metrics.

    Provides comprehensive analysis including parameter counts, FLOPs, memory requirements,
    architectural differences, and performance trade-offs.
    """

    def __init__(self):
        """Initialize the comparator."""
        self.model_factory = ModelFactory()
        self.performance_monitor = get_performance_monitor()
        self.async_processor = get_async_processor()
        self.memory_manager = get_memory_manager()

    @performance_profiler
    @memory_optimized(memory_threshold_mb=1000)
    def compare_models(
        self,
        model_names: List[str],
        sequence_length: int = 2048,
        batch_size: int = 1,
        parallel_configs: Optional[List[ParallelConfig]] = None,
        precision: str = "fp16",
    ) -> ComparisonResult:
        """
        Compare multiple models across comprehensive metrics.

        Args:
            model_names: List of model names to compare
            sequence_length: Input sequence length
            batch_size: Batch size
            parallel_configs: List of parallel configurations (one per model)
            precision: Model precision for memory calculations

        Returns:
            ComparisonResult with detailed comparison data
        """
        if not model_names:
            raise ValueError("At least one model name must be provided")

        # Default parallel configs if not provided
        if parallel_configs is None:
            parallel_configs = [ParallelConfig() for _ in model_names]
        elif len(parallel_configs) != len(model_names):
            raise ValueError("Number of parallel configs must match number of models")

        # Initialize results
        metrics = {
            "architecture": [],
            "total_params": [],
            "attention_params": [],
            "mlp_params": [],
            "embedding_params": [],
            "flops_forward": [],
            "flops_per_token": [],
            "memory_params": [],
            "memory_activations": [],
            "memory_total": [],
            "experts_per_token": [],
            "active_params_per_token": [],
            "param_efficiency": [],
            "flop_efficiency": [],
            "memory_efficiency": [],
        }

        model_metrics_list = []

        # Analyze each model
        for i, model_name in enumerate(model_names):
            try:
                # Create model instance
                model = self.model_factory.create_model(model_name)

                # Get comprehensive metrics
                model_metrics = model.get_metrics(
                    sequence_length=sequence_length,
                    batch_size=batch_size,
                    parallel_config=parallel_configs[i],
                )

                model_metrics_list.append(model_metrics)

                # Extract metrics for comparison
                metrics["architecture"].append(model_metrics.architecture)
                metrics["total_params"].append(model_metrics.total_params)
                metrics["attention_params"].append(model_metrics.attention_params)
                metrics["mlp_params"].append(model_metrics.mlp_params)
                metrics["embedding_params"].append(model_metrics.embedding_params)
                metrics["flops_forward"].append(model_metrics.flops_forward)
                metrics["flops_per_token"].append(model_metrics.flops_per_token)
                metrics["memory_params"].append(model_metrics.memory_params)
                metrics["memory_activations"].append(model_metrics.memory_activations)
                metrics["memory_total"].append(model_metrics.memory_total)
                metrics["experts_per_token"].append(
                    model_metrics.experts_per_token or 0
                )
                metrics["active_params_per_token"].append(
                    model_metrics.active_params_per_token or model_metrics.total_params
                )

            except Exception as e:
                # Handle model loading/analysis errors gracefully
                print(f"Error analyzing model {model_name}: {e}")
                # Fill with None/0 values
                for key in metrics:
                    if key not in ["experts_per_token", "active_params_per_token"]:
                        metrics[key].append(None)
                    else:
                        metrics[key].append(0)
                model_metrics_list.append(None)

        # Calculate efficiency metrics
        self._calculate_efficiency_metrics(metrics)

        # Add statistical analysis
        self._add_statistical_analysis(metrics)

        return ComparisonResult(
            models=model_names,
            metrics=metrics,
            parallel_configs=parallel_configs,
            sequence_length=sequence_length,
            batch_size=batch_size,
        )

    def _calculate_efficiency_metrics(self, metrics: Dict[str, List[Any]]) -> None:
        """Calculate efficiency metrics for comparison."""
        total_params = metrics["total_params"]
        flops_per_token = metrics["flops_per_token"]
        memory_total = metrics["memory_total"]
        active_params = metrics["active_params_per_token"]

        # Parameter efficiency (active params / total params for MoE models)
        param_efficiency = []
        for i in range(len(total_params)):
            if total_params[i] is not None and active_params[i] is not None:
                if total_params[i] > 0:
                    efficiency = active_params[i] / total_params[i]
                    param_efficiency.append(efficiency)
                else:
                    param_efficiency.append(0)
            else:
                param_efficiency.append(None)

        metrics["param_efficiency"] = param_efficiency

        # FLOP efficiency (FLOPs per parameter)
        flop_efficiency = []
        for i in range(len(total_params)):
            if total_params[i] is not None and flops_per_token[i] is not None:
                if total_params[i] > 0:
                    efficiency = flops_per_token[i] / total_params[i]
                    flop_efficiency.append(efficiency)
                else:
                    flop_efficiency.append(0)
            else:
                flop_efficiency.append(None)

        metrics["flop_efficiency"] = flop_efficiency

        # Memory efficiency (memory per parameter)
        memory_efficiency = []
        for i in range(len(total_params)):
            if total_params[i] is not None and memory_total[i] is not None:
                if total_params[i] > 0:
                    efficiency = memory_total[i] / total_params[i]
                    memory_efficiency.append(efficiency)
                else:
                    memory_efficiency.append(0)
            else:
                memory_efficiency.append(None)

        metrics["memory_efficiency"] = memory_efficiency

    def _add_statistical_analysis(self, metrics: Dict[str, List[Any]]) -> None:
        """Add statistical analysis to metrics."""
        numeric_metrics = [
            "total_params",
            "flops_per_token",
            "memory_total",
            "param_efficiency",
            "flop_efficiency",
            "memory_efficiency",
        ]

        for metric_name in numeric_metrics:
            values = [v for v in metrics[metric_name] if v is not None]
            if len(values) > 1:
                # Add relative comparisons (percentage difference from mean)
                mean_val = np.mean(values)
                relative_values = []
                for v in metrics[metric_name]:
                    if v is not None and mean_val > 0:
                        relative_values.append((v - mean_val) / mean_val * 100)
                    else:
                        relative_values.append(None)

                metrics[f"{metric_name}_relative"] = relative_values

    def analyze_architectural_differences(
        self, model_names: List[str]
    ) -> Dict[str, Any]:
        """
        Analyze architectural differences between models.

        Args:
            model_names: List of model names to analyze

        Returns:
            Dictionary with architectural analysis
        """
        analysis = {
            "architectures": {},
            "parameter_distribution": {},
            "attention_patterns": {},
            "moe_characteristics": {},
        }

        for model_name in model_names:
            try:
                model = self.model_factory.create_model(model_name)
                config = model.config

                # Basic architecture info
                arch_info = {
                    "model_type": getattr(config, "model_type", "unknown"),
                    "hidden_size": getattr(config, "hidden_size", 0),
                    "num_layers": getattr(config, "num_hidden_layers", 0),
                    "num_heads": getattr(config, "num_attention_heads", 0),
                    "intermediate_size": getattr(config, "intermediate_size", 0),
                    "vocab_size": getattr(config, "vocab_size", 0),
                }

                analysis["architectures"][model_name] = arch_info

                # Parameter distribution
                param_dist = {
                    "attention_params": model.compute_attention_params(),
                    "mlp_params": model.compute_mlp_params(),
                    "embedding_params": model.compute_embedding_params(),
                }
                analysis["parameter_distribution"][model_name] = param_dist

                # Attention patterns
                num_kv_heads = getattr(
                    config, "num_key_value_heads", arch_info["num_heads"]
                )
                attention_info = {
                    "num_attention_heads": arch_info["num_heads"],
                    "num_key_value_heads": num_kv_heads,
                    "uses_gqa": num_kv_heads != arch_info["num_heads"],
                    "head_dim": (
                        arch_info["hidden_size"] // arch_info["num_heads"]
                        if arch_info["num_heads"] > 0
                        else 0
                    ),
                }
                analysis["attention_patterns"][model_name] = attention_info

                # MoE characteristics
                is_moe = hasattr(config, "num_experts") or hasattr(
                    config, "n_routed_experts"
                )
                if is_moe:
                    moe_info = {
                        "is_moe": True,
                        "num_experts": getattr(
                            config,
                            "num_experts",
                            getattr(config, "n_routed_experts", 0),
                        ),
                        "experts_per_token": getattr(
                            config,
                            "num_experts_per_tok",
                            getattr(config, "experts_per_token", 1),
                        ),
                        "shared_experts": getattr(config, "n_shared_experts", 0),
                    }
                else:
                    moe_info = {"is_moe": False}

                analysis["moe_characteristics"][model_name] = moe_info

            except Exception as e:
                print(f"Error analyzing architecture for {model_name}: {e}")
                analysis["architectures"][model_name] = {"error": str(e)}

        return analysis

    def generate_comparison_summary(
        self, comparison_result: ComparisonResult
    ) -> Dict[str, Any]:
        """
        Generate a human-readable summary of model comparison.

        Args:
            comparison_result: Results from model comparison

        Returns:
            Dictionary with comparison summary
        """
        summary = {
            "overview": {},
            "parameter_analysis": {},
            "performance_analysis": {},
            "efficiency_analysis": {},
            "recommendations": [],
        }

        metrics = comparison_result.metrics
        models = comparison_result.models

        # Overview
        summary["overview"] = {
            "num_models": len(models),
            "models": models,
            "sequence_length": comparison_result.sequence_length,
            "batch_size": comparison_result.batch_size,
        }

        # Parameter analysis
        total_params = [p for p in metrics["total_params"] if p is not None]
        if total_params:
            summary["parameter_analysis"] = {
                "min_params": min(total_params),
                "max_params": max(total_params),
                "avg_params": sum(total_params) / len(total_params),
                "param_range_ratio": (
                    max(total_params) / min(total_params)
                    if min(total_params) > 0
                    else 0
                ),
            }

        # Performance analysis
        flops_per_token = [f for f in metrics["flops_per_token"] if f is not None]
        if flops_per_token:
            summary["performance_analysis"] = {
                "min_flops_per_token": min(flops_per_token),
                "max_flops_per_token": max(flops_per_token),
                "avg_flops_per_token": sum(flops_per_token) / len(flops_per_token),
                "flops_range_ratio": (
                    max(flops_per_token) / min(flops_per_token)
                    if min(flops_per_token) > 0
                    else 0
                ),
            }

        # Efficiency analysis
        param_efficiency = [
            e for e in metrics.get("param_efficiency", []) if e is not None
        ]
        if param_efficiency:
            summary["efficiency_analysis"] = {
                "avg_param_efficiency": sum(param_efficiency) / len(param_efficiency),
                "most_efficient_model": models[
                    metrics["param_efficiency"].index(max(param_efficiency))
                ],
                "least_efficient_model": models[
                    metrics["param_efficiency"].index(min(param_efficiency))
                ],
            }

        # Generate recommendations
        summary["recommendations"] = self._generate_recommendations(comparison_result)

        return summary

    def _generate_recommendations(
        self, comparison_result: ComparisonResult
    ) -> List[str]:
        """Generate recommendations based on comparison results."""
        recommendations = []
        metrics = comparison_result.metrics
        models = comparison_result.models

        # Find models with best efficiency
        param_efficiency = metrics.get("param_efficiency", [])
        flop_efficiency = metrics.get("flop_efficiency", [])
        memory_efficiency = metrics.get("memory_efficiency", [])

        # Parameter efficiency recommendation
        if param_efficiency and any(e is not None for e in param_efficiency):
            valid_efficiencies = [
                (i, e) for i, e in enumerate(param_efficiency) if e is not None
            ]
            if valid_efficiencies:
                best_idx, best_eff = max(valid_efficiencies, key=lambda x: x[1])
                if best_eff < 1.0:  # MoE model
                    recommendations.append(
                        f"For parameter efficiency, consider {models[best_idx]} "
                        f"(uses {best_eff:.1%} of parameters per token)"
                    )

        # Memory efficiency recommendation
        if memory_efficiency and any(e is not None for e in memory_efficiency):
            valid_efficiencies = [
                (i, e) for i, e in enumerate(memory_efficiency) if e is not None
            ]
            if valid_efficiencies:
                best_idx, _ = min(
                    valid_efficiencies, key=lambda x: x[1]
                )  # Lower is better for memory
                recommendations.append(
                    f"For memory efficiency, consider {models[best_idx]}"
                )

        # Architecture-specific recommendations
        architectures = metrics.get("architecture", [])
        if "deepseek" in [arch.lower() if arch else "" for arch in architectures]:
            recommendations.append(
                "DeepSeek models offer good parameter efficiency through MoE architecture"
            )

        if "llama" in [arch.lower() if arch else "" for arch in architectures]:
            recommendations.append(
                "Llama models provide consistent performance with dense architecture"
            )

        return recommendations

    def export_comparison(
        self,
        comparison_result: ComparisonResult,
        format: str = "json",
        filename: Optional[str] = None,
    ) -> str:
        """
        Export comparison results to file.

        Args:
            comparison_result: Results to export
            format: Export format ('json', 'csv', 'excel')
            filename: Output filename (auto-generated if None)

        Returns:
            Path to exported file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"model_comparison_{timestamp}.{format}"

        if format.lower() == "json":
            import json

            with open(filename, "w") as f:
                json.dump(comparison_result.to_dict(), f, indent=2)

        elif format.lower() == "csv":
            df = comparison_result.to_dataframe()
            df.to_csv(filename, index=False)

        elif format.lower() == "excel":
            df = comparison_result.to_dataframe()
            with pd.ExcelWriter(filename, engine="openpyxl") as writer:
                df.to_excel(writer, sheet_name="Model Comparison", index=False)

                # Add summary sheet
                summary = self.generate_comparison_summary(comparison_result)
                summary_df = pd.DataFrame([summary["overview"]])
                summary_df.to_excel(writer, sheet_name="Summary", index=False)

        else:
            raise ValueError(f"Unsupported export format: {format}")

        return filename

    def compare_parallel_strategies(
        self,
        model_name: str,
        parallel_configs: List[ParallelConfig],
        sequence_length: int = 2048,
        batch_size: int = 1,
    ) -> Dict[str, Any]:
        """
        Compare different parallel strategies for a single model.

        Args:
            model_name: Model to analyze
            parallel_configs: List of parallel configurations to compare
            sequence_length: Input sequence length
            batch_size: Batch size

        Returns:
            Dictionary with parallel strategy comparison
        """
        model = self.model_factory.create_model(model_name)

        results = {
            "model": model_name,
            "configurations": [],
            "metrics": {
                "memory_per_device": [],
                "communication_volume": [],
                "efficiency_score": [],
            },
        }

        for config in parallel_configs:
            try:
                # Get metrics for this configuration
                metrics = model.get_metrics(sequence_length, batch_size, config)

                # Calculate memory per device
                total_memory = metrics.memory_total
                total_devices = (
                    config.tensor_parallel_size
                    * config.pipeline_parallel_size
                    * config.data_parallel_size
                )
                memory_per_device = (
                    total_memory / total_devices if total_devices > 0 else total_memory
                )

                # Estimate communication volume
                shapes = model.get_matrix_shapes(config)
                comm_volume = ShapeAnalyzer.compute_communication_volume(shapes, config)

                # Calculate efficiency score (lower memory + communication is better)
                efficiency_score = 1.0 / (
                    memory_per_device + comm_volume.get("total", 0) + 1
                )

                results["configurations"].append(
                    {
                        "tensor_parallel_size": config.tensor_parallel_size,
                        "pipeline_parallel_size": config.pipeline_parallel_size,
                        "data_parallel_size": config.data_parallel_size,
                        "expert_parallel_size": config.expert_parallel_size,
                    }
                )

                results["metrics"]["memory_per_device"].append(memory_per_device)
                results["metrics"]["communication_volume"].append(
                    comm_volume.get("total", 0)
                )
                results["metrics"]["efficiency_score"].append(efficiency_score)

            except Exception as e:
                print(f"Error analyzing parallel config {config}: {e}")
                results["configurations"].append(config.__dict__)
                results["metrics"]["memory_per_device"].append(None)
                results["metrics"]["communication_volume"].append(None)
                results["metrics"]["efficiency_score"].append(None)

        return results

    async def compare_models_async(
        self,
        model_names: List[str],
        sequence_length: int = 2048,
        batch_size: int = 1,
        parallel_configs: Optional[List[ParallelConfig]] = None,
        precision: str = "fp16",
    ) -> ComparisonResult:
        """
        Asynchronously compare multiple models for better performance.

        Args:
            model_names: List of model names to compare
            sequence_length: Input sequence length
            batch_size: Batch size
            parallel_configs: List of parallel configurations (one per model)
            precision: Model precision for memory calculations

        Returns:
            ComparisonResult with detailed comparison data
        """
        with self.performance_monitor.monitor_operation(
            f"async_compare_{len(model_names)}_models"
        ):
            if not model_names:
                raise ValueError("At least one model name must be provided")

            # Default parallel configs if not provided
            if parallel_configs is None:
                parallel_configs = [ParallelConfig() for _ in model_names]
            elif len(parallel_configs) != len(model_names):
                raise ValueError(
                    "Number of parallel configs must match number of models"
                )

            # Define analysis function for async processing
            def analyze_single_model(
                model_name: str, parallel_config: ParallelConfig
            ) -> Optional[ModelMetrics]:
                try:
                    with self.memory_manager.memory_limit_context(
                        f"analyze_{model_name}"
                    ):
                        model = self.model_factory.create_model(model_name)
                        return model.get_metrics(
                            sequence_length=sequence_length,
                            batch_size=batch_size,
                            parallel_config=parallel_config,
                        )
                except Exception as e:
                    print(f"Error analyzing model {model_name}: {e}")
                    return None

            # Process models concurrently
            model_configs = list(zip(model_names, parallel_configs))
            results = await self.async_processor.process_models_async(
                model_names,
                lambda name: analyze_single_model(
                    name, parallel_configs[model_names.index(name)]
                ),
            )

            # Initialize metrics dictionary
            metrics = {
                "architecture": [],
                "total_params": [],
                "attention_params": [],
                "mlp_params": [],
                "embedding_params": [],
                "flops_forward": [],
                "flops_per_token": [],
                "memory_params": [],
                "memory_activations": [],
                "memory_total": [],
                "experts_per_token": [],
                "active_params_per_token": [],
                "param_efficiency": [],
                "flop_efficiency": [],
                "memory_efficiency": [],
            }

            # Process results
            for model_name in model_names:
                result = results.get(model_name)

                if isinstance(result, Exception) or result is None:
                    # Handle errors by filling with None/0 values
                    for key in metrics:
                        if key not in ["experts_per_token", "active_params_per_token"]:
                            metrics[key].append(None)
                        else:
                            metrics[key].append(0)
                else:
                    # Extract metrics from successful analysis
                    metrics["architecture"].append(result.architecture)
                    metrics["total_params"].append(result.total_params)
                    metrics["attention_params"].append(result.attention_params)
                    metrics["mlp_params"].append(result.mlp_params)
                    metrics["embedding_params"].append(result.embedding_params)
                    metrics["flops_forward"].append(result.flops_forward)
                    metrics["flops_per_token"].append(result.flops_per_token)
                    metrics["memory_params"].append(result.memory_params)
                    metrics["memory_activations"].append(result.memory_activations)
                    metrics["memory_total"].append(result.memory_total)
                    metrics["experts_per_token"].append(result.experts_per_token or 0)
                    metrics["active_params_per_token"].append(
                        result.active_params_per_token or result.total_params
                    )

            # Calculate efficiency metrics
            self._calculate_efficiency_metrics(metrics)

            # Add statistical analysis
            self._add_statistical_analysis(metrics)

            return ComparisonResult(
                models=model_names,
                metrics=metrics,
                parallel_configs=parallel_configs,
                sequence_length=sequence_length,
                batch_size=batch_size,
            )

    def compare_models_concurrent(
        self,
        model_names: List[str],
        sequence_length: int = 2048,
        batch_size: int = 1,
        parallel_configs: Optional[List[ParallelConfig]] = None,
        precision: str = "fp16",
        max_workers: int = 4,
    ) -> ComparisonResult:
        """
        Compare multiple models using concurrent processing (synchronous interface).

        Args:
            model_names: List of model names to compare
            sequence_length: Input sequence length
            batch_size: Batch size
            parallel_configs: List of parallel configurations (one per model)
            precision: Model precision for memory calculations
            max_workers: Maximum number of concurrent workers

        Returns:
            ComparisonResult with detailed comparison data
        """
        with self.performance_monitor.monitor_operation(
            f"concurrent_compare_{len(model_names)}_models"
        ):
            if not model_names:
                raise ValueError("At least one model name must be provided")

            # Default parallel configs if not provided
            if parallel_configs is None:
                parallel_configs = [ParallelConfig() for _ in model_names]
            elif len(parallel_configs) != len(model_names):
                raise ValueError(
                    "Number of parallel configs must match number of models"
                )

            # Define analysis function for concurrent processing
            def analyze_single_model(model_name: str) -> Optional[ModelMetrics]:
                try:
                    with self.memory_manager.memory_limit_context(
                        f"analyze_{model_name}"
                    ):
                        model = self.model_factory.create_model(model_name)
                        parallel_config = parallel_configs[
                            model_names.index(model_name)
                        ]
                        return model.get_metrics(
                            sequence_length=sequence_length,
                            batch_size=batch_size,
                            parallel_config=parallel_config,
                        )
                except Exception as e:
                    print(f"Error analyzing model {model_name}: {e}")
                    return None

            # Process models concurrently
            results = self.async_processor.process_models_sync(
                model_names, analyze_single_model
            )

            # Initialize metrics dictionary
            metrics = {
                "architecture": [],
                "total_params": [],
                "attention_params": [],
                "mlp_params": [],
                "embedding_params": [],
                "flops_forward": [],
                "flops_per_token": [],
                "memory_params": [],
                "memory_activations": [],
                "memory_total": [],
                "experts_per_token": [],
                "active_params_per_token": [],
                "param_efficiency": [],
                "flop_efficiency": [],
                "memory_efficiency": [],
            }

            # Process results
            for model_name in model_names:
                result = results.get(model_name)

                if isinstance(result, Exception) or result is None:
                    # Handle errors by filling with None/0 values
                    for key in metrics:
                        if key not in ["experts_per_token", "active_params_per_token"]:
                            metrics[key].append(None)
                        else:
                            metrics[key].append(0)
                else:
                    # Extract metrics from successful analysis
                    metrics["architecture"].append(result.architecture)
                    metrics["total_params"].append(result.total_params)
                    metrics["attention_params"].append(result.attention_params)
                    metrics["mlp_params"].append(result.mlp_params)
                    metrics["embedding_params"].append(result.embedding_params)
                    metrics["flops_forward"].append(result.flops_forward)
                    metrics["flops_per_token"].append(result.flops_per_token)
                    metrics["memory_params"].append(result.memory_params)
                    metrics["memory_activations"].append(result.memory_activations)
                    metrics["memory_total"].append(result.memory_total)
                    metrics["experts_per_token"].append(result.experts_per_token or 0)
                    metrics["active_params_per_token"].append(
                        result.active_params_per_token or result.total_params
                    )

            # Calculate efficiency metrics
            self._calculate_efficiency_metrics(metrics)

            # Add statistical analysis
            self._add_statistical_analysis(metrics)

            return ComparisonResult(
                models=model_names,
                metrics=metrics,
                parallel_configs=parallel_configs,
                sequence_length=sequence_length,
                batch_size=batch_size,
            )

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the comparator."""
        return {
            "performance_monitor": self.performance_monitor.get_performance_summary(),
            "async_processor": self.async_processor.get_performance_metrics(),
            "memory_usage": self.memory_manager.check_memory_usage(),
        }

    def cleanup_resources(self) -> None:
        """Cleanup resources and stop monitoring."""
        self.performance_monitor.stop_system_monitoring()
        self.async_processor.cleanup()
        self.memory_manager.force_garbage_collection()

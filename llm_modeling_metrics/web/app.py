"""
Gradio web interface for the Attention Roofline Figure.

This script creates an interactive web interface using Gradio to display
and customize the attention roofline plot.
"""

import io
import base64
import gradio as gr
import matplotlib.pyplot as plt
import numpy as np
from typing import List, Tuple, Optional
import warnings

# Suppress matplotlib warnings in web interface
warnings.filterwarnings("ignore")

# Import the analyzer from the existing script
from reproduce_attention_roofline_figure import AttentionRooflineAnalyzer


class GradioAttentionRoofline:
    """Gradio interface for attention roofline visualization."""
    
    def __init__(self):
        """Initialize the Gradio interface."""
        self.analyzer = None
        self.current_figure = None
        
    def initialize_analyzer(self):
        """Initialize the analyzer (can be slow, so we do it lazily)."""
        if self.analyzer is None:
            print("Initializing AttentionRooflineAnalyzer...")
            self.analyzer = AttentionRooflineAnalyzer()
            print("Analyzer initialized!")
        return self.analyzer
    
    def generate_plot(
        self,
        hardware_selection: List[str],
        model_selection: List[str],
        context_lengths: str,
        x_limit: float,
        y_limit: float,
        figure_width: float,
        figure_height: float,
        show_grid: bool,
        title: str
    ) -> <PERSON><PERSON>[plt.Figure, str]:
        """Generate the attention roofline plot with custom parameters."""
        try:
            # Initialize analyzer if needed
            analyzer = self.initialize_analyzer()
            
            # Parse context lengths
            try:
                context_lens = [int(x.strip()) for x in context_lengths.split(',')]
            except:
                context_lens = [8192, 32768]  # Default values
            
            # Analyze attention performance with custom context lengths
            results = analyzer.analyze_attention_performance(context_lens)
            
            # Create the plot with custom size
            fig, ax = plt.subplots(1, 1, figsize=(figure_width, figure_height))
            
            # Plot hardware rooflines (only selected ones)
            import seaborn as sns
            snscolors = sns.color_palette("husl", 4)
            hardware_keys = ["H100", "A800", "H20", "910B"]
            hardware_colors = {key: color for key, color in zip(hardware_keys, snscolors)}
            
            for hw_name in hardware_selection:
                if hw_name in hardware_colors:
                    color = hardware_colors[hw_name]
                    ai_range, roofline = analyzer.calculate_roofline_data(hw_name)
                    ax.plot(
                        ai_range,
                        roofline,
                        "--",
                        color=color,
                        alpha=0.7,
                        label=hw_name,
                        linewidth=1.5,
                    )
            
            # Plot attention mechanisms (only selected ones)
            for model_name, data in results.items():
                if "model_info" not in data:
                    continue
                
                # Check if this model is selected
                model_display_name = data["model_info"]["name"]
                if model_display_name not in model_selection:
                    continue
                
                model_info = data["model_info"]
                memory_gb = data["memory_access_gb"]
                compute_gflops = data["compute_gflops"]
                
                # Skip if no valid data
                if not memory_gb or all(x == 0 for x in memory_gb):
                    continue
                
                # Apply scaling
                scale_factor = model_info.get("scale_factor", 1)
                scaled_compute = [gf * scale_factor for gf in compute_gflops]
                
                # Plot the trajectory
                if len(memory_gb) > 1:
                    ax.plot(
                        memory_gb,
                        scaled_compute,
                        "-",
                        color=model_info["color"],
                        alpha=0.7,
                        linewidth=2,
                    )
                
                # Plot individual points
                markersize = 12 if model_info["marker"] == "*" else 8
                ax.scatter(
                    memory_gb,
                    scaled_compute,
                    color=model_info["color"],
                    marker=model_info["marker"],
                    s=markersize**2,
                    label=model_info["name"],
                    zorder=5,
                    alpha=0.8,
                )
            
            # Customize the plot
            ax.set_xlabel("Memory access (GB)", fontsize=12)
            ax.set_ylabel("Compute (GFLOPS)", fontsize=12)
            ax.set_xlim(0, x_limit)
            ax.set_ylim(0, y_limit)
            
            # Add grid if requested
            if show_grid:
                ax.grid(True, alpha=0.3)
            
            # Add legend
            ax.legend(loc="upper left", fontsize=10)
            
            # Add title
            ax.set_title(title, fontsize=14, pad=20)
            
            # Tight layout
            plt.tight_layout()
            
            self.current_figure = fig
            
            return fig, "Plot generated successfully!"
            
        except Exception as e:
            # Return empty figure and error message
            fig, ax = plt.subplots(1, 1, figsize=(8, 6))
            ax.text(0.5, 0.5, f"Error: {str(e)}", ha='center', va='center', transform=ax.transAxes)
            return fig, f"Error generating plot: {str(e)}"
    
    def save_plot(self, filename: str) -> str:
        """Save the current plot to file."""
        if self.current_figure is None:
            return "No plot to save. Please generate a plot first."
        
        try:
            if not filename.endswith(('.png', '.jpg', '.jpeg', '.pdf', '.svg')):
                filename += '.png'
            
            self.current_figure.savefig(filename, dpi=300, bbox_inches="tight")
            return f"Plot saved successfully as {filename}"
        except Exception as e:
            return f"Error saving plot: {str(e)}"
    
    def create_interface(self):
        """Create the Gradio interface."""
        with gr.Blocks(title="Attention Roofline Analyzer", theme=gr.themes.Soft()) as interface:
            gr.Markdown("# 🚀 Attention Roofline Analyzer")
            gr.Markdown("Interactive visualization of attention mechanisms performance on different hardware.")
            
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("## 🔧 Configuration")
                    
                    # Hardware selection
                    hardware_selection = gr.CheckboxGroup(
                        choices=["H100", "A800", "H20", "910B"],
                        value=["H100", "A800", "H20", "910B"],
                        label="Hardware to Display",
                        info="Select which hardware rooflines to show"
                    )
                    
                    # Model selection
                    model_selection = gr.CheckboxGroup(
                        choices=["DSv3, 8K-32K", "Qwen3 MoE, 8K-32K", "Step-3, 8K-32K"],
                        value=["DSv3, 8K-32K", "Qwen3 MoE, 8K-32K", "Step-3, 8K-32K"],
                        label="Models to Display",
                        info="Select which attention models to show"
                    )
                    
                    # Context lengths
                    context_lengths = gr.Textbox(
                        value="8192, 32768",
                        label="Context Lengths",
                        info="Comma-separated list of context lengths to analyze"
                    )
                    
                    with gr.Row():
                        x_limit = gr.Slider(
                            minimum=1, maximum=10, value=3.5, step=0.1,
                            label="X-axis Limit (GB)"
                        )
                        y_limit = gr.Slider(
                            minimum=100, maximum=2000, value=600, step=50,
                            label="Y-axis Limit (GFLOPS)"
                        )
                    
                    with gr.Row():
                        figure_width = gr.Slider(
                            minimum=6, maximum=20, value=12, step=1,
                            label="Figure Width"
                        )
                        figure_height = gr.Slider(
                            minimum=4, maximum=16, value=8, step=1,
                            label="Figure Height"
                        )
                    
                    show_grid = gr.Checkbox(
                        value=True,
                        label="Show Grid"
                    )
                    
                    title = gr.Textbox(
                        value="Compute and Memory Access of Different Attention Designs During Decoding",
                        label="Plot Title"
                    )
                    
                    generate_btn = gr.Button("🎨 Generate Plot", variant="primary", size="lg")
                    
                    # Save functionality
                    gr.Markdown("## 💾 Save Plot")
                    save_filename = gr.Textbox(
                        value="attention_roofline_custom.png",
                        label="Filename",
                        info="Include extension (.png, .pdf, .svg, etc.)"
                    )
                    save_btn = gr.Button("💾 Save Plot", variant="secondary")
                
                with gr.Column(scale=2):
                    gr.Markdown("## 📊 Visualization")
                    plot_output = gr.Plot(label="Attention Roofline Plot")
                    status_output = gr.Textbox(label="Status", interactive=False)
                    save_status = gr.Textbox(label="Save Status", interactive=False)
            
            # Event handlers
            generate_btn.click(
                fn=self.generate_plot,
                inputs=[
                    hardware_selection, model_selection, context_lengths,
                    x_limit, y_limit, figure_width, figure_height,
                    show_grid, title
                ],
                outputs=[plot_output, status_output]
            )
            
            save_btn.click(
                fn=self.save_plot,
                inputs=[save_filename],
                outputs=[save_status]
            )
            
            # Add some example information
            with gr.Row():
                gr.Markdown("""
                ## 📖 About
                
                This tool visualizes the performance characteristics of different attention mechanisms:
                
                - **DSv3 (DeepSeek V3)**: Uses MLA (Multi-head Latent Attention)
                - **Qwen3 MoE**: Uses GQA (Grouped Query Attention) 
                - **Step-3**: Uses MFA (Multi-head Full Attention)
                
                The rooflines represent theoretical peak performance for different hardware:
                - **H100**: NVIDIA H100 SXM5 (989.4 TFLOPS BF16)
                - **A800**: NVIDIA A800 (312 TFLOPS BF16)
                - **H20**: NVIDIA H20 (148 TFLOPS BF16)
                - **910B**: Huawei Ascend 910B (320 TFLOPS FP16)
                """)
        
        return interface


def main():
    """Launch the Gradio interface."""
    app = GradioAttentionRoofline()
    interface = app.create_interface()
    
    # Launch with sharing enabled for easy access
    interface.launch(
        share=True,
        server_name="0.0.0.0",
        server_port=7860,
        show_error=True
    )


if __name__ == "__main__":
    main()

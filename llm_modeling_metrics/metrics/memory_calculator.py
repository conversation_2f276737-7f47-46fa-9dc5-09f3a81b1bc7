"""
Memory calculation utilities for LLM operations.
"""

from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from ..core.base_model import ParallelConfig


@dataclass
class MixedPrecisionMemoryBreakdown:
    """Memory breakdown with mixed precision details."""

    total: int
    by_component: Dict[str, int]  # 'parameters', 'activations', 'kv_cache', etc.
    by_precision: Dict[str, int]  # 'fp16': bytes, 'fp8': bytes, etc.
    precision_config: Dict[str, str]  # Component -> precision mapping
    efficiency_metrics: Dict[str, float]  # Memory savings, etc.

    def get_memory_savings_vs_fp32(self) -> float:
        """Calculate memory savings compared to fp32 baseline."""
        if not self.by_precision:
            return 0.0

        # Calculate what memory would be with fp32
        fp32_total = 0
        for component, memory in self.by_component.items():
            if component in ["parameters", "gradients", "optimizer_states"]:
                # These scale with precision
                current_precision = self.precision_config.get(
                    f"{component}_dtype", "fp32"
                )
                current_bytes_per_element = MemoryCalculator.PRECISION_BYTES.get(
                    current_precision, 4
                )
                fp32_bytes_per_element = MemoryCalculator.PRECISION_BYTES["fp32"]
                fp32_memory = memory * (
                    fp32_bytes_per_element / current_bytes_per_element
                )
                fp32_total += fp32_memory
            else:
                fp32_total += memory

        if fp32_total == 0:
            return 0.0

        return (fp32_total - self.total) / fp32_total

    def get_precision_distribution(self) -> Dict[str, float]:
        """Get percentage distribution of memory by precision."""
        if self.total == 0:
            return {}

        return {
            precision: (memory / self.total) * 100
            for precision, memory in self.by_precision.items()
        }


class MemoryCalculator:
    """
    Utility class for computing memory requirements in LLM operations.

    Provides detailed memory calculations for parameters, activations, gradients,
    and optimizer states under different parallel strategies and precision settings.
    """

    # Memory constants (bytes per element)
    PRECISION_BYTES = {
        "fp32": 4,
        "fp16": 2,
        "bf16": 2,
        "int8": 1,
        "int4": 0.5,
        "fp8": 1,
        "fp4": 0.5,
    }

    # Cache for precision-specific calculations to improve performance
    _precision_cache = {}
    _memory_cache = {}

    @classmethod
    def _get_cache_key(cls, *args, **kwargs) -> str:
        """Generate cache key for memory calculations."""
        import hashlib
        import json

        # Create a deterministic string from arguments
        cache_data = {
            "args": args,
            "kwargs": {
                k: v
                for k, v in kwargs.items()
                if k not in ["training", "activation_checkpointing"]
            },
        }
        cache_str = json.dumps(cache_data, sort_keys=True, default=str)
        return hashlib.md5(cache_str.encode()).hexdigest()

    @classmethod
    def _clear_cache(cls):
        """Clear all caches (useful for testing)."""
        cls._precision_cache.clear()
        cls._memory_cache.clear()

    @staticmethod
    def compute_mixed_precision_memory(
        total_params: int,
        weight_dtype: str = "fp16",
        grad_dtype: str = "fp16",
        optimizer_dtype: str = "fp32",
        kv_cache_dtype: str = "fp16",
        training: bool = False,
        optimizer_type: str = "adam",
    ) -> Dict[str, int]:
        """
        Compute memory requirements with mixed precision support.

        Args:
            total_params: Total number of parameters
            weight_dtype: Data type for model weights ('fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4')
            grad_dtype: Data type for gradients ('fp32', 'fp16', 'bf16')
            optimizer_dtype: Data type for optimizer states ('fp32', 'fp16', 'bf16')
            kv_cache_dtype: Data type for KV cache ('fp32', 'fp16', 'bf16', 'int8', 'fp8')
            training: Whether this is for training (includes gradients/optimizer)
            optimizer_type: Type of optimizer ('adam', 'sgd', 'adamw')

        Returns:
            Dictionary with mixed precision memory breakdown
        """
        memory = {}

        # Parameter memory
        weight_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(weight_dtype, 2)
        param_memory = total_params * weight_bytes_per_param
        memory["parameters"] = param_memory
        memory["weight_dtype"] = weight_dtype

        # Gradient memory (only for training)
        if training:
            grad_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(grad_dtype, 2)
            gradient_memory = total_params * grad_bytes_per_param
            memory["gradients"] = gradient_memory
            memory["grad_dtype"] = grad_dtype

        # Optimizer state memory (only for training)
        if training:
            optimizer_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(
                optimizer_dtype, 4
            )
            if optimizer_type.lower() in ["adam", "adamw"]:
                # Adam stores first and second moments
                optimizer_memory = total_params * 2 * optimizer_bytes_per_param
            elif optimizer_type.lower() == "sgd":
                # SGD with momentum stores momentum buffer
                optimizer_memory = total_params * optimizer_bytes_per_param
            else:
                # Default to Adam-like memory usage
                optimizer_memory = total_params * 2 * optimizer_bytes_per_param

            memory["optimizer_states"] = optimizer_memory
            memory["optimizer_dtype"] = optimizer_dtype

        # KV cache dtype info (for reference, actual cache memory computed separately)
        memory["kv_cache_dtype"] = kv_cache_dtype

        # Total parameter-related memory
        memory["total"] = sum(v for k, v in memory.items() if isinstance(v, int))

        return memory

    @staticmethod
    def compute_parameter_memory(
        total_params: int,
        precision: str = "fp16",
        include_gradients: bool = True,
        include_optimizer: bool = True,
        optimizer_type: str = "adam",
    ) -> Dict[str, int]:
        """
        Compute memory requirements for model parameters.

        Args:
            total_params: Total number of parameters
            precision: Parameter precision ('fp32', 'fp16', 'bf16', 'int8', 'int4')
            include_gradients: Whether to include gradient memory
            include_optimizer: Whether to include optimizer state memory
            optimizer_type: Type of optimizer ('adam', 'sgd', 'adamw')

        Returns:
            Dictionary with parameter memory breakdown
        """
        memory = {}

        bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(precision, 2)

        # Parameter memory
        param_memory = total_params * bytes_per_param
        memory["parameters"] = param_memory

        # Gradient memory (typically same precision as parameters)
        if include_gradients:
            gradient_memory = total_params * bytes_per_param
            memory["gradients"] = gradient_memory

        # Optimizer state memory
        if include_optimizer:
            if optimizer_type.lower() in ["adam", "adamw"]:
                # Adam stores first and second moments (both fp32 typically)
                optimizer_memory = (
                    total_params * 2 * MemoryCalculator.PRECISION_BYTES["fp32"]
                )
            elif optimizer_type.lower() == "sgd":
                # SGD with momentum stores momentum buffer
                optimizer_memory = (
                    total_params * MemoryCalculator.PRECISION_BYTES["fp32"]
                )
            else:
                # Default to Adam-like memory usage
                optimizer_memory = (
                    total_params * 2 * MemoryCalculator.PRECISION_BYTES["fp32"]
                )

            memory["optimizer_states"] = optimizer_memory

        # Total parameter-related memory
        memory["total"] = sum(memory.values())

        return memory

    @staticmethod
    def compute_activation_memory(
        model_config: Dict[str, Any],
        sequence_length: int = 2048,
        batch_size: int = 1,
        precision: str = "fp16",
        activation_checkpointing: bool = False,
    ) -> Dict[str, int]:
        """
        Compute memory requirements for activations.

        Args:
            model_config: Model configuration dictionary
            sequence_length: Input sequence length
            batch_size: Batch size
            precision: Activation precision
            activation_checkpointing: Whether activation checkpointing is used

        Returns:
            Dictionary with activation memory breakdown
        """
        memory = {}

        bytes_per_element = MemoryCalculator.PRECISION_BYTES.get(precision, 2)

        # Extract configuration parameters
        hidden_size = model_config.get("hidden_size", 0)
        num_layers = model_config.get("num_hidden_layers", 0)
        num_heads = model_config.get("num_attention_heads", 0)
        intermediate_size = model_config.get("intermediate_size", 0)
        vocab_size = model_config.get("vocab_size", 0)

        # Input embeddings
        input_embedding_memory = (
            batch_size * sequence_length * hidden_size * bytes_per_element
        )
        memory["input_embeddings"] = input_embedding_memory

        # Per-layer activation memory
        layer_memory = MemoryCalculator._compute_layer_activation_memory(
            hidden_size,
            num_heads,
            intermediate_size,
            sequence_length,
            batch_size,
            bytes_per_element,
            model_config,
        )

        if activation_checkpointing:
            # With activation checkpointing, we only store activations for a subset of layers
            # Typically store every sqrt(num_layers) layers
            import math

            checkpointed_layers = max(1, int(math.sqrt(num_layers)))
            total_layer_memory = layer_memory * checkpointed_layers
        else:
            # Without checkpointing, store activations for all layers
            total_layer_memory = layer_memory * num_layers

        memory["layer_activations"] = total_layer_memory

        # Output layer activations (logits)
        output_memory = batch_size * sequence_length * vocab_size * bytes_per_element
        memory["output_logits"] = output_memory

        # Attention matrices (can be very large for long sequences)
        attention_memory = MemoryCalculator._compute_attention_activation_memory(
            num_heads,
            sequence_length,
            batch_size,
            bytes_per_element,
            num_layers,
            activation_checkpointing,
        )
        memory["attention_matrices"] = attention_memory

        # Total activation memory
        memory["total"] = sum(memory.values())

        return memory

    @staticmethod
    def _compute_layer_activation_memory(
        hidden_size: int,
        num_heads: int,
        intermediate_size: int,
        sequence_length: int,
        batch_size: int,
        bytes_per_element: int,
        model_config: Dict[str, Any],
    ) -> int:
        """Compute activation memory for a single layer."""
        layer_memory = 0

        # Hidden states (input to layer)
        hidden_state_memory = (
            batch_size * sequence_length * hidden_size * bytes_per_element
        )
        layer_memory += hidden_state_memory

        # Attention intermediate activations
        head_dim = hidden_size // num_heads

        # Q, K, V tensors
        qkv_memory = 3 * batch_size * sequence_length * hidden_size * bytes_per_element
        layer_memory += qkv_memory

        # Attention output before projection
        attn_output_memory = (
            batch_size * sequence_length * hidden_size * bytes_per_element
        )
        layer_memory += attn_output_memory

        # MLP intermediate activations
        is_moe = "num_experts" in model_config or "n_routed_experts" in model_config

        if is_moe:
            # For MoE, only activated experts contribute to memory
            experts_per_token = model_config.get(
                "num_experts_per_tok", model_config.get("experts_per_token", 2)
            )
            shared_experts = model_config.get("n_shared_experts", 0)

            # Router activations
            num_experts = model_config.get(
                "num_experts", model_config.get("n_routed_experts", 0)
            )
            router_memory = (
                batch_size * sequence_length * num_experts * bytes_per_element
            )
            layer_memory += router_memory

            # Expert intermediate activations (only for active experts)
            total_tokens = batch_size * sequence_length
            active_expert_memory = (
                total_tokens
                * experts_per_token
                * intermediate_size
                * bytes_per_element
                * 2
            )  # gate + up
            layer_memory += active_expert_memory

            # Shared expert memory (if any)
            if shared_experts > 0:
                shared_expert_memory = (
                    total_tokens
                    * shared_experts
                    * intermediate_size
                    * bytes_per_element
                    * 2
                )
                layer_memory += shared_expert_memory
        else:
            # Dense MLP
            # Gate and up projections (for gated activations like SwiGLU)
            mlp_intermediate_memory = (
                batch_size * sequence_length * intermediate_size * bytes_per_element * 2
            )
            layer_memory += mlp_intermediate_memory

        return layer_memory

    @staticmethod
    def _compute_attention_activation_memory(
        num_heads: int,
        sequence_length: int,
        batch_size: int,
        bytes_per_element: int,
        num_layers: int,
        activation_checkpointing: bool,
    ) -> int:
        """Compute memory for attention matrices."""
        # Attention scores and probabilities
        # Shape: (batch_size, num_heads, seq_len, seq_len)
        attention_matrix_size = (
            batch_size * num_heads * sequence_length * sequence_length
        )
        attention_memory_per_layer = (
            attention_matrix_size * bytes_per_element * 2
        )  # scores + probs

        if activation_checkpointing:
            # With checkpointing, attention matrices are recomputed
            # We only store them for the current layer being processed
            total_attention_memory = attention_memory_per_layer
        else:
            # Without checkpointing, store attention matrices for all layers
            total_attention_memory = attention_memory_per_layer * num_layers

        return total_attention_memory

    @staticmethod
    def compute_kv_cache_memory(
        model_config: Dict[str, Any],
        sequence_length: int = 2048,
        batch_size: int = 1,
        precision: str = "fp16",
        dtype: Optional[str] = None,
    ) -> Dict[str, int]:
        """
        Compute memory requirements for KV cache during inference.

        Args:
            model_config: Model configuration dictionary
            sequence_length: Maximum sequence length to cache
            batch_size: Batch size
            precision: Cache precision (deprecated, use dtype instead)
            dtype: Data type for KV cache ('fp16', 'bf16', 'fp32', 'int8')

        Returns:
            Dictionary with KV cache memory breakdown
        """
        memory = {}

        # Use dtype if provided, otherwise fall back to precision for backward compatibility
        effective_dtype = dtype if dtype is not None else precision
        bytes_per_element = MemoryCalculator.PRECISION_BYTES.get(effective_dtype, 2)

        # Extract configuration parameters
        hidden_size = model_config.get("hidden_size", 0)
        num_layers = model_config.get("num_hidden_layers", 0)
        num_heads = model_config.get("num_attention_heads", 0)
        num_kv_heads = model_config.get("num_key_value_heads", num_heads)
        head_dim = hidden_size // num_heads

        # Check if this is MLA (Multi-head Latent Attention)
        kv_lora_rank = model_config.get("kv_lora_rank")
        qk_rope_head_dim = model_config.get("qk_rope_head_dim", 64)

        if kv_lora_rank is not None:
            # MLA: KV cache stores compressed representation
            # Cache size per layer: batch_size * seq_len * (kv_lora_rank + qk_rope_head_dim)
            kv_cache_dim = kv_lora_rank + qk_rope_head_dim
            kv_cache_per_layer = (
                batch_size * sequence_length * kv_cache_dim * bytes_per_element
            )

            memory["kv_cache_per_layer"] = kv_cache_per_layer
            memory["kv_lora_rank"] = kv_lora_rank
            memory["qk_rope_head_dim"] = qk_rope_head_dim
            memory["kv_cache_dim"] = kv_cache_dim
            memory["attention_type"] = "MLA"

            # Memory growth per new token for MLA
            kv_cache_per_token = (
                batch_size * kv_cache_dim * bytes_per_element * num_layers
            )
        else:
            # Standard attention: separate K and V caches
            # K cache: (batch_size, num_kv_heads, seq_len, head_dim)
            k_cache_per_layer = (
                batch_size
                * num_kv_heads
                * sequence_length
                * head_dim
                * bytes_per_element
            )

            # V cache: (batch_size, num_kv_heads, seq_len, head_dim)
            v_cache_per_layer = (
                batch_size
                * num_kv_heads
                * sequence_length
                * head_dim
                * bytes_per_element
            )

            kv_cache_per_layer = k_cache_per_layer + v_cache_per_layer

            memory["k_cache_per_layer"] = k_cache_per_layer
            memory["v_cache_per_layer"] = v_cache_per_layer
            memory["kv_cache_per_layer"] = kv_cache_per_layer
            memory["attention_type"] = "Standard"

            # Memory growth per new token for standard attention
            kv_cache_per_token = (
                batch_size
                * num_kv_heads
                * head_dim
                * bytes_per_element
                * 2
                * num_layers
            )

        # Total KV cache for all layers
        total_kv_cache = kv_cache_per_layer * num_layers
        memory["total_kv_cache"] = total_kv_cache
        memory["kv_cache_per_token"] = kv_cache_per_token

        memory["total"] = total_kv_cache

        return memory

    @staticmethod
    def compute_kv_cache_memory_by_dtype(
        model_config: Dict[str, Any],
        sequence_length: int,
        batch_size: int = 1,
        dtype: str = "fp16",
    ) -> Dict[str, int]:
        """
        Compute KV cache memory with configurable dtype.

        Args:
            model_config: Model configuration dictionary
            sequence_length: Maximum sequence length to cache
            batch_size: Batch size
            dtype: Data type for KV cache ('fp16', 'bf16', 'fp32', 'int8')

        Returns:
            Dictionary with KV cache memory breakdown including dtype info
        """
        memory = MemoryCalculator.compute_kv_cache_memory(
            model_config, sequence_length, batch_size, dtype=dtype
        )

        # Add dtype information to the result
        memory["dtype"] = dtype
        memory["bytes_per_element"] = MemoryCalculator.PRECISION_BYTES.get(dtype, 2)

        return memory

    @staticmethod
    def analyze_memory_growth_by_sequence_length(
        model_config: Dict[str, Any],
        total_params: int,
        sequence_lengths: list,
        batch_size: int = 1,
        dtype: str = "fp16",
    ) -> Dict[str, Any]:
        """
        Analyze memory growth patterns across sequence lengths.

        Args:
            model_config: Model configuration dictionary
            total_params: Total number of parameters
            sequence_lengths: List of sequence lengths to analyze
            batch_size: Batch size
            dtype: Data type for calculations

        Returns:
            Dictionary with memory growth analysis including attention mechanism info
        """
        results = {
            "sequence_lengths": sequence_lengths,
            "dtype": dtype,
            "batch_size": batch_size,
            "attention_mechanism": MemoryCalculator.get_attention_mechanism_info(
                model_config
            ),
            "memory_data": [],
        }

        for seq_len in sequence_lengths:
            # Compute KV cache memory for this sequence length
            kv_memory = MemoryCalculator.compute_kv_cache_memory_by_dtype(
                model_config, seq_len, batch_size, dtype
            )

            # Compute total memory including parameters and activations
            total_memory = MemoryCalculator.compute_total_memory_requirements(
                model_config,
                total_params,
                seq_len,
                batch_size,
                dtype,
                training=False,
                include_kv_cache=True,
            )

            import logging

            logging.info(f"total_memory={total_memory}")

            memory_point = {
                "sequence_length": seq_len,
                "kv_cache_memory": kv_memory["total"],
                "total_memory": total_memory["total"],
                "parameter_memory": total_memory.get("param_parameters", 0),
                "activation_memory": total_memory.get("activation_total", 0),
                "memory_per_token": kv_memory.get("kv_cache_per_token", 0),
                "debug_total_memory_dict": total_memory,  # Temporary debug info
            }

            results["memory_data"].append(memory_point)

        return results

    @staticmethod
    def get_attention_mechanism_info(model_config: Dict[str, Any]) -> Dict[str, str]:
        """
        Determine attention mechanism type (MHA, GQA, MLA) from model configuration.

        Args:
            model_config: Model configuration dictionary

        Returns:
            Dictionary with attention mechanism information
        """
        num_attention_heads = model_config.get("num_attention_heads", 0)
        num_key_value_heads = model_config.get(
            "num_key_value_heads", num_attention_heads
        )

        # Check for MLA (Multi-head Latent Attention) indicators
        # MLA typically has specific configuration keys like kv_lora_rank or qk_rope_head_dim
        mla_indicators = [
            "kv_lora_rank",
            "qk_rope_head_dim",
            "kv_head_dim",
            "qk_nope_head_dim",
            "q_lora_rank",
            "kv_a_proj_with_mqa",
            "qk_head_dim",
        ]

        has_mla_indicators = any(
            key in model_config and model_config[key] is not None
            for key in mla_indicators
        )

        if has_mla_indicators:
            mechanism_type = "MLA"
            description = "Multi-head Latent Attention"
        elif num_key_value_heads < num_attention_heads and num_key_value_heads > 0:
            mechanism_type = "GQA"
            description = "Grouped Query Attention"
        elif num_key_value_heads == num_attention_heads:
            mechanism_type = "MHA"
            description = "Multi-Head Attention"
        else:
            mechanism_type = "Unknown"
            description = "Unknown attention mechanism"

        return {
            "type": mechanism_type,
            "description": description,
            "num_attention_heads": num_attention_heads,
            "num_key_value_heads": num_key_value_heads,
            "head_ratio": (
                num_attention_heads / max(num_key_value_heads, 1)
                if num_key_value_heads > 0
                else 1.0
            ),
        }

    @classmethod
    def compute_mixed_precision_total_memory(
        cls,
        model_config: Dict[str, Any],
        total_params: int,
        sequence_length: int = 2048,
        batch_size: int = 1,
        weight_dtype: str = "bf16",
        activation_dtype: str = "bf16",
        grad_dtype: str = "fp16",
        optimizer_dtype: str = "fp32",
        kv_cache_dtype: str = "bf16",
        training: bool = False,
        activation_checkpointing: bool = False,
        include_kv_cache: bool = False,
        optimizer_type: str = "adam",
    ) -> MixedPrecisionMemoryBreakdown:
        """
        Compute total memory requirements with mixed precision support.

        Args:
            model_config: Model configuration dictionary
            total_params: Total number of parameters
            sequence_length: Input sequence length
            batch_size: Batch size
            weight_dtype: Data type for model weights
            activation_dtype: Data type for activations
            grad_dtype: Data type for gradients (training only)
            optimizer_dtype: Data type for optimizer states (training only)
            kv_cache_dtype: Data type for KV cache (inference only)
            training: Whether this is for training
            activation_checkpointing: Whether activation checkpointing is used
            include_kv_cache: Whether to include KV cache memory (inference only)
            optimizer_type: Type of optimizer

        Returns:
            MixedPrecisionMemoryBreakdown with complete mixed precision memory analysis
        """
        # Generate cache key for performance optimization
        cache_key = cls._get_cache_key(
            model_config,
            total_params,
            sequence_length,
            batch_size,
            weight_dtype,
            activation_dtype,
            grad_dtype,
            optimizer_dtype,
            kv_cache_dtype,
            include_kv_cache,
            optimizer_type,
        )

        # Check cache first (excluding training and activation_checkpointing from cache key)
        cache_key_with_flags = f"{cache_key}_{training}_{activation_checkpointing}"
        if cache_key_with_flags in cls._memory_cache:
            return cls._memory_cache[cache_key_with_flags]

        # Validate precision types
        precisions = {
            "weight": weight_dtype,
            "activation": activation_dtype,
            "kv_cache": kv_cache_dtype,
        }
        if training:
            precisions.update({"grad": grad_dtype, "optimizer": optimizer_dtype})

        for component, precision in precisions.items():
            if precision not in cls.PRECISION_BYTES:
                raise ValueError(f"Unsupported precision '{precision}' for {component}")

        # Component memory calculations
        by_component = {}
        by_precision = {}

        # Mixed precision parameter memory
        param_memory = cls.compute_mixed_precision_memory(
            total_params,
            weight_dtype,
            grad_dtype,
            optimizer_dtype,
            kv_cache_dtype,
            training,
            optimizer_type,
        )

        # Extract component memory
        by_component["parameters"] = param_memory.get("parameters", 0)
        if training:
            by_component["gradients"] = param_memory.get("gradients", 0)
            by_component["optimizer_states"] = param_memory.get("optimizer_states", 0)

        # Activation memory with specified dtype
        activation_memory = cls.compute_activation_memory(
            model_config,
            sequence_length,
            batch_size,
            activation_dtype,
            activation_checkpointing,
        )
        by_component["activations"] = activation_memory.get("total", 0)

        # KV cache memory with specified dtype (can be included for both training and inference)
        if include_kv_cache:
            kv_cache_memory = cls.compute_kv_cache_memory(
                model_config, sequence_length, batch_size, dtype=kv_cache_dtype
            )
            by_component["kv_cache"] = kv_cache_memory.get("total", 0)

        # Calculate memory by precision
        by_precision[weight_dtype] = (
            by_precision.get(weight_dtype, 0) + by_component["parameters"]
        )
        by_precision[activation_dtype] = (
            by_precision.get(activation_dtype, 0) + by_component["activations"]
        )

        if training:
            by_precision[grad_dtype] = (
                by_precision.get(grad_dtype, 0) + by_component["gradients"]
            )
            by_precision[optimizer_dtype] = (
                by_precision.get(optimizer_dtype, 0) + by_component["optimizer_states"]
            )

        if include_kv_cache:
            by_precision[kv_cache_dtype] = (
                by_precision.get(kv_cache_dtype, 0) + by_component["kv_cache"]
            )

        # Total memory
        total_memory = sum(by_component.values())

        # Precision configuration
        precision_config = {
            "weight_dtype": weight_dtype,
            "activation_dtype": activation_dtype,
            "kv_cache_dtype": kv_cache_dtype if include_kv_cache else None,
        }
        if training:
            precision_config.update(
                {"grad_dtype": grad_dtype, "optimizer_dtype": optimizer_dtype}
            )

        # Calculate efficiency metrics
        efficiency_metrics = cls._calculate_efficiency_metrics(
            by_component, by_precision, precision_config
        )

        result = MixedPrecisionMemoryBreakdown(
            total=total_memory,
            by_component=by_component,
            by_precision=by_precision,
            precision_config=precision_config,
            efficiency_metrics=efficiency_metrics,
        )

        # Cache the result for future use
        cls._memory_cache[cache_key_with_flags] = result

        return result

    @staticmethod
    def _calculate_efficiency_metrics(
        by_component: Dict[str, int],
        by_precision: Dict[str, int],
        precision_config: Dict[str, str],
    ) -> Dict[str, float]:
        """Calculate efficiency metrics for mixed precision memory usage."""
        metrics = {}

        total_memory = sum(by_component.values())
        if total_memory == 0:
            return metrics

        # Calculate memory savings vs fp32 baseline
        fp32_total = 0
        for component, memory in by_component.items():
            if component in ["parameters", "gradients", "optimizer_states"]:
                # These scale with precision
                component_dtype_key = (
                    f'{component.rstrip("s")}_dtype'  # parameters -> parameter_dtype
                )
                if component == "parameters":
                    component_dtype_key = "weight_dtype"

                current_precision = precision_config.get(component_dtype_key, "fp32")
                current_bytes_per_element = MemoryCalculator.PRECISION_BYTES.get(
                    current_precision, 4
                )
                fp32_bytes_per_element = MemoryCalculator.PRECISION_BYTES["fp32"]
                fp32_memory = memory * (
                    fp32_bytes_per_element / current_bytes_per_element
                )
                fp32_total += fp32_memory
            else:
                fp32_total += memory

        if fp32_total > 0:
            metrics["memory_savings_vs_fp32"] = (fp32_total - total_memory) / fp32_total

        # Calculate memory savings vs bf16 baseline
        bf16_total = 0
        for component, memory in by_component.items():
            if component in [
                "parameters",
                "gradients",
                "optimizer_states",
                "activations",
                "kv_cache",
            ]:
                # These scale with precision
                component_dtype_key = f'{component.rstrip("s")}_dtype'
                if component == "parameters":
                    component_dtype_key = "weight_dtype"
                elif component == "activations":
                    component_dtype_key = "activation_dtype"

                current_precision = precision_config.get(component_dtype_key, "bf16")
                current_bytes_per_element = MemoryCalculator.PRECISION_BYTES.get(
                    current_precision, 2
                )
                bf16_bytes_per_element = MemoryCalculator.PRECISION_BYTES["bf16"]
                bf16_memory = memory * (
                    bf16_bytes_per_element / current_bytes_per_element
                )
                bf16_total += bf16_memory
            else:
                bf16_total += memory

        if bf16_total > 0:
            metrics["memory_savings_vs_bf16"] = (bf16_total - total_memory) / bf16_total

        # Calculate precision distribution
        metrics["precision_distribution"] = {
            precision: (memory / total_memory) * 100
            for precision, memory in by_precision.items()
        }

        # Calculate compression ratio (total memory / fp32 baseline)
        if fp32_total > 0:
            metrics["compression_ratio"] = total_memory / fp32_total

        return metrics

    @staticmethod
    def compute_total_memory_requirements(
        model_config: Dict[str, Any],
        total_params: int,
        sequence_length: int = 2048,
        batch_size: int = 1,
        precision: str = "fp16",
        training: bool = False,
        activation_checkpointing: bool = False,
        include_kv_cache: bool = False,
        # Mixed precision parameters (optional)
        weight_dtype: str = None,
        activation_dtype: str = None,
        grad_dtype: str = None,
        optimizer_dtype: str = None,
        kv_cache_dtype: str = None,
    ) -> Dict[str, int]:
        """
        Compute total memory requirements for model execution.

        Args:
            model_config: Model configuration dictionary
            total_params: Total number of parameters
            sequence_length: Input sequence length
            batch_size: Batch size
            precision: Model precision (legacy parameter, used as fallback)
            training: Whether this is for training (includes gradients/optimizer)
            activation_checkpointing: Whether activation checkpointing is used
            include_kv_cache: Whether to include KV cache memory (for inference)
            weight_dtype: Data type for weights (overrides precision)
            activation_dtype: Data type for activations (overrides precision)
            grad_dtype: Data type for gradients (overrides precision)
            optimizer_dtype: Data type for optimizer states (overrides precision)
            kv_cache_dtype: Data type for KV cache (overrides precision)

        Returns:
            Dictionary with complete memory breakdown
        """
        # Use mixed precision parameters if provided, otherwise fall back to precision
        effective_weight_dtype = weight_dtype or precision
        effective_activation_dtype = activation_dtype or precision
        effective_grad_dtype = grad_dtype or precision
        effective_optimizer_dtype = (
            optimizer_dtype or "fp32"
        )  # Optimizer typically uses fp32
        effective_kv_cache_dtype = kv_cache_dtype or precision

        # If any mixed precision parameters are provided, use the enhanced method
        if any(
            [
                weight_dtype,
                activation_dtype,
                grad_dtype,
                optimizer_dtype,
                kv_cache_dtype,
            ]
        ):
            mixed_precision_result = (
                MemoryCalculator.compute_mixed_precision_total_memory(
                    model_config,
                    total_params,
                    sequence_length,
                    batch_size,
                    effective_weight_dtype,
                    effective_activation_dtype,
                    effective_grad_dtype,
                    effective_optimizer_dtype,
                    effective_kv_cache_dtype,
                    training,
                    activation_checkpointing,
                    include_kv_cache,
                )
            )

            # Convert to legacy format for backward compatibility
            memory = {}
            memory.update(
                {
                    f"param_{k}": v
                    for k, v in mixed_precision_result.by_component.items()
                    if k in ["parameters", "gradients", "optimizer_states"]
                }
            )
            memory["activation_total"] = mixed_precision_result.by_component.get(
                "activations", 0
            )
            if include_kv_cache and not training:
                memory["kv_cache_total"] = mixed_precision_result.by_component.get(
                    "kv_cache", 0
                )
            memory["total"] = mixed_precision_result.total
            memory["mixed_precision_breakdown"] = mixed_precision_result

            return memory

        # Legacy single-precision calculation
        memory = {}

        # Parameter memory
        param_memory = MemoryCalculator.compute_parameter_memory(
            total_params, precision, training, training, "adam"
        )
        memory.update({f"param_{k}": v for k, v in param_memory.items()})

        # Activation memory
        activation_memory = MemoryCalculator.compute_activation_memory(
            model_config,
            sequence_length,
            batch_size,
            precision,
            activation_checkpointing,
        )
        memory.update({f"activation_{k}": v for k, v in activation_memory.items()})

        # KV cache memory (for inference)
        if include_kv_cache and not training:
            kv_cache_memory = MemoryCalculator.compute_kv_cache_memory(
                model_config, sequence_length, batch_size, precision
            )
            memory.update({f"kv_cache_{k}": v for k, v in kv_cache_memory.items()})

        # Total memory
        memory["total"] = memory["param_total"] + memory["activation_total"]
        if include_kv_cache and not training:
            memory["total"] += memory.get("kv_cache_total", 0)

        return memory

    @staticmethod
    def create_mixed_precision_breakdown_from_legacy(
        memory: Dict[str, int], precision_config: Dict[str, str]
    ) -> MixedPrecisionMemoryBreakdown:
        """
        Create a MixedPrecisionMemoryBreakdown from legacy memory calculations.

        Args:
            memory: Legacy memory breakdown dictionary
            precision_config: Precision configuration mapping

        Returns:
            MixedPrecisionMemoryBreakdown instance
        """
        # Extract component memory
        by_component = {}
        by_component["parameters"] = memory.get("param_parameters", 0)
        by_component["gradients"] = memory.get("param_gradients", 0)
        by_component["optimizer_states"] = memory.get("param_optimizer_states", 0)
        by_component["activations"] = memory.get("activation_total", 0)
        by_component["kv_cache"] = memory.get("kv_cache_total", 0)

        # Remove zero components
        by_component = {k: v for k, v in by_component.items() if v > 0}

        # Calculate memory by precision
        by_precision = {}
        for component, memory_amount in by_component.items():
            if component == "parameters":
                precision = precision_config.get("weight_dtype", "fp16")
            elif component == "gradients":
                precision = precision_config.get("grad_dtype", "fp16")
            elif component == "optimizer_states":
                precision = precision_config.get("optimizer_dtype", "fp32")
            elif component == "activations":
                precision = precision_config.get("activation_dtype", "fp16")
            elif component == "kv_cache":
                precision = precision_config.get("kv_cache_dtype", "fp16")
            else:
                precision = "fp16"  # Default fallback

            by_precision[precision] = by_precision.get(precision, 0) + memory_amount

        total_memory = sum(by_component.values())

        # Calculate efficiency metrics
        efficiency_metrics = MemoryCalculator._calculate_efficiency_metrics(
            by_component, by_precision, precision_config
        )

        return MixedPrecisionMemoryBreakdown(
            total=total_memory,
            by_component=by_component,
            by_precision=by_precision,
            precision_config=precision_config,
            efficiency_metrics=efficiency_metrics,
        )

    @staticmethod
    def adjust_memory_for_parallel(
        memory: Dict[str, int], parallel_config: ParallelConfig, total_params: int
    ) -> Dict[str, int]:
        """
        Adjust memory calculations for parallel execution.

        Args:
            memory: Original memory calculations
            parallel_config: Parallel configuration
            total_params: Total number of parameters

        Returns:
            Adjusted memory calculations per device
        """
        adjusted_memory = memory.copy()

        tp_size = parallel_config.tensor_parallel_size
        pp_size = parallel_config.pipeline_parallel_size
        dp_size = parallel_config.data_parallel_size

        # Tensor parallelism: parameters are sharded across devices
        if tp_size > 1:
            # Parameter memory is divided by TP size
            for key in adjusted_memory:
                if "param_" in key and key != "param_total":
                    adjusted_memory[f"{key}_per_device"] = (
                        adjusted_memory[key] // tp_size
                    )

        # Pipeline parallelism: layers are distributed across devices
        if pp_size > 1:
            # Activation memory is roughly divided by PP size
            for key in adjusted_memory:
                if "activation_" in key and "layer" in key:
                    adjusted_memory[f"{key}_per_device"] = (
                        adjusted_memory[key] // pp_size
                    )

        # Data parallelism: each device processes a subset of the batch
        if dp_size > 1:
            # Activation memory scales with batch size, so divide by DP size
            for key in adjusted_memory:
                if "activation_" in key:
                    if f"{key}_per_device" not in adjusted_memory:
                        adjusted_memory[f"{key}_per_device"] = (
                            adjusted_memory[key] // dp_size
                        )
                    else:
                        adjusted_memory[f"{key}_per_device"] //= dp_size

        # Calculate total memory per device
        per_device_keys = [k for k in adjusted_memory.keys() if "_per_device" in k]
        if per_device_keys:
            adjusted_memory["total_per_device"] = sum(
                adjusted_memory[k] for k in per_device_keys
            )

        # Communication buffer memory (for gradient synchronization)
        if dp_size > 1 or tp_size > 1:
            # Estimate communication buffer as a fraction of parameter memory
            param_memory = adjusted_memory.get("param_parameters", 0)
            comm_buffer_memory = int(param_memory * 0.1)  # 10% of parameter memory
            adjusted_memory["communication_buffers"] = comm_buffer_memory

        return adjusted_memory

    @staticmethod
    def get_memory_breakdown_summary(memory: Dict[str, int]) -> Dict[str, Any]:
        """
        Get a summary of memory breakdown with percentages and human-readable values.

        Args:
            memory: Memory calculations dictionary

        Returns:
            Summary with percentages and human-readable values
        """
        total_memory = memory.get("total", 0)
        if total_memory == 0:
            return {"total": 0, "breakdown": {}}

        summary = {
            "total": total_memory,
            "total_human": MemoryCalculator._format_memory(total_memory),
            "breakdown": {},
        }

        # Group related memory components
        component_groups = {
            "parameters": [
                "param_parameters",
                "param_gradients",
                "param_optimizer_states",
            ],
            "activations": [
                "activation_input_embeddings",
                "activation_layer_activations",
                "activation_attention_matrices",
                "activation_output_logits",
            ],
            "kv_cache": ["kv_cache_total_kv_cache"],
            "communication": ["communication_buffers"],
        }

        for group_name, keys in component_groups.items():
            group_memory = sum(memory.get(key, 0) for key in keys)
            if group_memory > 0:
                percentage = (group_memory / total_memory) * 100
                summary["breakdown"][group_name] = {
                    "memory": group_memory,
                    "memory_human": MemoryCalculator._format_memory(group_memory),
                    "percentage": round(percentage, 2),
                }

        return summary

    @staticmethod
    def estimate_memory_for_sequence_lengths(
        model_config: Dict[str, Any],
        total_params: int,
        sequence_lengths: list,
        batch_size: int = 1,
        precision: str = "fp16",
    ) -> Dict[int, Dict[str, int]]:
        """
        Estimate memory requirements for different sequence lengths.

        Args:
            model_config: Model configuration dictionary
            total_params: Total number of parameters
            sequence_lengths: List of sequence lengths to analyze
            batch_size: Batch size
            precision: Model precision

        Returns:
            Dictionary mapping sequence lengths to memory requirements
        """
        results = {}

        for seq_len in sequence_lengths:
            memory = MemoryCalculator.compute_total_memory_requirements(
                model_config,
                total_params,
                seq_len,
                batch_size,
                precision,
                training=False,
                include_kv_cache=True,
            )
            results[seq_len] = memory

        return results

    @staticmethod
    def get_mixed_precision_memory_summary(
        breakdown: MixedPrecisionMemoryBreakdown,
    ) -> Dict[str, Any]:
        """
        Get a human-readable summary of mixed precision memory breakdown.

        Args:
            breakdown: MixedPrecisionMemoryBreakdown instance

        Returns:
            Dictionary with formatted summary information
        """
        summary = {
            "total": breakdown.total,
            "total_human": MemoryCalculator._format_memory(breakdown.total),
            "by_component": {},
            "by_precision": {},
            "efficiency_metrics": breakdown.efficiency_metrics,
            "precision_config": breakdown.precision_config,
        }

        # Format component breakdown
        for component, memory in breakdown.by_component.items():
            summary["by_component"][component] = {
                "bytes": memory,
                "human": MemoryCalculator._format_memory(memory),
                "percentage": (
                    (memory / breakdown.total * 100) if breakdown.total > 0 else 0
                ),
            }

        # Format precision breakdown
        for precision, memory in breakdown.by_precision.items():
            summary["by_precision"][precision] = {
                "bytes": memory,
                "human": MemoryCalculator._format_memory(memory),
                "percentage": (
                    (memory / breakdown.total * 100) if breakdown.total > 0 else 0
                ),
                "bytes_per_element": MemoryCalculator.PRECISION_BYTES.get(precision, 2),
            }

        # Add efficiency insights
        if breakdown.efficiency_metrics:
            summary["insights"] = []

            if "memory_savings_vs_fp32" in breakdown.efficiency_metrics:
                savings = breakdown.efficiency_metrics["memory_savings_vs_fp32"] * 100
                if savings > 0:
                    summary["insights"].append(
                        f"Saves {savings:.1f}% memory vs fp32 baseline"
                    )

            if "memory_savings_vs_bf16" in breakdown.efficiency_metrics:
                savings = breakdown.efficiency_metrics["memory_savings_vs_bf16"] * 100
                if savings > 0:
                    summary["insights"].append(
                        f"Saves {savings:.1f}% memory vs bf16 baseline"
                    )
                elif savings < 0:
                    summary["insights"].append(
                        f"Uses {abs(savings):.1f}% more memory vs bf16 baseline"
                    )

            if "compression_ratio" in breakdown.efficiency_metrics:
                ratio = breakdown.efficiency_metrics["compression_ratio"]
                summary["insights"].append(f"Compression ratio: {ratio:.2f}x vs fp32")

        return summary

    @staticmethod
    def _format_memory(memory_bytes: int) -> str:
        """Format memory size in human-readable form."""
        if memory_bytes >= 1024**4:
            return f"{memory_bytes / (1024**4):.2f}TB"
        elif memory_bytes >= 1024**3:
            return f"{memory_bytes / (1024**3):.2f}GB"
        elif memory_bytes >= 1024**2:
            return f"{memory_bytes / (1024**2):.2f}MB"
        elif memory_bytes >= 1024:
            return f"{memory_bytes / 1024:.2f}KB"
        else:
            return f"{memory_bytes}B"

"""
Reproduce the compute vs memory access plot for different attention mechanisms.

This script analyzes different attention mechanisms (MLA, GQA, MFA) across various
context lengths and plots them against hardware rooflines for H100, A800, H20, and 910B.
"""

import os
import sys
from typing import Any, Dict, List, Tuple

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

sns.set_style("whitegrid")
sns.set_palette("husl")


# Add the package to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm_modeling_metrics import ModelFactory
from llm_modeling_metrics.core.operators import (
    AttentionOperator,
    HardwareSpecs,
    MLAAttentionOperator,
)


class AttentionRooflineAnalyzer:
    """Analyzer for attention mechanisms on roofline plots."""

    def __init__(self):
        """Initialize the analyzer with hardware specifications."""
        self.hardware_specs = self._load_hardware_specs()

        self.create_models()

    def _load_hardware_specs(self) -> Dict[str, HardwareSpecs]:
        """Load hardware specifications from GPU specs file."""
        specs = {}

        # H100 SXM5
        specs["H100"] = HardwareSpecs(
            name="NVIDIA H100 SXM5",
            peak_flops={
                "bf16_tensor": 989.4,  # TFLOPS
                "fp16_tensor": 989.4,
                "fp8_tensor": 1978.9,
                "fp32": 66.9,
            },
            memory_bandwidth_gbps=3352,  # GB/s
            memory_size_gb=80,
            tensor_cores=528,
        )

        # A100 (representing A800 with similar specs)
        specs["A800"] = HardwareSpecs(
            name="NVIDIA A800",
            peak_flops={"bf16_tensor": 312, "fp16_tensor": 312, "fp32": 19.5},  # TFLOPS
            memory_bandwidth_gbps=1555,  # GB/s
            memory_size_gb=40,
            tensor_cores=432,
        )

        # H20
        specs["H20"] = HardwareSpecs(
            name="NVIDIA H20",
            peak_flops={"bf16_tensor": 148, "fp8_tensor": 296, "fp32": 44},  # TFLOPS
            memory_bandwidth_gbps=4000,  # GB/s
            memory_size_gb=96,
            tensor_cores=400,  # Estimated
        )

        # 910B (Huawei Ascend)
        specs["910B"] = HardwareSpecs(
            name="Huawei Ascend 910B",
            peak_flops={
                "fp16_tensor": 320,  # TFLOPS
                "int8_tensor": 640,
                "fp32": 160,  # Estimated
            },
            memory_bandwidth_gbps=1600,  # GB/s
            memory_size_gb=64,
            tensor_cores=None,
        )

        return specs

    def create_models(self) -> Dict[str, Any]:
        """Create real models for analysis using ModelFactory."""
        models = {}

        # DeepSeek V3 with MLA Attention
        print("Loading DeepSeek V3 model...")
        models["DSv3_MLA"] = {
            "model": ModelFactory.create_model("deepseek-ai/DeepSeek-V3"),
            "name": "DSv3, 8K-32K",
            "color": "blue",
            "marker": "o",
            "scale_factor": 1.0,  # Use realistic values
        }

        # Qwen3 MoE (Qwen/Qwen3-235B-A22B)
        print("Loading Qwen3 MoE model...")
        models["Qwen3_MoE"] = {
            "model": ModelFactory.create_model("Qwen/Qwen3-235B-A22B"),
            "name": "Qwen3 MoE, 8K-32K",
            "color": "blue",
            "marker": "o",
            "scale_factor": 1.0,
        }

        print("Loading Step-3 representative model...")
        models["Step3_MFA"] = {
            "model": ModelFactory.create_model("stepfun-ai/step3"),
            "name": "Step-3, 8K-32K",
            "color": "red",
            "marker": "*",
            "scale_factor": 1.0,
        }
        self.models = models

    def create_attention_operators(self) -> Dict[str, Any]:
        """Create attention operators for analysis using real model configurations."""
        operators = {}

        self.models["DSv3_MLA"]["model"].init_model(kv_cache_precision="fp8")
        # DeepSeek V3 MLA configuration (from real model config)
        operators["DSv3_MLA"] = {
            "operator": self.models["DSv3_MLA"]["model"].attn_op,
            "name": "DSv3, 8K-32K",
            "color": "blue",
            "marker": "o",
            "num_layers": 61,  # From actual config
        }

        self.models["Qwen3_MoE"]["model"].init_model(kv_cache_precision="fp8")
        # Qwen3 MoE GQA configuration (estimated from similar models)
        operators["Qwen3_MoE"] = {
            "operator": self.models["Qwen3_MoE"]["model"].attn_op,
            "name": "Qwen3 MoE, 8K-32K",
            "color": "blue",
            "marker": "o",
            "num_layers": 94,
        }

        self.models["Step3_MFA"]["model"].init_model(kv_cache_precision="fp8")
        # Step-3 MFA configuration (standard MHA)
        operators["Step3_MFA"] = {
            "operator": self.models["Step3_MFA"]["model"].attn_op,
            "name": "Step-3, 8K-32K",
            "color": "red",
            "marker": "*",
            "num_layers": 61,
        }

        return operators

    def _get_reference_values(self) -> Dict[str, Dict[int, Dict[str, float]]]:
        """Get reference values from the tables for comparison."""
        return {
            "DSv3_MLA": {
                8192: {"memory_bytes": 2.88e8, "compute_flops": 1.47e11},
                32768: {"memory_bytes": 1.15e9, "compute_flops": 5.89e11},
            },
            "Qwen3_MoE": {
                8192: {"memory_bytes": 7.89e8, "compute_flops": 2.52e10},
                32768: {"memory_bytes": 3.15e9, "compute_flops": 1.01e11},
            },
            "Step3_MFA": {
                8192: {"memory_bytes": 2.56e8, "compute_flops": 3.27e10},
                32768: {"memory_bytes": 1.02e9, "compute_flops": 1.31e11},
            },
        }

    def compute_with_full_model_formulas(self):
        """Compute values using operators and compare with reference."""
        print("=== Computing with Operators (All Layers) ===")
        print()

        ref_values = self._get_reference_values()

        print("Verifying operator calculations against reference values:")
        print()
        print(
            f"{'Model':<12} {'Context':<8} {'Op Memory (MB)':<15} {'Ref Memory (MB)':<16} {'Op FLOPs (G)':<13} {'Ref FLOPs (G)':<14} {'Mem Match':<10} {'FLOP Match':<11}"
        )
        print("-" * 120)

        for model_name, model_info in self.models.items():
            # print(model_name, model_info)
            for context_len in [8192, 32768]:
                attn_mem, attn_flops = model_info["model"].compute_attention_mem_flops(
                    kv_lens=context_len,
                    batch_size=1,
                    sequence_length=1,
                    kv_cache_precision="fp8",
                )
                total_memory_movement = attn_mem
                total_flops = attn_flops

                # Get reference values
                ref_data = ref_values[model_name][context_len]
                ref_memory = ref_data["memory_bytes"]
                ref_flops = ref_data["compute_flops"]

                # Check matches (within 10% tolerance)
                memory_match = (
                    "✓"
                    if abs(total_memory_movement - ref_memory) / ref_memory < 0.1
                    else "✗"
                )
                flop_match = (
                    "✓" if abs(total_flops - ref_flops) / ref_flops < 0.1 else "✗"
                )

                print(
                    f"{model_name:<12} {context_len:<8} {total_memory_movement/1e6:<15.1f} "
                    f"{ref_memory/1e6:<16.1f} {total_flops/1e9:<13.1f} {ref_flops/1e9:<14.1f} "
                    f"{memory_match:<10} {flop_match:<11}"
                )

        print()

    def analyze_attention_performance(
        self, context_lengths: List[int] = None
    ) -> Dict[str, Any]:
        """Analyze attention performance using operators and reference values."""
        if context_lengths is None:
            context_lengths = [8192, 32768]  # 8K to 32K

        operators = self.create_attention_operators()
        reference_values = self._get_reference_values()
        results = {}

        for model_name, op_info in operators.items():
            operator = op_info["operator"]
            num_layers = op_info["num_layers"]

            results[model_name] = {
                "model_info": op_info,
                "context_lengths": context_lengths,
                "compute_gflops": [],
                "memory_access_gb": [],
                "arithmetic_intensity": [],
            }

            print(f"Analyzing {op_info['name']} using {operator.__class__.__name__}...")

            for context_len in context_lengths:
                try:
                    # Always use reference values for consistency with the original figure
                    # if model_name in reference_values and context_len in reference_values[model_name]:
                    # if False:
                    #     ref_data = reference_values[model_name][context_len]
                    #     attention_flops = ref_data['compute_flops']
                    #     memory_bytes = ref_data['memory_bytes']
                    #     gflops = attention_flops / 1e9
                    #     memory_gb = memory_bytes / 1e9

                    #     print(f"  Using reference values for {op_info['name']} at {context_len}: "
                    #           f"{gflops:.1f} GFLOPS, {memory_gb:.2f} GB")
                    # else:
                    if True:
                        # For other context lengths, use operator calculations but scale appropriately
                        operator.set_shape(
                            batch_size=1, sequence_length=1, kv_lens=context_len
                        )

                        kv_cache_bytes = (
                            operator.compute_memory_access_bytes() * num_layers
                        )

                        total_flops = operator.compute_flops() * num_layers

                        gflops = total_flops / 1e9
                        memory_gb = kv_cache_bytes / 1e9

                    # Calculate arithmetic intensity
                    ai = total_flops / kv_cache_bytes
                    # ai = (attention_flops if 'attention_flops' in locals() else total_flops) / (memory_bytes if 'memory_bytes' in locals() else kv_cache_bytes) if (memory_bytes if 'memory_bytes' in locals() else kv_cache_bytes) > 0 else 0

                    results[model_name]["compute_gflops"].append(gflops)
                    results[model_name]["memory_access_gb"].append(memory_gb)
                    results[model_name]["arithmetic_intensity"].append(ai)

                except Exception as e:
                    print(
                        f"Error analyzing {op_info['name']} at context {context_len}: {e}"
                    )
                    # Add zero values to maintain list consistency
                    results[model_name]["compute_gflops"].append(0)
                    results[model_name]["memory_access_gb"].append(0)
                    results[model_name]["arithmetic_intensity"].append(0)

        return results

    def calculate_roofline_data(
        self, hardware_name: str, precision: str = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Calculate roofline data for given hardware with optimal precision."""
        if hardware_name not in self.hardware_specs:
            raise ValueError(f"Hardware {hardware_name} not found")

        hw_spec = self.hardware_specs[hardware_name]

        # Use hardware-specific optimal precision if not specified
        if precision is None:
            if hardware_name in ["H100", "H20"]:
                precision = "fp8_tensor"  # Use FP8 for H100 and H20
            elif hardware_name == "910B":
                precision = "bf16_tensor"  # Use INT8 for Ascend 910B
            else:
                precision = "bf16_tensor"  # Default for A800

        # Get peak performance in GFLOPS
        if precision in hw_spec.peak_flops:
            peak_gflops = (
                hw_spec.peak_flops[precision] * 1000
            )  # Convert TFLOPS to GFLOPS
        else:
            # Fallback to available precisions
            available_precisions = [
                "fp8_tensor",
                "bf16_tensor",
                "fp16_tensor",
                "int8_tensor",
                "fp32",
            ]
            for fallback_precision in available_precisions:
                if fallback_precision in hw_spec.peak_flops:
                    peak_gflops = hw_spec.peak_flops[fallback_precision] * 1000
                    print(
                        f"Using fallback precision {fallback_precision} for {hardware_name}"
                    )
                    break
            else:
                peak_gflops = 100 * 1000  # Last resort default

        # Memory bandwidth in GB/s
        memory_bw = hw_spec.memory_bandwidth_gbps

        gbmem_range = np.linspace(0, 4, 100)
        gflops = gbmem_range / memory_bw * peak_gflops
        return gbmem_range, gflops

    def plot_attention_roofline(self, save_path: str = "attention_roofline_figure.png"):
        """Create the attention roofline plot matching the original figure."""
        # Analyze attention performance
        results = self.analyze_attention_performance()

        # Create the plot
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))

        # Plot hardware rooflines

        snscolors = sns.color_palette("husl", 4)
        hardware_keys = ["H100", "A800", "H20", "910B"]
        hardware_colors = {key: color for key, color in zip(hardware_keys, snscolors)}

        for hw_name, color in hardware_colors.items():
            ai_range, roofline = self.calculate_roofline_data(hw_name)
            ax.plot(
                ai_range,
                roofline,
                "--",
                color=color,
                alpha=0.7,
                label=hw_name,
                linewidth=1.5,
            )

        # Plot attention mechanisms from real models
        for model_name, data in results.items():
            if "model_info" not in data:
                continue

            model_info = data["model_info"]
            memory_gb = data["memory_access_gb"]
            compute_gflops = data["compute_gflops"]

            # Skip if no valid data
            if not memory_gb or all(x == 0 for x in memory_gb):
                continue

            # Apply scaling to match original figure characteristics
            scale_factor = model_info.get("scale_factor", 1)
            scaled_compute = [gf * scale_factor for gf in compute_gflops]

            # Keep in GFLOPS for y-axis (to match original figure)
            compute_gflops_plot = scaled_compute

            # Plot the trajectory from 8K to 32K context
            if len(memory_gb) > 1:
                ax.plot(
                    memory_gb,
                    compute_gflops_plot,
                    "-",
                    color=model_info["color"],
                    alpha=0.7,
                    linewidth=2,
                )

            # Plot individual points
            markersize = 12 if model_info["marker"] == "*" else 8
            ax.scatter(
                memory_gb,
                compute_gflops_plot,
                color=model_info["color"],
                marker=model_info["marker"],
                s=markersize**2,
                label=model_info["name"],
                zorder=5,
                alpha=0.8,
            )

        # Customize the plot to match the original figure
        ax.set_xlabel("Memory access (GB)", fontsize=12)
        ax.set_ylabel("Compute (GFLOPS)", fontsize=12)
        ax.set_xlim(0, 3.5)
        ax.set_ylim(0, 600)

        # Add grid
        ax.grid(True, alpha=0.3)

        # Add legend
        ax.legend(loc="upper left", fontsize=10)

        # Add title
        ax.set_title(
            "Compute and Memory Access of Different Attention Designs During Decoding",
            fontsize=14,
            pad=20,
        )

        # Tight layout
        plt.tight_layout()

        # Save the figure
        plt.savefig(save_path, dpi=300, bbox_inches="tight")
        print(f"Figure saved to {save_path}")

        return fig, ax


def main():
    """Main function to reproduce the attention roofline figure."""
    print("Reproducing Attention Roofline Figure...")
    print("=" * 50)

    # Create analyzer
    analyzer = AttentionRooflineAnalyzer()

    # Compute with operators and compare with reference
    analyzer.compute_with_full_model_formulas()

    # Create and save the plot
    fig, ax = analyzer.plot_attention_roofline()

    # Show the plot
    plt.show()


if __name__ == "__main__":
    main()
